*.pyc
src/media
src/static
src/r_static
src/_dependencies/unpacked

# tests related files
src/.tests_reports/.coverage
src/.tests_reports/coverage.xml
src/.tests_reports/htmlcov
pytestdebug.log
.pytest_cache
cypress/videos/*
cypress/screenshots/*
cypress.env.json

/src/dna_backend.js
*.db
.vagrant/
.project
.pydevproject
.idea
qodana.yaml
.vscode
.editorconfig
src/.coverage
.pip-cache/
backups/
src/packed.tar.gz
src/webpack-stats.json
src/webpack-stats-es5.json
src/frontend_cms/static/dist_vue/webpack-stats.json
src/frontend_cms/static/dist_vue/webpack-stats-es5.json
.DS_Store
frontend_src/bower_components/
frontend_src/node_modules/
frontend_src/gulp_helpers/
frontend_src/npm-shrinkwrap.json
frontend_src/src/scss/sprites_build/
frontend_src/src/js/plugins.min.js
frontend_src/src/maps
src/frontend_cms/static/dist/js/scripts.min.js
src/frontend_cms/static/dist/css/style.css
src/frontend_cms/static/dist/css/styleVendors.css
src/frontend_cms/static/dist/css/styleConfig.css
src/frontend_cms/static/dist/bundles
src/frontend_cms/static/dist/js/head.min.js
src/frontend_cms/static/dist_vue
src/frontend_cms/static/**/*.objx
src/frontend_cms/static/src_webgl/**/app.js
src/frontend_cms/static/src_webgl/**/app_old.js
src/frontend_cms/static/dist/css/cplus.css
src/frontend_cms/static/dist/css/row-configurator.css
src/frontend_cms/static/dist/css/ds.css
src/frontend_cms/static/dist/css/*

frontend_cms/maps/src_webgl/ivy/screen_tool.js.map
src/accounting/data/
frontend_src/gulp_helpers/gulp-wrap/
src/frontend_src/static/dist/js/public/**
frontend_src/src_webgl/ivy/renderer/methods/raytrace/deffered-raytrace/node_modules/**
frontend_src/src_webgl/deffered-raytrace/node_modules/**
src/frontend_src/static/dist/js/head.min.js
src/frontend_src/src/js/public/dist/js/maps/**

frontend_src/src_webgl/ivy/renderer/methods/raytrace/deffered-raytrace/node_modules/
frontend_src/src_webgl/ivy/renderer/methods/raytrace/node_modules/**
frontend_src/src_webgl/ivy/renderer/methods/raytrace/node_modules/
frontend_src/maps/src_webgl/ivy/app.js.map
frontend_src/src_webgl/ivy/presets/bundle/presets.js
frontend_src/src/js/public/dist/js/maps/plugins.min.js.map

src/frontend_cms/static/dist/js/public/dist/js/maps/**

frontend_src/src_designer/core/node_modules/**
frontend_src/src_designer/ui-bootstrap/node_modules/**
frontend_src/src_designer/ui/node_modules/**
frontend_src/src_designer/ui/.quasar
frontend_src/src_designer/ui/dist/
src/frontend_cms/static/src_webgl/ivy/*.js
node_modules/


\.vscode/
src/frontend_cms/static/ivy.objx
env-3/**/*
env-icu/**/*

# env files for pipenv
Pipfile
Pipfile.lock

.Python

.python-version
.env
/venv/
.venv/
.venv36/
.venv37/
/frontend_src/src_designer/src/ui/.quasar
/frontend_src/src_designer/package-lock.json
.venv*/
/venv2/

.ipynb_checkpoints
*.ipynb

# local development
shared/media
shared/static_files
shared/postgresql
shared/mailhog
shared/headless_files
shared/minio
shared/tmp

# docker
docker-compose.override.yml

*/venv/

# celery
src/celerybeat-schedule
src/celerybeat.pid

# Phrase
.phrase.yml

# nvm
*/.nvmrc
*/.npmrc
.aider*
/nuxt3/CLAUDE.md
