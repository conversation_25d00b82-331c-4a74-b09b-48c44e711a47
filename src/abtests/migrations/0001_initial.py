# -*- coding: utf-8 -*-
# Generated by Django 1.11.24 on 2020-02-17 21:54
from __future__ import unicode_literals

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='ABTest',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('codename', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('active', models.BooleanField(default=False)),
                ('start_date', models.DateField(blank=True, null=True)),
                ('end_date', models.DateField(blank=True, null=True)),
                (
                    'rate_split',
                    models.IntegerField(
                        default=50, help_text='0-100 rate for variation'
                    ),
                ),
                (
                    'traffic_type',
                    models.IntegerField(
                        choices=[
                            (0, 'All'),
                            (1, 'Mobile'),
                            (2, 'Tablet'),
                            (3, 'PC'),
                            (4, 'PC and Tablet'),
                        ]
                    ),
                ),
                (
                    'ga_custom_dimension',
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ('results_description', models.TextField(blank=True, null=True)),
                ('results_url', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('feature_flag', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'A/b test',
                'verbose_name_plural': 'A/B tests',
            },
        ),
    ]
