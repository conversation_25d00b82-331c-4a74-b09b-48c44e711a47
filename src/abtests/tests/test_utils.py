import datetime

from unittest import mock

import pytest

from freezegun import freeze_time

from abtests.utils import (
    get_variant_cookie_value_for,
    set_response_ab_test_cookie,
    should_add_ab_test,
)

TESTING_AB_TESTS_TOKEN = (
    'eyJ0eXAiOiJhYl90ZXN0c190b2tlbiIsImFsZyI6IkhTMjU2In0.'
    'eyJ0c3R2cnNuIjp0cnVlfQ.G8BCZv9b_3cjgCZxyzhkZZgdyAGqSJitpR5MJnkOLec'
)
TESTING_AB_TESTS_PAYLOAD = {'tstvrsn': True}
TESTING_AB_TESTS_TOKEN_SETTINGS = {
    'signing_key': 'super_secret',
    'algorithm': 'HS256',
    'query_parameter': 'ab_tests',
}


@pytest.fixture()
def _ab_tests_settings(settings):
    settings.AB_TESTS_TOKEN_SETTINGS = TESTING_AB_TESTS_TOKEN_SETTINGS


@pytest.fixture()
def request_mock():
    return mock.Mock(path='/some/path')


@pytest.mark.django_db
class TestShouldAddABTest:
    @pytest.mark.parametrize(
        'path,expected_value',
        [
            ('/api/v1/foo/bar', False),
            ('/api/v1/ecommerce/bar', True),
            ('/foo/bar', True),
        ],
    )
    def test_returns_value_depending_on_ignored_path(
        self,
        path,
        expected_value,
        ab_test,
    ):
        request_mock = mock.Mock(path='/api/actors/john-cleese/biography')
        should_add_test = should_add_ab_test(
            request_mock,
            ab_test,
        )
        assert not should_add_test

    def test_returns_False_when_ab_test_is_already_in_requests_session(
        self, ab_test_factory, request_mock
    ):
        code_name = 'tstck'
        request_mock.configure_mock(session={code_name: True})
        ab_test = ab_test_factory(codename=code_name)
        assert not should_add_ab_test(request_mock, ab_test)

    def test_returns_False_when_ab_test_cookie_is_already_in_requests_cookies(
        self, ab_test_factory, request_mock
    ):
        code_name = 'tstck'
        request_mock.configure_mock(session=dict(), COOKIES={code_name: True})
        ab_test = ab_test_factory(codename=code_name)
        assert not should_add_ab_test(request_mock, ab_test)

    def test_returns_True_when_ab_test_cookie_missing_in_requests_cookies(
        self, ab_test_factory, request_mock
    ):
        code_name = 'tstck'
        request_mock.configure_mock(session=dict(), COOKIES=dict())
        ab_test = ab_test_factory(codename=code_name)
        assert should_add_ab_test(request_mock, ab_test)

    def test_returns_True_when_ab_test_cookie_set_to_None_in_requests_cookies(
        self, ab_test_factory, request_mock
    ):
        code_name = 'tstck'
        request_mock.configure_mock(session=dict(), COOKIES={code_name: None})
        ab_test = ab_test_factory(codename=code_name)
        assert should_add_ab_test(request_mock, ab_test)


@pytest.mark.django_db
class TestGetVariantCookieValueFor:
    def test_returns_NOK_value_when_requests_session_contains_falsy_value_under_ab_test_codename_key(  # noqa E501
        self,
        ab_test_factory,
    ):
        code_name = 'tstck'
        request_mock = mock.Mock(session={code_name: False})
        ab_test = ab_test_factory(codename=code_name)
        assert get_variant_cookie_value_for(request_mock, ab_test) == 'nok'

    def test_returns_OK_value_when_requests_session_contains_truthy_value_under_ab_test_codename_key(  # noqa E501
        self,
        ab_test_factory,
    ):
        code_name = 'tstck'
        request_mock = mock.Mock(session={code_name: True})
        ab_test = ab_test_factory(codename=code_name)
        assert get_variant_cookie_value_for(request_mock, ab_test) == 'ok'

    def test_returns_calls_ABTest_instance_choose_variant_method_when_requests_session_missing(  # noqa E501
        self,
        ab_test_factory,
    ):
        code_name = 'tstck'
        request_mock = mock.Mock()
        del request_mock.session
        ab_test = ab_test_factory(codename=code_name)
        ab_test.choose_variant = mock.Mock(return_value=False)
        assert get_variant_cookie_value_for(request_mock, ab_test) == 'nok'


@pytest.mark.django_db
class TestSetResponseABTestCookie:
    def test_response_cookie_with_proper_values_is_set(
        self,
        settings,
        ab_test_factory,
    ):
        code_name = 'tstck'
        cookie_value = 'nok'
        cookie_domain = 'http://foo.bar/'
        request_mock = mock.Mock()
        response_mock = mock.Mock()
        ab_test = ab_test_factory(codename=code_name)
        datetime_today_mock = datetime.datetime(2019, 7, 1)

        with mock.patch(
            'abtests.utils.get_variant_cookie_value_for',
            return_value=cookie_value,
        ):
            settings.SESSION_COOKIE_DOMAIN = cookie_domain
            settings.SESSION_COOKIE_SECURE = True
            with freeze_time(datetime_today_mock):
                set_response_ab_test_cookie(
                    request_mock,
                    response_mock,
                    ab_test,
                )
        response_mock.set_cookie.assert_called_once_with(
            code_name,
            cookie_value,
            domain=cookie_domain,
            secure=True,
            expires=datetime_today_mock + datetime.timedelta(days=120),
        )

    def test_pop_ab_test_from_request_session_when_session_present(
        self,
        ab_test_factory,
    ):
        code_name = 'tstck'
        request_mock = mock.Mock()
        response_mock = mock.Mock()
        ab_test = ab_test_factory(codename=code_name)
        set_response_ab_test_cookie(
            request_mock,
            response_mock,
            ab_test,
        )
        request_mock.session.pop.assert_called_once_with(code_name, None)
