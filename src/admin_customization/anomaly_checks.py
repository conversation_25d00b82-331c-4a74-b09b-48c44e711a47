import abc
import logging

from datetime import (
    date,
    timedelta,
)
from decimal import Decimal

from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.db.models import (
    F,
    Q,
)
from django.utils import timezone

from custom.models import Countries
from invoice.choices import InvoiceStatus
from invoice.models import Invoice
from mailing.templates import (
    AnomalyDeliveredButBadStatusMail,
    AnomalyInvoiceMail,
    AnomalyItemsProductsAmountMail,
    SalesReport,
)
from orders.enums import OrderStatus
from orders.models import PaidOrders
from producers.choices import ProductStatus
from regions.models import Country

logger = logging.getLogger('cstm')


class AnomalyEmailNotification:
    def __init__(self, recipients, mail_template, mail_topic):
        self._recipients = recipients
        self._mail_template = mail_template
        self._mail_topic = mail_topic

    def notify(self, data):
        for recipient in self._recipients:
            mail = self._mail_template(
                to_address=recipient[1],
                data_html={'data': data},
                topic=self._mail_topic,
            )
            mail.send()


class AbstractAnomalyCheck(abc.ABC):
    @abc.abstractmethod
    def check(self):
        raise NotImplementedError

    @property
    def notifications(self):
        return []

    def check_and_notify(self):
        check_results = self.check()

        if check_results:
            for notification in self.notifications:
                notification.notify(check_results)
        return check_results


class OrdersInProductionCheckWithDifferentProductItems(AbstractAnomalyCheck):
    def check(self):
        from datetime import date

        from dateutil.relativedelta import relativedelta

        orders = (
            PaidOrders.objects.filter(
                paid_at__gte=(date.today() + relativedelta(months=-2))
            )
            .filter(status__in=[OrderStatus.IN_PRODUCTION, OrderStatus.TO_BE_SHIPPED])
            .order_by('-pk')
        )
        check_results = []
        sample_box_content_type = ContentType.objects.get(model='samplebox')
        for order in orders:
            furniture_items = order.items.exclude(content_type=sample_box_content_type)
            not_aborted_products = order.product_set.exclude(
                status=ProductStatus.ABORTED
            )
            if not_aborted_products.count() != furniture_items.count():
                check_results.append(
                    {
                        'order': order,
                        'items': furniture_items.count(),
                        'production_items': not_aborted_products.count(),
                    }
                )
        return check_results

    @property
    def notifications(self):
        return [
            AnomalyEmailNotification(
                recipients=settings.ANOMALY_RECIPIENTS,
                mail_template=AnomalyItemsProductsAmountMail,
                mail_topic='Product items count != Order items count',
            )
        ]


class DeliveredButBadStatusCheck(AbstractAnomalyCheck):
    def check(self):
        return PaidOrders.objects.order_by('-id')[:10]

    @property
    def notifications(self):
        return [
            AnomalyEmailNotification(
                recipients=settings.ADMINS,
                mail_template=AnomalyDeliveredButBadStatusMail,
                mail_topic='Anomaly Report: Delivered but in bad status',
            )
        ]


class SalesReportCheck(AbstractAnomalyCheck):
    def check(self):
        now = timezone.now()
        date_from = now - timedelta(days=1)
        context = {
            'countries': {},
            'sales': [],
            'items': 0,
            'promo_amount': 0,
            'report_date': now.strftime('%Y-%m-%d'),
            'total_price': 0,
        }
        for order in PaidOrders.objects.filter(paid_at__gte=date_from):
            country = (
                order.invoice_country
                if order.different_billing_address
                else order.country
            )
            if country not in context['countries']:
                context['countries'][country] = 0
            promo_gross = Decimal(order.get_base_promo_amount())
            total_value_gross = Decimal(order.get_base_total_value())
            context['countries'][country] += 1
            context['promo_amount'] += promo_gross
            context['total_price'] += total_value_gross
            items = []
            for item in order.items.all():
                items.append(
                    {
                        'name': item.order_item.default_title,
                        'price_eur': Decimal(item.price),
                    }
                )
            context['items'] += len(items)
        return context

    @property
    def notifications(self):
        return [
            AnomalyEmailNotification(
                recipients=settings.ADMINS,
                mail_template=SalesReport,
                mail_topic='Sales report',
            )
        ]


class InvoiceNumbering(AbstractAnomalyCheck):
    def find_wholes_in_numeration(self):
        errors = {}

        today = date.today()
        invoices = Invoice.objects.filter(
            issued_at__year=today.year,
            issued_at__month=today.month,
        ).order_by('id')

        normal_invoices = (
            invoices.filter(status=InvoiceStatus.ENABLED)
            .exclude(pretty_id__startswith='RV')
            .exclude(pretty_id__startswith='INV')
            .exclude(pretty_id='')
        )

        normal_anomalies = self._detect_anomaly(
            normal_invoices,
            remove_to_sort='',
            split_element=0,
        )
        if normal_anomalies:
            errors['normal'] = normal_anomalies

        correcting_invoices = (
            invoices.filter(status=InvoiceStatus.CORRECTING)
            .exclude(pretty_id__startswith='RV')
            .exclude(pretty_id__startswith='INV')
            .exclude(pretty_id='')
        )

        normal_fks_anomalies = self._detect_anomaly(
            correcting_invoices,
            remove_to_sort='FKS',
            split_element=0,
        )
        if normal_fks_anomalies:
            errors['normal_fks'] = normal_fks_anomalies

        region_vat_countries = (
            Country.objects.filter(
                Q(region_vat=True) | Q(name=Countries.united_kingdom.name)
            )
            .order_by('name')
            .values_list('name', flat=True)
        )

        for country in region_vat_countries:
            normal_invoices_by_country = (
                invoices.filter(status=InvoiceStatus.ENABLED)
                .filter(order__country=country)
                .filter(Q(pretty_id__contains='RV') | Q(pretty_id__contains='INV'))
                .order_by('id')
            )

            normal_anomalies_by_country = self._detect_anomaly(
                normal_invoices_by_country,
                remove_to_sort='',
                split_element=1,
            )
            if normal_anomalies_by_country:
                errors[country] = normal_anomalies_by_country

            correcting_invoices_by_country = (
                invoices.filter(status=InvoiceStatus.CORRECTING)
                .filter(order__country=country)
                .filter(Q(pretty_id__contains='RV') | Q(pretty_id__contains='INV'))
                .order_by('id')
            )

            correction_anomalies_by_country = self._detect_anomaly(
                correcting_invoices_by_country,
                remove_to_sort='FKS',
                split_element=1,
            )
            if correction_anomalies_by_country:
                errors[f'Correction {country}'] = correction_anomalies_by_country
        return errors

    def _detect_anomaly(self, invoice_queryset, remove_to_sort, split_element):
        errors = []

        invoice_queryset = list(
            invoice_queryset.filter(cached_delivery_address_country=F('order__country'))
        )

        if len(invoice_queryset) == 0:
            return []

        invoice_queryset = sorted(
            invoice_queryset,
            key=lambda x: int(
                x.pretty_id.split('/')[split_element].replace(remove_to_sort, '')
            ),
        )
        diff = 0
        start = int(
            invoice_queryset[0]
            .pretty_id.split('/')[split_element]
            .replace(remove_to_sort, '')
            if invoice_queryset
            else 1
        )
        if start != 1:
            errors.append([start - 1, '', invoice_queryset[0].pretty_id])

        prev = None
        for num, inv in enumerate(invoice_queryset, start=start):
            i_num = int(
                inv.pretty_id.split('/')[split_element].replace(remove_to_sort, '')
            )
            if num != i_num:
                current_diff = num - i_num
                if current_diff != diff:
                    errors.append((diff - current_diff, prev.pretty_id, inv.pretty_id))
                    diff = current_diff
            prev = inv
        return errors

    def check(self):
        return self.find_wholes_in_numeration()

    @property
    def notifications(self):
        return [
            AnomalyEmailNotification(
                recipients=settings.ACCOUNTING_RECIPIENTS,
                mail_template=AnomalyInvoiceMail,
                mail_topic='Invoice numeration anomalies detected',
            )
        ]
