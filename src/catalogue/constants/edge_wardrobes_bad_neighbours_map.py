"""For the edge wardrobe color mapping, the neighboring rules had to be modified.
Certain colors necessitated a more lenient approach due to the impracticality of
enforcing strict rules on the board.
"""

from catalogue.constants.color_groupings import (
    BLACK_COLORS,
    GREY_COLORS,
    STRICT_WHITE_COLORS,
)
from custom.enums import (
    ShelfType,
    Type13Color,
)

EDGE_WARDROBES_BAD_NEIGHBOURS_MAP = {
    Type13Color.WHITE: STRICT_WHITE_COLORS,
    Type13Color.SAND: {(ShelfType.TYPE13, Type13Color.SAND)},
    Type13Color.MUSTARD_YELLOW: {
        (ShelfType.TYPE13, Type13Color.BEIGE),
        (ShelfType.TYPE13, Type13Color.MUSTARD_YELLOW),
    },
    Type13Color.GRAY: GREY_COLORS,
    Type13Color.DARK_GRAY: GREY_COLORS,
    Type13Color.WHITE_PLYWOOD: STRICT_WHITE_COLORS,
    Type13Color.GRAY_PLYWOOD: GREY_COLORS,
    Type13Color.BLACK_PLYWOOD: BLACK_COLORS,
    Type13Color.CLAY_BROWN: {(ShelfType.TYPE13, Type13Color.CLAY_BROWN)},
    Type13Color.OLIVE_GREEN: {(ShelfType.TYPE13, Type13Color.OLIVE_GREEN)},
    Type13Color.BEIGE: {
        (ShelfType.TYPE13, Type13Color.BEIGE),
        (ShelfType.TYPE13, Type13Color.MUSTARD_YELLOW),
    },
    Type13Color.BLACK: BLACK_COLORS,
}
