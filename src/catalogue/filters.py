from django.db.models import Q

import django_filters

from catalogue.enums import FurnitureAttributesEnum
from catalogue.models import CatalogueEntry
from catalogue.validators import validate_shelf_types
from custom.filters import CamelCaseFilterSetMetaclass
from gallery.enums import (
    CollectiveFurnitureCategory,
    FurnitureCategory,
)

EMPTY_VALUES = ([], (), {}, '', None)


class ShelfTypesFilter(django_filters.Filter):
    """Filter a list of requested shelf types. Object has to have any of them."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._field = self.field_class(
            label=self.label,
            validators=[validate_shelf_types],
            **self.extra.copy(),
        )

    def filter(self, qs, value):
        if value in EMPTY_VALUES:
            return qs
        value_list = [int(v) for v in value.split(',')]
        return qs.filter(shelf_type__in=value_list)


class ExclusiveFilter(django_filters.Filter):
    """Filter a list of attributes. Object has to have all of them."""

    def filter(self, qs, value):
        if value in EMPTY_VALUES:
            return qs
        value_list = value.split(',')
        for filter_value in value_list:
            qs = qs.filter(attributes__name__in=[filter_value])
        return qs


class InclusiveFilter(django_filters.Filter):
    """Filter a list of attributes. Object has to have any of them."""

    def filter(self, qs, value):
        if value in EMPTY_VALUES:
            return qs
        value_list = value.split(',')
        return qs.filter(attributes__name__in=value_list)


class FeaturesFilter(django_filters.Filter):
    def filter(self, qs, value):
        if value in EMPTY_VALUES:
            return qs

        values = value.split(',')
        if (
            FurnitureAttributesEnum.ARMREST in values
            and FurnitureAttributesEnum.OPENEND in values
        ):
            values.remove(FurnitureAttributesEnum.ARMREST)
            values.remove(FurnitureAttributesEnum.OPENEND)
            values.append(FurnitureAttributesEnum.SEMI_OPENEND)

        for filter_value in values:
            qs = qs.filter(attributes__name__in=[filter_value])
        return qs


class DimensionsFilter(django_filters.Filter):
    """Filter used for filtering height, width and depth.

    Available filter options are:
    gt, lt and value range with '-' (inclusive on both sides), which can be used
    together, comma separated. Values should be given in cm.
    F.ex.: '?height=lt100,200-300,gt350'
    """

    def filter(self, qs, value):
        if value in EMPTY_VALUES:
            return qs
        value_list = value.split(',')
        dimension = self.field_name
        query = Q()
        for filter_value in value_list:
            if '-' in filter_value:
                min_value, max_value = filter_value.split('-')
                try:
                    min_value = int(min_value)
                    max_value = int(max_value)
                except ValueError:
                    return qs
                query |= Q(
                    Q(**{f'{dimension}__gte': min_value * 10})
                    & Q(**{f'{dimension}__lte': max_value * 10}),
                )
            elif filter_value.startswith('gt'):
                try:
                    min_value = int(filter_value.removeprefix('gt'))
                except ValueError:
                    return qs
                query |= Q(**{f'{dimension}__gt': min_value * 10})
            elif filter_value.startswith('lt'):
                try:
                    max_value = int(filter_value.removeprefix('lt'))
                except ValueError:
                    return qs
                query |= Q(**{f'{dimension}__lt': max_value * 10})
            else:
                try:
                    value = int(filter_value)
                except ValueError:
                    return qs
                query |= Q(**{f'{dimension}__exact': value * 10})

        qs = qs.filter(query)
        return qs


class ColorTypeFilter(django_filters.Filter):
    """Pass values like `01_0` for shelf_type=01 and material 1 or `24_3`"""

    def filter(self, qs, value):
        if value in EMPTY_VALUES:
            return qs
        value_list = value.split(',')
        query = Q()
        for filter_value in value_list:
            if '_' not in filter_value:
                continue
            material, shelf_type = filter_value.split('_')
            query |= Q(shelf_type=shelf_type, material=material)
        return qs.filter(query)


class CollectiveCategoryFilter(django_filters.Filter):
    def filter(self, qs, value):
        if value in EMPTY_VALUES:
            return qs
        try:
            collective_category = CollectiveFurnitureCategory(value)
        except ValueError:
            return qs
        return qs.filter(
            category__in=collective_category.get_furniture_categories(),
        )


class CategoriesFilter(django_filters.Filter):
    def filter(self, qs, value):
        if value in EMPTY_VALUES:
            return qs
        categories = [
            FurnitureCategory(category)
            for category in value.split(',')
            if category in FurnitureCategory.values
        ]
        return qs.filter(category__in=categories)


class CatalogueFilterSet(
    django_filters.FilterSet,
    metaclass=CamelCaseFilterSetMetaclass,
):
    shelf_type = ShelfTypesFilter()
    min_width = django_filters.NumberFilter(field_name='width', lookup_expr='gte')
    max_width = django_filters.NumberFilter(field_name='width', lookup_expr='lte')
    width = DimensionsFilter()
    height = DimensionsFilter()
    depth = DimensionsFilter()
    features = FeaturesFilter()
    colors = InclusiveFilter()
    special = InclusiveFilter()
    materials = InclusiveFilter()
    types = InclusiveFilter()
    style = InclusiveFilter()
    direction = InclusiveFilter()
    # It is not a ColorEnum, it is a sotty material corduroy/wool material
    material = InclusiveFilter()
    color_type = ColorTypeFilter()
    collective_category = CollectiveCategoryFilter()
    # does not influence ordering, in contrast to "category" filter
    categories = CategoriesFilter()

    class Meta:
        model = CatalogueEntry
        fields = ['category', 'material']
