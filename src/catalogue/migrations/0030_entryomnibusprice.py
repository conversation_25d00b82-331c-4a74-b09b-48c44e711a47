# Generated by Django 4.1.9 on 2024-06-07 16:03

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('regions', '0010_remove_region_rate_for_default_region'),
        ('catalogue', '0029_alter_catalogueentry_shelf_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='EntryOmnibusPrice',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('price', models.DecimalField(decimal_places=0, max_digits=6)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                (
                    'entry',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='omnibus_prices',
                        to='catalogue.catalogueentry',
                    ),
                ),
                (
                    'region',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to='regions.region'
                    ),
                ),
            ],
            options={
                'unique_together': {('entry', 'region')},
            },
        ),
    ]
