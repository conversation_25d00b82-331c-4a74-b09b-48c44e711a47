from typing import Optional

from catalogue.constants import (
    BAD_NEIGHBOURS_MAP,
    SAME_COLORS_MAP,
)
from catalogue.constants.edge_wardrobes_bad_neighbours_map import (
    EDGE_WARDROBES_BAD_NEIGHBOURS_MAP,
)
from catalogue.constants.wardrobes_bad_neighbours_map import (
    WARDROBES_BAD_NEIGHBOURS_MAP,
)
from catalogue.enums import ColorGroup
from catalogue.models import CatalogueEntry
from catalogue.services.automatic_merchandising.catalogue_stats import CategoryStats
from custom.enums import (
    ColorEnum,
    ShelfType,
    Type01Color,
    Type02Color,
    Type13Color,
)
from gallery.enums import FurnitureCategory


class ColorService:
    def __init__(self, category: Optional[FurnitureCategory] = None):
        self.category = category
        self.available_shelf_types = self._get_available_shelf_types()

    def _get_color_group_map_for_wardrobes(
        self,
    ) -> dict[ColorGroup, set[tuple[ShelfType, ColorEnum]]]:
        """Gets the color group map for wardrobes.

        Dark grey colored is moved from group "nude" to group "mild" - to make more
        "mild" choices for wardrobes.
        """
        color_map = self._get_color_group_map()
        color_map[ColorGroup.NUDE] -= {(ShelfType.TYPE13, Type13Color.DARK_GRAY)}
        color_map[ColorGroup.MILD] |= {(ShelfType.TYPE13, Type13Color.DARK_GRAY)}
        return color_map

    @property
    def edge_color_group_map(
        self,
    ) -> dict[ColorGroup, set[tuple[ShelfType, ColorEnum]]]:
        color_map = self._get_color_group_map()
        color_map[ColorGroup.NUDE] -= {(ShelfType.TYPE13, Type13Color.BEIGE)}
        color_map[ColorGroup.MILD] |= {(ShelfType.TYPE13, Type13Color.BEIGE)}
        return color_map

    def _get_color_group_map(
        self,
    ) -> dict[ColorGroup, set[tuple[ShelfType, ColorEnum]]]:
        """Gets the color group map for the given category or for all shelves.

        Returns:
            a dictionary in the following format:
            {
                ColorGroup.NUDE: set[tuple[ShelfType, ColorEnum]],
                ColorGroup.MILD: set[tuple[ShelfType, ColorEnum]],
                ColorGroup.BOLD: set[tuple[ShelfType, ColorEnum]],
            }
        The set contains colors available for the specified category or all boards.
        """
        color_group_map = {
            ColorGroup.NUDE: set(),
            ColorGroup.MILD: set(),
            ColorGroup.BOLD: set(),
        }
        for shelf_type in self.available_shelf_types:
            color_group_map[ColorGroup.NUDE] |= {
                (shelf_type, color)
                for color in ShelfType(shelf_type).colors.get_nude_colors()
            }
            color_group_map[ColorGroup.MILD] |= {
                (shelf_type, color)
                for color in ShelfType(shelf_type).colors.get_mild_colors()
            }
            color_group_map[ColorGroup.BOLD] |= {
                (shelf_type, color)
                for color in ShelfType(shelf_type).colors.get_bold_colors()
            }
        if self.category in FurnitureCategory.get_desk_like_choices():
            for color_group in color_group_map:
                color_group_map[color_group] -= self.colors_not_in_desk[color_group]
        return color_group_map

    @property
    def color_group_map(self) -> dict[ColorGroup, set[tuple[ShelfType, ColorEnum]]]:
        return (
            self._get_color_group_map_for_wardrobes()
            if self.category == FurnitureCategory.WARDROBE
            else self._get_color_group_map()
        )

    def _get_available_shelf_types(self) -> set[ShelfType]:
        return (
            CategoryStats(self.category).available_shelf_types
            if self.category
            else ShelfType.values()
        )

    @property
    def all_colors_available(self) -> set[tuple[ShelfType, ColorEnum]]:
        return (
            self.color_group_map[ColorGroup.NUDE]
            | self.color_group_map[ColorGroup.MILD]
            | self.color_group_map[ColorGroup.BOLD]
        )

    @property
    def colors_not_in_desk(self) -> dict[ColorGroup, set[tuple[ShelfType, ColorEnum]]]:
        """Gets colors that are not available for the desk and dressing table
        category."""
        return {
            ColorGroup.NUDE: {
                (ShelfType.TYPE02, Type02Color.COTTON),
                (ShelfType.TYPE02, Type02Color.STONE_GRAY),
                (ShelfType.TYPE02, Type02Color.WALNUT),
            },
            ColorGroup.MILD: {
                (ShelfType.TYPE02, Type02Color.SKY_BLUE),
                (ShelfType.TYPE02, Type02Color.MUSTARD_YELLOW),
                (ShelfType.TYPE02, Type02Color.REISINGERS_PINK),
                (ShelfType.TYPE02, Type02Color.SAGE_GREEN),
            },
            ColorGroup.BOLD: {
                (ShelfType.TYPE01, Type01Color.BLUE),
                (ShelfType.TYPE01, Type01Color.DARK_BROWN),
                (ShelfType.TYPE02, Type02Color.TERRACOTTA),
                (ShelfType.TYPE02, Type02Color.MIDNIGHT_BLUE),
                (ShelfType.TYPE02, Type02Color.MATTE_BLACK),
                (ShelfType.TYPE02, Type02Color.BURGUNDY),
            },
        }


class ColorsGetter:
    def __init__(self, shelf_type: ShelfType, material: ColorEnum):
        self.shelf_type = shelf_type
        self.material = material

    def get_bad_neighbours(
        self,
        category: Optional[FurnitureCategory] = None,
    ) -> set[tuple]:
        """Gets bad neighbours for the given color from the given category.

        Bad neighbours are colors that are not recommended to be placed next to
        the given color. They are defined in the BAD_NEIGHBOURS_MAP.
        """
        bad_neighbours_map = self.get_bad_neighbours_map(category)
        return {
            color
            for color in bad_neighbours_map[self.shelf_type][self.material]
            if color in ColorService(category).all_colors_available
        }

    def get_bad_neighbours_for_edge_wardrobes(self):
        return {
            color
            for color in EDGE_WARDROBES_BAD_NEIGHBOURS_MAP[self.material]
            if color in ColorService(FurnitureCategory.WARDROBE).all_colors_available
        }

    def get_same_colors(
        self,
        category: Optional[FurnitureCategory] = None,
    ) -> set[tuple]:
        """Returns the same color from the SAME_COLORS_MAP (from different type or
        with plywood edges) from the given category.
        They prevent color repetition within the same group.
        """
        return {
            color
            for color in SAME_COLORS_MAP[self.shelf_type][self.material]
            if color in ColorService(category).all_colors_available
        }

    @staticmethod
    def get_bad_neighbours_map(
        category: Optional[FurnitureCategory],
    ) -> dict[ShelfType, dict[ColorEnum, set[tuple]]]:
        return (
            BAD_NEIGHBOURS_MAP
            if category != FurnitureCategory.WARDROBE
            else WARDROBES_BAD_NEIGHBOURS_MAP
        )


class EntryColorGroupGetter:
    def __init__(self, entry: CatalogueEntry):
        self.entry = entry

    def run(self) -> ColorGroup:
        type_colors = ShelfType(self.entry.shelf_type).colors
        color_group = None
        if self.entry.material in type_colors.get_nude_colors():
            color_group = ColorGroup.NUDE
        elif self.entry.material in type_colors.get_mild_colors():
            color_group = ColorGroup.MILD
        elif self.entry.material in type_colors.get_bold_colors():
            color_group = ColorGroup.BOLD
        return color_group
