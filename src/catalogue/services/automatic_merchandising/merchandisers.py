"""Algorithms for automatic merchandising - setting the order of the furniture in our
catalogue automatically.
"""
import logging
import random

from typing import (
    Callable,
    Optional,
    Union,
)

from django.db import connection
from django.db.models import (
    Case,
    F,
    Func,
    IntegerField,
    JSONField,
    Q,
    QuerySet,
    Value,
    When,
)
from django.db.models.functions import Cast

from catalogue.constants.merchandising_config import (
    DEFAULT_ROW_LENGTH,
    MAXIMUM_FIRST_STAGE_ENTRIES_COUNT,
    MINIMAL_FIRST_STAGE_ENTRIES_COUNT,
    MIXED_CATEGORIES_FURNITURE_TYPE_WEIGHTS,
    MOSAIC_PATTERN_FREQUENCY,
    NUM_OF_ENTRIES_WITH_MAX_STRATEGIC_VALUE,
    PAGE_LENGTH,
    VENEER_TYPE13_FREQUENCY,
)
from catalogue.enums import (
    FurnitureAttributesEnum,
    ListingOrder,
    StrategyEnum,
)
from catalogue.exceptions import NoFirstEntryFoundError
from catalogue.models import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    CatalogueEntry,
)
from catalogue.paginator import CatalogueBasicPagination
from catalogue.services.automatic_merchandising.categories_filters_getters import (
    BedsideTableFiltersGetter,
    BookcaseFiltersGetter,
    ChestFiltersGetter,
    DeskFiltersGetter,
    EdgeWardrobeFiltersGetter,
    ShoerackFiltersGetter,
    SideboardFiltersGetter,
    SofaFiltersGetter,
    TvStandFiltersGetter,
    VinylStorageFiltersGetter,
    WallStorageFiltersGetter,
    WardrobeFiltersGetter,
)
from catalogue.services.automatic_merchandising.category_picker import CategoryPicker
from catalogue.services.automatic_merchandising.filters_getters import (
    AllShelvesEdgeOnlyFiltersGetter,
    AllShelvesFiltersGetter,
    CategoryFiltersGetter,
)
from catalogue.services.automatic_merchandising.overrides_order_validator import (
    validate_overrides_for_merchandising,
)
from catalogue.services.automatic_merchandising.queries import (
    SOFA_SIMPLE_ORDERING_QUERY,
)
from catalogue.services.automatic_merchandising.validators import (
    validate_edge_only_option_availability,
)
from custom.enums import (
    Furniture,
    ShelfType,
)
from gallery.enums import (
    CollectiveFurnitureCategory,
    FurnitureCategory,
)
from regions.models import Region

logger = logging.getLogger('cstm')


class RandomOrderMerchandiserMixin:
    """no business rules, strictly random order"""

    queryset: QuerySet[CatalogueEntry]
    success: bool

    def set_catalogue_in_order(self) -> None:
        # use only for small number of entries
        for entry in self.queryset.order_by('?'):
            self._set_order(entry)

        self.success = True


class SofaOrderingWithBasicRulesMixin:
    """
    randomize sofa entries order,
    so no category or color group is shared with neighbours
    """

    queryset: QuerySet[CatalogueEntry]
    success: bool

    def set_catalogue_in_order(self) -> None:
        entry_ids = self.get_entry_ids_in_target_order()
        if not entry_ids:
            self.success = False
            return

        for entry in self.get_ordered_queryset(ids=entry_ids):
            self._set_order(entry)

        self.success = True

    @staticmethod
    def get_entry_ids_in_target_order() -> list[int]:
        with connection.cursor() as cur:
            cur.execute(SOFA_SIMPLE_ORDERING_QUERY)
            return [row[0] for row in cur.fetchall()]

    def get_ordered_queryset(self, ids: list[int]) -> QuerySet[CatalogueEntry]:
        preserved = Case(
            *[When(id=id_, then=Value(position)) for position, id_ in enumerate(ids)],
            output_field=IntegerField(),
        )
        return self.queryset.filter(id__in=ids).order_by(preserved)


class BaseMerchandiser:
    """Base class for all merchandising algorithms."""

    def __init__(
        self,
        strategy: StrategyEnum = StrategyEnum.NONE,
        region: Optional[Region] = None,
        board_name: Optional[str] = None,
        edge_wardrobes_only: bool = False,
        poverty_mode: bool = False,
    ):
        self.strategy = strategy
        self.region = region
        self.poverty_mode = poverty_mode
        self.region_name = region.name if region else None
        self.country_code = region.country.code if region else None
        self.shiam_field = self._get_shiam_field()
        if edge_wardrobes_only:
            validate_edge_only_option_availability(strategy, self.region)
        self.edge_wardrobes_only = edge_wardrobes_only
        self.queryset = self._get_queryset()
        self.ordering_field = self._get_ordering_field()
        self.order = 0
        self.is_first_page = True
        self.first_stage_order = 0
        self.second_stage_order = None
        self.entries_to_update = []
        self.used_ids = set()
        self.filters_getter = None
        self.merchandising_info = ''
        self.success = False
        self.overrides = None
        if board_name:
            self._get_overrides(board_name)

    def run(self) -> None:
        self.update_ordering_field_to_none()
        self.set_catalogue_in_order()
        self.update_catalogue()

    def update_ordering_field_to_none(self):
        self.queryset.update(**{self.ordering_field: None})

    def set_catalogue_in_order(self) -> None:
        if self.overrides:
            self._set_first_stage_with_overrides()
        else:
            self._set_catalogue_first_stage()
        if self.first_stage_order < MINIMAL_FIRST_STAGE_ENTRIES_COUNT:
            return
        self.success = True
        self._set_catalogue_second_stage()
        self._set_remaining_catalogue()
        self.merchandising_info += (
            f'{str(self)} finished first stage merchandising at '
            f'{self.first_stage_order} entries. Field {self.ordering_field} '
            'has been updated.\n'
            f'Second stage merchandising has been finished at {self.second_stage_order}'
            '\n\n'
        )

    def _get_queryset(self) -> QuerySet[CatalogueEntry]:
        raise NotImplementedError

    def _get_filters_getter(self):
        raise NotImplementedError

    def _get_shiam_field(self) -> str:
        return self.strategy.get_shiam_field(self.region_name, self.country_code)

    def _get_ordering_field(self) -> str:
        raise NotImplementedError

    def _get_overrides(self, board_name: str) -> None:
        overrides = BoardManualOrder.objects.filter(
            board_name=board_name,
            published=True,
        )
        if overrides.exists():
            validate_overrides_for_merchandising(overrides)
            self.overrides = overrides

    def _set_first_entry(self) -> None:
        raise NotImplementedError

    def _set_entry_first_stage(self) -> Optional[CatalogueEntry]:
        return self._set_entry(self._get_first_stage_qs)

    def _set_entry_second_stage(self) -> Optional[CatalogueEntry]:
        return self._set_entry(self._get_second_stage_qs)

    def _set_entry(self, get_qs_function: Callable) -> None:
        raise NotImplementedError

    def _filter_original_patterns(
        self,
        queryset: QuerySet['CatalogueEntry'],
    ) -> QuerySet['CatalogueEntry']:
        """Filter the queryset based on business rules for Original shelves patterns."""
        if not queryset.filter(content_type__model=Furniture.jetty.value).exists():
            return queryset

        used_patterns = list(self.filters_getter.last_original_furniture_patterns)
        if not all(used_patterns[:DEFAULT_ROW_LENGTH]):
            return queryset

        filtered_queryset = queryset

        # Exclude pixel from first page - business requirements
        if self.order <= PAGE_LENGTH:
            filtered_queryset = filtered_queryset.exclude(
                attributes__name=FurnitureAttributesEnum.PIXEL
            )

        # Check and exclude/filter Mosaic pattern
        if FurnitureAttributesEnum.MOSAIC in used_patterns[:MOSAIC_PATTERN_FREQUENCY]:
            filtered_queryset = filtered_queryset.exclude(
                attributes__name=FurnitureAttributesEnum.MOSAIC
            )
            if not filtered_queryset.exists():
                return queryset
        else:
            if all(used_patterns[:MOSAIC_PATTERN_FREQUENCY]):
                filtered_queryset = filtered_queryset.filter(
                    attributes__name=FurnitureAttributesEnum.MOSAIC
                )
                if not filtered_queryset.exists():
                    return queryset

        return filtered_queryset

    def _filter_shelf_types(
        self,
        queryset: QuerySet['CatalogueEntry'],
    ) -> QuerySet['CatalogueEntry']:
        used_shelf_types = list(self.filters_getter.last_shelf_types)
        if (
            self.order <= PAGE_LENGTH
            or ShelfType.VENEER_TYPE13 in used_shelf_types[:VENEER_TYPE13_FREQUENCY]
        ):
            filtered_queryset = queryset.exclude(shelf_type=ShelfType.VENEER_TYPE13)
            if filtered_queryset.exists():
                return filtered_queryset

        return queryset

    def _get_first_stage_qs(self, *args) -> QuerySet[CatalogueEntry]:
        raise NotImplementedError

    def _get_second_stage_qs(self, *args) -> QuerySet[CatalogueEntry]:
        raise NotImplementedError

    def _set_catalogue_first_stage(self) -> None:
        try:
            self._set_first_entry()
        except NoFirstEntryFoundError:
            self.first_stage_order = 0
            return
        number_to_set = self.queryset.count() - 1
        for _ in range(min(number_to_set, MAXIMUM_FIRST_STAGE_ENTRIES_COUNT - 1)):
            entry = self._set_entry_first_stage()
            if not entry:
                break
        self.first_stage_order = self.order

    def _set_first_stage_with_overrides(self):
        self._set_overrides()
        self._set_first_stage_after_overrides()

    def _set_overrides(self) -> None:
        """Method for setting up the order for the entries with overrides.

        The entries are set by the order specified in BoardManualOrder model.
        No automerch rules are applied, only the entries' data is saved in
        the filtersgetter class.
        NOTE: The BoardManualOrder objects have to be ordered properly, that is from
        order 1 to the last order, without any gaps.
        """
        raise NotImplementedError

    def _set_first_stage_after_overrides(self):
        overrides_count = self.overrides.count()
        number_to_set = self.queryset.count() - overrides_count
        max_to_set = MAXIMUM_FIRST_STAGE_ENTRIES_COUNT - overrides_count
        for _ in range(min(number_to_set, max_to_set)):
            entry = self._set_entry_first_stage()

            if not entry:
                break
        self.first_stage_order = self.order

    def _set_catalogue_second_stage(self) -> None:
        number_to_set = self.queryset.count() - self.first_stage_order
        for _ in range(min(number_to_set, 960)):
            entry = self._set_entry_second_stage()
            if not entry:
                break
        self.second_stage_order = self.order

    def _get_esthetic_queryset(self, esthetic_filters: Q) -> QuerySet[CatalogueEntry]:
        return self.queryset.exclude(id__in=self.used_ids).filter(esthetic_filters)

    def _get_queryset_with_photos(
        self,
        queryset: QuerySet[CatalogueEntry],
    ) -> Optional[QuerySet[CatalogueEntry]]:
        real_life_photos_filter = self.filters_getter.get_real_life_photos_filter()
        rendered_photos_filter = self.filters_getter.get_rendered_photos_filter()
        return queryset.filter(real_life_photos_filter) or queryset.filter(
            rendered_photos_filter
        )

    def _get_queryset_with_topsellers(
        self,
        queryset: QuerySet[CatalogueEntry],
    ) -> Optional[QuerySet[CatalogueEntry]]:
        """Returns a queryset with topsellers if the count of the entries already set
        with topsellers label is less or equal to desired minimal amount specified
        in TopsellerFirstPageCount, otherwise returns an empty queryset.
        """
        topseller_filter = self.filters_getter.get_topseller_filter()
        return (
            queryset.filter(topseller_filter)
            if topseller_filter
            else CatalogueEntry.objects.none()
        )

    def _process_successful_entry_pick(
        self,
        entry: CatalogueEntry,
        use_overrides: bool = False,
        *args,
        **kwargs,
    ) -> None:
        self._set_order(entry=entry)
        self.used_ids.add(entry.id)
        self.filters_getter.update_merchandising_data_for_used_entry(
            entry,
            use_overrides=use_overrides,
        )
        if self.order == CatalogueBasicPagination.page_size:
            self.is_first_page = False

    def _set_remaining_catalogue(self) -> None:
        entries_left = self._get_remaining_entries()
        for entry in entries_left:
            self._set_order(entry=entry)

    def _set_order(self, entry: CatalogueEntry) -> None:
        setattr(entry, self.ordering_field, self.order)
        self.entries_to_update.append(entry)
        self.order += 1

    def update_catalogue(self) -> None:
        CatalogueEntry.objects.bulk_update(
            self.entries_to_update,
            fields=[self.ordering_field],
            batch_size=1000,
        )

    def _get_entry_with_strategy(self, qs: QuerySet[CatalogueEntry]) -> CatalogueEntry:
        if self.strategy != StrategyEnum.NONE:
            qs = qs.order_by(f'-{self.shiam_field}')
            qs = qs[:NUM_OF_ENTRIES_WITH_MAX_STRATEGIC_VALUE]
        return random.choice(qs)

    def _get_remaining_entries(self) -> list[CatalogueEntry]:
        qs_left = self.queryset.exclude(id__in=self.used_ids)
        if self.strategy == StrategyEnum.NONE:
            return self._get_remaining_entries_without_strategy(qs_left)
        return self._get_remaining_entries_with_strategy(qs_left)

    def _get_remaining_entries_without_strategy(
        self,
        qs_left: QuerySet[CatalogueEntry],
    ) -> list[CatalogueEntry]:
        return list(qs_left.order_by('?'))

    def _get_remaining_entries_with_strategy(
        self,
        qs_left: QuerySet[CatalogueEntry],
    ) -> list[CatalogueEntry]:
        raise NotImplementedError


class AllShelvesMerchandiser(BaseMerchandiser):
    """Class for setting up the order for 'All shelves' board."""

    def __init__(
        self,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.category_picker = CategoryPicker()
        self.filters_getter = self._get_filters_getter()

    def _get_queryset(self) -> QuerySet[CatalogueEntry]:
        if self.edge_wardrobes_only:
            return (
                CatalogueEntry.objects.select_related('content_type')
                .prefetch_related('attributes')
                .exclude(shelf_type=ShelfType.TYPE03)
            )
        return (
            CatalogueEntry.objects.select_related('content_type')
            .prefetch_related('attributes')
            .all()
        )

    def _get_filters_getter(
        self,
    ) -> Union[AllShelvesFiltersGetter, AllShelvesEdgeOnlyFiltersGetter]:
        if self.edge_wardrobes_only:
            return AllShelvesEdgeOnlyFiltersGetter()
        return AllShelvesFiltersGetter()

    def _get_ordering_field(self) -> str:
        if self.edge_wardrobes_only:
            return self.strategy.get_test_alt_order_field()
        return self.strategy.get_test_order_field(self.region_name, self.country_code)

    def _set_first_entry(self) -> None:
        esthetic_first_entry_filters = self.filters_getter.get_first_entry_filter()
        first_entry_filter = (
            esthetic_first_entry_filters & self.filters_getter.get_cheap_price_filter()
        )
        if qs := self.queryset.filter(first_entry_filter):
            entry = self._get_entry_with_strategy(qs)
            self._process_successful_entry_pick(
                entry,
                FurnitureCategory(entry.category),
            )
        elif (
            entry := self.queryset.filter(esthetic_first_entry_filters)
            .order_by('price')
            .first()
        ):
            self._process_successful_entry_pick(
                entry,
                FurnitureCategory(entry.category),
            )
            # logger.info('No cheap first entry found for all_shelves')
            self.merchandising_info += 'No cheap first entry found for all_shelves'

        else:
            raise NoFirstEntryFoundError

    def _set_entry(self, get_qs_function: Callable) -> Optional[CatalogueEntry]:
        entry = None
        category = self.category_picker.get_category_candidate()
        qs = get_qs_function(category)
        while not qs:
            self.category_picker.reject_category(category)
            if category := self.category_picker.get_category_candidate():
                qs = get_qs_function(category)
            else:
                return entry
        entry = self._get_entry_with_strategy(qs)
        self._process_successful_entry_pick(entry, category)
        return entry

    def _get_first_stage_qs(
        self,
        category: FurnitureCategory,
    ) -> QuerySet[CatalogueEntry]:
        """Class for getting queryset for the given category.

        Entries with photos are preferred, so the queryset is first filtered by both
        esthetic rules for the category and the presence of photos. If the resulting
        queryset is empty, then only aesthetic rules are applied.
        """
        esthetic_filters = self.filters_getter.get_esthetic_filters(category)

        qs = self._get_esthetic_queryset(esthetic_filters)
        qs = self._filter_shelf_types(qs)
        qs = self._filter_original_patterns(qs)

        if qs_with_promoted := qs.filter(
            self.filters_getter.get_all_shelves_promoted_filter()
        ):
            return qs_with_promoted

        qs = self._get_queryset_with_photos(qs) or qs

        if not self.is_first_page:
            return qs

        return self._get_queryset_with_topsellers(qs) or qs

    def _set_catalogue_second_stage(self) -> None:
        self.category_picker.reset_rejected_categories()
        super()._set_catalogue_second_stage()

    def _get_second_stage_qs(
        self,
        category: FurnitureCategory,
    ) -> QuerySet[CatalogueEntry]:
        second_stage_filters = self.filters_getter.get_second_stage_filters(category)
        return self.queryset.exclude(id__in=self.used_ids).filter(second_stage_filters)

    def _process_successful_entry_pick(
        self,
        entry: CatalogueEntry,
        category: FurnitureCategory,
        *args,
        **kwargs,
    ) -> None:
        super()._process_successful_entry_pick(entry, *args, **kwargs)
        self.category_picker.accept_category(category)

    def _get_remaining_entries_with_strategy(
        self,
        qs_left: QuerySet[CatalogueEntry],
    ) -> list[CatalogueEntry]:
        return list(
            qs_left.order_by(
                f'-{self.shiam_field}',
                StrategyEnum.NONE.get_order_field(),
            )
        )

    def _set_overrides(self):
        entry = None
        for override in self.overrides.order_by('order'):
            entry = override.entry
            self._process_successful_entry_pick(
                entry,
                FurnitureCategory(entry.category),
                use_overrides=True,
            )
        self.filters_getter.determine_color_group_for_last_overrides_entry(entry)

    def __str__(self) -> str:
        return f'AllShelvesMerchandiser with strategy: {self.strategy}'


class CategoryMerchandiser(BaseMerchandiser):
    """Class for setting up a category board with category."""

    def __init__(
        self,
        category: FurnitureCategory,
        **kwargs,
    ):
        self.category = category
        super().__init__(**kwargs)
        self.filters_getter = self._get_filters_getter()

    def _get_queryset(self) -> QuerySet[CatalogueEntry]:
        if self.category == FurnitureCategory.WARDROBE and self.edge_wardrobes_only:
            return CatalogueEntry.objects.filter(
                category=FurnitureCategory.WARDROBE,
                shelf_type=ShelfType.TYPE13,
            )
        return CatalogueEntry.objects.filter(category=self.category)

    def _get_ordering_field(self) -> str:
        if self.category == FurnitureCategory.WARDROBE and self.edge_wardrobes_only:
            return self.strategy.get_test_alt_category_order_field()
        return self.strategy.get_test_category_order_field(
            self.region_name,
            self.country_code,
        )

    def _get_filters_getter(self) -> CategoryFiltersGetter:
        filters_getter_map = {
            # Shelving
            FurnitureCategory.BEDSIDE_TABLE.value: BedsideTableFiltersGetter,
            FurnitureCategory.BOOKCASE.value: BookcaseFiltersGetter,
            FurnitureCategory.CHEST.value: ChestFiltersGetter,
            FurnitureCategory.DESK.value: DeskFiltersGetter,
            FurnitureCategory.DRESSING_TABLE.value: DeskFiltersGetter,
            FurnitureCategory.SHOERACK.value: ShoerackFiltersGetter,
            FurnitureCategory.SIDEBOARD.value: SideboardFiltersGetter,
            FurnitureCategory.TV_STAND.value: TvStandFiltersGetter,
            FurnitureCategory.VINYL_STORAGE.value: VinylStorageFiltersGetter,
            FurnitureCategory.WALL_STORAGE.value: WallStorageFiltersGetter,
            FurnitureCategory.WARDROBE.value: WardrobeFiltersGetter,
            # Sofas
            FurnitureCategory.TWO_SEATER.value: SofaFiltersGetter,
            FurnitureCategory.THREE_SEATER.value: SofaFiltersGetter,
            FurnitureCategory.FOUR_PLUS_SEATER.value: SofaFiltersGetter,
            FurnitureCategory.CORNER.value: SofaFiltersGetter,
            FurnitureCategory.CHAISE_LONGUE.value: SofaFiltersGetter,
            FurnitureCategory.ARMCHAIR.value: SofaFiltersGetter,
            FurnitureCategory.FOOTREST_AND_MODULES.value: SofaFiltersGetter,
            FurnitureCategory.COVER.value: SofaFiltersGetter,
        }
        if self.category == FurnitureCategory.WARDROBE and self.edge_wardrobes_only:
            return EdgeWardrobeFiltersGetter(category=FurnitureCategory.WARDROBE)
        if self.category in filters_getter_map:
            return filters_getter_map[self.category](category=self.category)
        return CategoryFiltersGetter(category=self.category)

    def _set_first_entry(self) -> None:
        esthetic_first_entry_filters = self.filters_getter.get_first_entry_filter(
            poverty_mode=self.poverty_mode,
        )
        first_entry_filter = (
            esthetic_first_entry_filters & self.filters_getter.get_cheap_price_filter()
        )

        if qs := self.queryset.filter(first_entry_filter):
            entry = self._get_entry_with_strategy(qs)
            self._process_successful_entry_pick(entry)
        elif (
            entry := self.queryset.filter(esthetic_first_entry_filters)
            .order_by('price')
            .first()
        ):
            self._process_successful_entry_pick(entry)
            # logger.info('No cheap first entry found for category %s', self.category)
            self.merchandising_info += (
                f'No cheap first entry found for category ' f'{self.category}'
            )

        else:
            raise NoFirstEntryFoundError

    def _get_entry_with_strategy(self, qs: QuerySet[CatalogueEntry]) -> CatalogueEntry:
        if self.strategy != StrategyEnum.NONE:
            qs = qs.order_by(f'-{self.shiam_field}')
            num_with_max_value = NUM_OF_ENTRIES_WITH_MAX_STRATEGIC_VALUE
            qs = qs[:num_with_max_value]
        return random.choice(qs)

    def _set_entry(self, get_qs_function: Callable) -> Optional[CatalogueEntry]:
        entry = None
        qs = get_qs_function()
        if not qs:
            return entry
        entry = self._get_entry_with_strategy(qs)
        self._process_successful_entry_pick(entry)
        return entry

    def _filter_furniture_type(
        self,
        queryset: QuerySet['CatalogueEntry'],
    ) -> QuerySet['CatalogueEntry']:
        if self.category not in MIXED_CATEGORIES_FURNITURE_TYPE_WEIGHTS.keys():
            return queryset

        jetties = 0
        watties = 0
        jetty_shelf_types = ShelfType.get_jetty_shelf_types()
        watty_shelf_types = ShelfType.get_watty_shelf_types()
        for shelf_type in list(self.filters_getter.last_shelf_types)[
            :DEFAULT_ROW_LENGTH
        ]:
            if shelf_type in jetty_shelf_types:
                jetties += 1
            elif shelf_type in watty_shelf_types:
                watties += 1

        filtered_queryset = None
        if jetties >= watties:
            filtered_queryset = queryset.exclude(
                content_type__model=Furniture.jetty.value
            )
        elif jetties <= watties:
            filtered_queryset = queryset.exclude(
                content_type__model=Furniture.watty.value
            )

        if filtered_queryset:
            return filtered_queryset

        return queryset

    def _get_first_stage_qs(self) -> QuerySet[CatalogueEntry]:
        """Class for getting queryset with entries complying with design rules.

        Entries with photos are preferred, so the queryset is first filtered by both
        esthetic rules and the presence of photos. If the resulting queryset is empty,
        then only aesthetic rules are applied.
        """
        esthetic_filters = self.filters_getter.get_esthetic_filters()

        qs = self._get_esthetic_queryset(esthetic_filters)
        qs = self._filter_furniture_type(qs)
        qs = self._filter_shelf_types(qs)
        qs = self._filter_original_patterns(qs)

        if qs_with_promoted := qs.filter(
            self.filters_getter.get_category_promoted_filter()
        ):
            return qs_with_promoted

        qs = self._get_queryset_with_photos(qs) or qs

        if not self.is_first_page:
            return qs

        return self._get_queryset_with_topsellers(qs) or qs

    def _set_overrides(self) -> None:
        entry = None
        for override in self.overrides.order_by('order'):
            entry = override.entry
            self._process_successful_entry_pick(entry, use_overrides=True)
        self.filters_getter.determine_color_group_for_last_overrides_entry(entry)

    def _get_second_stage_qs(self) -> QuerySet[CatalogueEntry]:
        second_stage_filters = self.filters_getter.get_second_stage_filters()
        return self.queryset.exclude(id__in=self.used_ids).filter(second_stage_filters)

    def _get_remaining_entries_with_strategy(
        self,
        qs_left: QuerySet[CatalogueEntry],
    ) -> list[CatalogueEntry]:
        return list(
            qs_left.order_by(
                f'-{self.shiam_field}',
                StrategyEnum.NONE.get_category_order_field(),
            )
        )

    def __str__(self) -> str:
        return (
            f'CategoryMerchandiser for category {self.category} with strategy: '
            f'{self.strategy}'
        )


class CollectiveCategoryMerchandiser(SofaOrderingWithBasicRulesMixin, BaseMerchandiser):
    MODEL_ORDERING_FIELD = 'test_profit_netto_dynamic_order'
    ORDERING_KEY = ListingOrder.COLLECTIVE_CATEGORY.value

    def __init__(self, collective_category: CollectiveFurnitureCategory, **kwargs):
        self.collective_category = collective_category
        self._region_code = None
        super().__init__(**kwargs)

    @property
    def region_code(self):
        if self._region_code is None:
            self._region_code = self.strategy.get_region_code(region=self.region)
        return self._region_code

    def update_ordering_field_to_none(self):
        self.queryset.update(
            **{
                self.MODEL_ORDERING_FIELD: Func(
                    F(self.MODEL_ORDERING_FIELD),
                    Value([self.region_code, self.ORDERING_KEY]),
                    Cast(Value('null'), JSONField()),
                    function='jsonb_set',
                )
            }
        )

    def update_catalogue(self) -> None:
        CatalogueEntry.objects.bulk_update(
            self.entries_to_update,
            fields=[self.MODEL_ORDERING_FIELD],
            batch_size=1000,
        )

    def _get_queryset(self) -> QuerySet[CatalogueEntry]:
        return CatalogueEntry.objects.filter(
            category__in=self.collective_category.get_furniture_categories()
        )

    def _get_ordering_field(self) -> str:
        return self.strategy.get_test_dynamic_order(
            listing=ListingOrder.COLLECTIVE_CATEGORY, region=self.region
        )

    def _set_order(self, entry: CatalogueEntry) -> None:
        ordering_field = getattr(entry, self.MODEL_ORDERING_FIELD)
        ordering_field[self.region_code][self.ORDERING_KEY] = self.order
        self.entries_to_update.append(entry)
        self.order += 1
