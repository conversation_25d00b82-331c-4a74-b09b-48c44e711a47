import pytest

from catalogue.enums import StrategyEnum


@pytest.mark.django_db
class TestStrategyEnum:
    @pytest.mark.parametrize(
        (
            'strategy_name',
            'shiam_field',
            'order_field',
            'category_order_field',
            'test_order_field',
            'test_category_order_field',
        ),
        [
            (
                'none',
                None,
                'order',
                'category_order',
                'test_order',
                'test_category_order',
            ),
            (
                'profit_netto',
                'profit_netto',
                'profit_netto_order',
                'profit_netto_category_order',
                'test_profit_netto_order',
                'test_profit_netto_category_order',
            ),
        ],
    )
    def test_global_strategy_fields(
        self,
        strategy_name,
        shiam_field,
        order_field,
        category_order_field,
        test_order_field,
        test_category_order_field,
    ):
        strategy = StrategyEnum(strategy_name)

        strategy_shiam_field = strategy.get_shiam_field()
        strategy_order_field = strategy.get_order_field()
        strategy_category_order_field = strategy.get_category_order_field()
        strategy_test_order_field = strategy.get_test_order_field()
        strategy_test_category_order_field = strategy.get_test_category_order_field()

        assert strategy_shiam_field == shiam_field
        assert strategy_order_field == order_field
        assert strategy_category_order_field == category_order_field
        assert strategy_test_order_field == test_order_field
        assert strategy_test_category_order_field == test_category_order_field

    @pytest.mark.parametrize(
        (
            'region_name',
            'code',
            'shiam_field',
            'order_field',
            'category_order_field',
            'test_order_field',
            'test_category_order_field',
        ),
        [
            (
                'germany',
                'de',
                'profit_netto_de',
                'profit_netto_order_de',
                'profit_netto_category_order_de',
                'test_profit_netto_order_de',
                'test_profit_netto_category_order_de',
            ),
            (
                'france',
                'fr',
                'profit_netto_fr',
                'profit_netto_order_fr',
                'profit_netto_category_order_fr',
                'test_profit_netto_order_fr',
                'test_profit_netto_category_order_fr',
            ),
            (
                'netherlands',
                'nl',
                'profit_netto_nl',
                'profit_netto_order_nl',
                'profit_netto_category_order_nl',
                'test_profit_netto_order_nl',
                'test_profit_netto_category_order_nl',
            ),
            (
                'united_kingdom',
                'uk',
                'profit_netto_uk',
                'profit_netto_order_uk',
                'profit_netto_category_order_uk',
                'test_profit_netto_order_uk',
                'test_profit_netto_category_order_uk',
            ),
            (
                'switzerland',
                'ch',
                'profit_netto_ch',
                'profit_netto_order_ch',
                'profit_netto_category_order_ch',
                'test_profit_netto_order_ch',
                'test_profit_netto_category_order_ch',
            ),
        ],
    )
    def test_profit_netto_fields_for_region(
        self,
        region_name,
        code,
        shiam_field,
        order_field,
        category_order_field,
        test_order_field,
        test_category_order_field,
        country_factory,
    ):
        strategy = StrategyEnum.PROFIT_NETTO
        country = country_factory(
            name=region_name,
            region__name=region_name,
            code=code,
        )
        region = country.region
        strategy_shiam_field = strategy.get_shiam_field(
            region_name=region.name,
            country_code=region.country.code,
        )
        strategy_order_field = strategy.get_order_field(
            region_name=region.name,
            country_code=region.country.code,
        )
        strategy_category_order_field = strategy.get_category_order_field(
            region_name=region.name,
            country_code=region.country.code,
        )
        strategy_test_order_field = strategy.get_test_order_field(
            region_name=region.name,
            country_code=region.country.code,
        )
        strategy_test_category_order_field = strategy.get_test_category_order_field(
            region_name=region.name,
            country_code=region.country.code,
        )

        assert strategy_shiam_field == shiam_field
        assert strategy_order_field == order_field
        assert strategy_category_order_field == category_order_field
        assert strategy_test_order_field == test_order_field
        assert strategy_test_category_order_field == test_category_order_field

    def test_returns_proper_alternative_profit_netto_order_fields(self):
        strategy = StrategyEnum.PROFIT_NETTO

        assert strategy.get_alt_order_field() == 'profit_netto_alt_order'
        assert strategy.get_test_alt_order_field() == 'test_profit_netto_alt_order'

    def test_returns_proper_alternative_profit_netto_category_order_fields(self):
        strategy = StrategyEnum.PROFIT_NETTO

        assert (
            strategy.get_alt_category_order_field() == 'profit_netto_alt_category_order'
        )
        assert strategy.get_test_alt_category_order_field() == (
            'test_profit_netto_alt_category_order'
        )

    def test_raises_errors_when_getting_alternative_fields_for_strategy_none(self):
        strategy = StrategyEnum.NONE

        with pytest.raises(ValueError):
            strategy.get_alt_order_field()

        with pytest.raises(ValueError):
            strategy.get_alt_category_order_field()

        with pytest.raises(ValueError):
            strategy.get_test_alt_order_field()

        with pytest.raises(ValueError):
            strategy.get_test_alt_category_order_field()
