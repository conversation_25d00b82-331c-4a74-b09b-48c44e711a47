from unittest import mock

from django.core.files.base import File

import pytest

from pytest_cases import (
    case,
    parametrize_with_cases,
)

from catalogue.enums import FurnitureAttributesEnum
from catalogue.exceptions import (
    HasRealLifestyleImageException,
    HasTopsellersException,
    HighOnBoardsException,
    UsedInBoardManualOrderException,
)
from catalogue.models import CatalogueEntry
from catalogue.services.attributes_assigner import AttributesAssigner
from catalogue.services.create_entries import CatalogueEntryCreator
from catalogue.services.remove_entries import (
    CatalogueEntryRemover,
    CatalogueSingleEntryRemover,
)
from custom.enums import (
    ShelfType,
    Type01Color,
    Type02Color,
    Type03Color,
)
from custom.enums.colors import Sofa01Color
from gallery.enums import FurnitureCategory
from gallery.tests.factories import get_sotty_element


@pytest.mark.django_db
class TestCatalogueEntryCreator:
    def test_create_original_catalogue_entry(
        self,
        jetty,
    ):
        all_shelves_order = 0
        category_order = 0
        count_before = CatalogueEntry.objects.count()

        _, catalogue_entry = CatalogueEntryCreator().create_original_catalogue_entry(
            jetty,
            all_shelves_order,
            category_order,
        )
        count_after = CatalogueEntry.objects.count()

        assert count_after == count_before + 1
        assert catalogue_entry.furniture == jetty

    def test_create_catalogue_entries_set_in_all_colors(self, jetty):
        all_shelves_order = 0
        category_order = 0
        expected_count = len(Type01Color.choices_active())
        count_before = CatalogueEntry.objects.count()

        result = CatalogueEntryCreator().create_catalogue_entries_set_in_all_colors(
            jetty,
            all_shelves_order,
            category_order,
        )
        count_after = CatalogueEntry.objects.count()

        assert result == expected_count
        assert count_after == count_before + expected_count

    def test_create_catalogue_entries_set_in_all_colors_for_desk(
        self,
        jetty_factory,
    ):
        desk = jetty_factory(
            shelf_type=ShelfType.TYPE02,
            shelf_category=FurnitureCategory.DESK,
        )
        all_shelves_order = 0
        category_order = 0
        expected_count = len(Type02Color.get_desk_available_colors())
        count_before = CatalogueEntry.objects.count()

        result = CatalogueEntryCreator().create_catalogue_entries_set_in_all_colors(
            desk,
            all_shelves_order,
            category_order,
        )
        count_after = CatalogueEntry.objects.count()

        assert result == expected_count
        assert count_after == count_before + expected_count

    def test_create_catalogue_entries_from_furniture(self, jetty):
        all_shelves_order = 0
        category_order = 0
        expected_count = len(Type01Color.choices_active())
        count_before = CatalogueEntry.objects.count()

        result = CatalogueEntryCreator().create_catalogue_entries_from_furniture(
            jetty.furniture_type,
            jetty.id,
            all_shelves_order,
            category_order,
        )
        count_after = CatalogueEntry.objects.count()

        assert result == expected_count
        assert count_after == count_before + expected_count


@pytest.fixture
def entries_set(catalogue_entry_factory):
    entries = catalogue_entry_factory.create_batch(size=5)
    entry = entries[0]
    available_colors = [
        {'id': entry.object_id, 'material': entry.material} for entry in entries
    ]
    entry.available_colors = available_colors
    entry.save()
    return entries


@pytest.mark.django_db
class TestCatalogueEntryRemover:
    def test_delete_all_entries(self, entries_set):
        entry = entries_set[0]

        assert CatalogueEntry.objects.count() == len(entries_set)

        remover = CatalogueEntryRemover(entry)
        remover.delete_all_entries()

        assert CatalogueEntry.objects.count() == 0

    def test_delete_all_entries_raises_error_when_entry_in_board_manual_orders(
        self,
        entries_set,
        board_manual_order_factory,
    ):
        entry = entries_set[0]
        board_manual_order_factory(entry=entry)

        remover = CatalogueEntryRemover(entry)

        with pytest.raises(UsedInBoardManualOrderException):
            remover.delete_all_entries()

    def test_delete_all_entries_raises_error_when_entry_has_insta_grid_image(
        self,
        mocker,
        entries_set,
    ):
        entry = entries_set[0]
        image_file = mocker.MagicMock(spec=File)
        image_file.name = 'test_name.jpg'
        entry.real_lifestyle_image = image_file
        entry.save()

        remover = CatalogueEntryRemover(entry)

        with pytest.raises(HasRealLifestyleImageException):
            remover.delete_all_entries()

    def test_delete_all_entries_raises_error_when_entry_is_topseller(
        self,
        entries_set,
    ):
        entry = entries_set[0]
        entry.attributes.add(FurnitureAttributesEnum.TOP_SELLER)

        remover = CatalogueEntryRemover(entry)

        with pytest.raises(HasTopsellersException):
            remover.delete_all_entries()

    def test_delete_all_entries_raises_error_when_entry_is_high_on_boards(
        self,
        entries_set,
    ):
        entry = entries_set[0]
        entry.profit_netto_category_order = 1
        entry.save(update_fields=['profit_netto_category_order'])

        remover = CatalogueEntryRemover(entry)

        with pytest.raises(HighOnBoardsException):
            remover.delete_all_entries()


@pytest.fixture
def entry_with_internal_drawers(catalogue_entry_factory, watty_factory):
    drawers = [
        {
            'x1': -270,
            'x2': 294,
            'y1': 58,
            'y2': 208,
            'z1': 60,
            'z2': 612,
            'origin': {'x': 1, 'y': 1, 'z': 1},
            'subtype': 'i',
        }
    ]
    watty = watty_factory(
        drawers=drawers,
        shelf_type=ShelfType.TYPE03.value,
        material=Type03Color.WHITE.value,
    )
    return catalogue_entry_factory(furniture=watty)


class TestAttributesAssignerCases:
    @case(tags='sotty')
    def case_sotty_with_armrest_attribute(self, sotty_factory):
        sotty = sotty_factory(
            armrests=[
                get_sotty_element(material=Sofa01Color.REWOOL2_BROWN),
                get_sotty_element(material=Sofa01Color.REWOOL2_BROWN),
            ]
        )
        return sotty, FurnitureAttributesEnum.ARMREST

    @case(tags='sotty')
    def case_sotty_with_semi_openend_attribute(self, sotty_factory):
        sotty = sotty_factory(
            armrests=[get_sotty_element(material=Sofa01Color.REWOOL2_BROWN)]
        )
        return sotty, FurnitureAttributesEnum.SEMI_OPENEND

    @case(tags='sotty')
    def case_sotty_with_openend_attribute(self, sotty_factory):
        sotty = sotty_factory(armrests=[])
        return sotty, FurnitureAttributesEnum.OPENEND

    @case(tags='sotty')
    def case_sotty_with_seating_attribute(self, sotty_factory):
        sotty = sotty_factory(
            seaters=[get_sotty_element(material=Sofa01Color.REWOOL2_BROWN)]
        )
        return sotty, FurnitureAttributesEnum.SEATING

    @case(tags='sotty')
    def case_sotty_with_chaise_longue_attribute(self, sotty_factory):
        sotty = sotty_factory(
            chaise_longues=[get_sotty_element(material=Sofa01Color.REWOOL2_BROWN)]
        )
        return sotty, FurnitureAttributesEnum.CHAISE_LONGUE

    @case(tags='sotty')
    def case_sotty_with_corner_attribute(self, sotty_factory):
        sotty = sotty_factory(
            corners=[get_sotty_element(material=Sofa01Color.REWOOL2_BROWN)]
        )
        return sotty, FurnitureAttributesEnum.CORNER

    @case(tags='sotty')
    @pytest.mark.parametrize(
        ('material', 'attribute'),
        [
            (Sofa01Color.CORDUROY_ROCK, FurnitureAttributesEnum.CORDUROY),
            (Sofa01Color.REWOOL2_BROWN, FurnitureAttributesEnum.WOOL),
        ],
    )
    def case_sotty_with_material_attribute(self, sotty_factory, material, attribute):
        sotty = sotty_factory(materials=[material])
        return sotty, attribute

    @case(tags='sotty')
    @pytest.mark.parametrize(
        ('params', 'attribute'),
        [
            ({'symmetrical': True}, FurnitureAttributesEnum.ORIENTATION_SYMMETRICAL),
            ({'direction': 'left'}, FurnitureAttributesEnum.ORIENTATION_LEFT),
            ({'direction': 'right'}, FurnitureAttributesEnum.ORIENTATION_RIGHT),
        ],
    )
    def case_sotty_with_orientation_attribute(self, sotty_factory, params, attribute):
        sotty = sotty_factory(configurator_params=params)
        return sotty, attribute

    @case(tags='sotty')
    def case_sotty_with_extended_chaise_longue_attribute(self, sotty_factory):
        configurator_params = {'layout': [{'modules': [{'type': 'split'}]}]}
        sotty = sotty_factory(configurator_params=configurator_params)
        return sotty, FurnitureAttributesEnum.EXTENDED_CHAISE_LONGUE


@pytest.mark.django_db
class TestAttributesAssigner:
    def test_assign_attributes(self, entry_with_internal_drawers):
        entry = entry_with_internal_drawers
        entry.attributes.clear()
        assert not entry.attributes.all()

        entry_expected_attributes = {
            FurnitureAttributesEnum.DRAWERS,
            FurnitureAttributesEnum.INTERNAL_DRAWERS,
            FurnitureAttributesEnum.WHITE,
        }

        AttributesAssigner(entry).assign_attributes()
        entry_attributes_after = {attr.name for attr in entry.attributes.all()}

        assert entry_expected_attributes.issubset(entry_attributes_after)

    def test_update_all_attributes(self, entry_with_internal_drawers):
        entry = entry_with_internal_drawers
        entry.attributes.add(FurnitureAttributesEnum.TOP_SELLER)
        entry_expected_attributes = {
            FurnitureAttributesEnum.DRAWERS,
            FurnitureAttributesEnum.INTERNAL_DRAWERS,
            FurnitureAttributesEnum.WHITE,
            FurnitureAttributesEnum.TOP_SELLER,
        }
        entry_attributes_before = {attr.name for attr in entry.attributes.all()}

        assert entry_expected_attributes.issubset(entry_attributes_before)

        AttributesAssigner(entry).update_all_attributes()
        entry_attributes_after = {attr.name for attr in entry.attributes.all()}

        assert entry_expected_attributes.issubset(entry_attributes_after)

    def test_update_sale_attribute_when_promo_introduced(
        self,
        entry_with_internal_drawers,
    ):
        entry = entry_with_internal_drawers
        entry.attributes.remove(FurnitureAttributesEnum.SALE)

        with mock.patch(
            'catalogue.models.CatalogueEntry.is_in_promo',
            new_callable=mock.PropertyMock,
        ) as mocked_property:
            mocked_property.return_value = True
            AttributesAssigner(entry).update_promo_attribute()

        entry_attributes_after = {attr.name for attr in entry.attributes.all()}

        assert FurnitureAttributesEnum.SALE in entry_attributes_after

    def test_update_sale_attribute_when_promo_no_longer_valid(
        self,
        entry_with_internal_drawers,
    ):
        entry = entry_with_internal_drawers
        entry.attributes.add(FurnitureAttributesEnum.SALE)

        with mock.patch(
            'catalogue.models.CatalogueEntry.is_in_promo',
            new_callable=mock.PropertyMock,
        ) as mocked_property:
            mocked_property.return_value = False
            AttributesAssigner(entry).update_promo_attribute()

        entry_attributes_after = {attr.name for attr in entry.attributes.all()}

        assert FurnitureAttributesEnum.SALE not in entry_attributes_after

    def test_update_new_arrival_attribute_when_introducing_new_product(
        self,
        entry_with_internal_drawers,
    ):
        entry = entry_with_internal_drawers
        entry.attributes.remove(FurnitureAttributesEnum.NEW_ARRIVAL)

        with mock.patch(
            'catalogue.models.CatalogueEntry.is_new_arrival',
            new_callable=mock.PropertyMock,
        ) as mocked_property:
            mocked_property.return_value = True
            AttributesAssigner(entry).update_new_arrival_attribute()

        entry_attributes_after = {attr.name for attr in entry.attributes.all()}

        assert FurnitureAttributesEnum.NEW_ARRIVAL in entry_attributes_after

    def test_update_new_arrival_attribute_when_entry_is_no_longer_new(
        self,
        entry_with_internal_drawers,
    ):
        entry = entry_with_internal_drawers
        entry.attributes.add(FurnitureAttributesEnum.NEW_ARRIVAL)

        with mock.patch(
            'catalogue.models.CatalogueEntry.is_new_arrival',
            new_callable=mock.PropertyMock,
        ) as mocked_property:
            mocked_property.return_value = False
            AttributesAssigner(entry).update_new_arrival_attribute()

        entry_attributes_after = {attr.name for attr in entry.attributes.all()}

        assert FurnitureAttributesEnum.NEW_ARRIVAL not in entry_attributes_after

    @pytest.mark.parametrize(
        ['shelf_type', 'color', 'result'],
        [
            [ShelfType.TYPE01, Type01Color.WHITE, False],
            [ShelfType.TYPE02, Type02Color.FOREST_GREEN, False],
            [ShelfType.TYPE02, Type02Color.REISINGERS_PINK, True],
        ],
    )
    def test_assign_special_edition_attribute(
        self,
        shelf_type,
        color,
        result,
        catalogue_entry_factory,
        jetty_factory,
    ):
        jetty = jetty_factory(shelf_type=shelf_type, material=color)
        catalogue_entry = catalogue_entry_factory(furniture=jetty)

        AttributesAssigner(catalogue_entry)._assign_common_attributes()

        attributes = {attr.name for attr in catalogue_entry.attributes.all()}
        assert (FurnitureAttributesEnum.SPECIAL_EDITION in attributes) is result

    @parametrize_with_cases(
        ('sotty', 'expected_attribute'),
        cases=TestAttributesAssignerCases,
        has_tag='sotty',
    )
    def test_assign_sotty_attributes(
        self, catalogue_entry_factory, sotty, expected_attribute
    ):
        catalogue_entry = catalogue_entry_factory(furniture=sotty)

        assert expected_attribute in catalogue_entry.attributes.values_list(
            'name', flat=True
        )


@pytest.mark.django_db
class TestCatalogueSingleEntryRemover:
    def test_entry_data_is_removed_from_related_entries(self, related_entries):
        entry_to_remove = related_entries[0]
        related_entry = related_entries[1]
        new_available_colors = [
            entry_data
            for entry_data in entry_to_remove.available_colors
            if entry_data['id'] != entry_to_remove.id
        ]
        catalogue_entries_count_before = CatalogueEntry.objects.count()

        CatalogueSingleEntryRemover(entry_to_remove).run()
        catalogue_entries_count_after = CatalogueEntry.objects.count()
        related_entry.refresh_from_db()

        assert catalogue_entries_count_after == catalogue_entries_count_before - 1
        assert new_available_colors == related_entry.available_colors
