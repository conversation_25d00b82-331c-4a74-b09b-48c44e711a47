import logging

from Adyen.client import AdyenClient
from Adyen.services import AdyenPaymentsApi

from custom.utils.adyen import get_current_payment_settings

logger = logging.getLogger('cstm')


def get_adyen_client():
    adyen_settings = get_current_payment_settings()
    return AdyenClient(
        xapikey=adyen_settings.get('CHECKOUT_API_KEY'),
        platform=adyen_settings.get('ENVIRONMENT'),
        merchant_account=adyen_settings.get('MERCHANT_ACCOUNT'),
        hmac=adyen_settings.get('ADYEN_HMAC_KEY'),
        live_endpoint_prefix=adyen_settings.get('LIVE_ENDPOINT_PREFIX'),
    )


def capture_klarna_payment(data):
    client = get_adyen_client()
    payment_service = AdyenPaymentsApi(client)
    return payment_service.modifications_api.capture(request=data)
