# We don't deliver sofas to those regions
from typing import Final

DENMARK_POSTAL_CODES: Final[frozenset[str]] = frozenset(
    {'4592', '4942', '4944', '5601', '5602', '5960', '5970', '5985', '9940'}
)
FRANCE_POSTAL_CODES: Final[frozenset[str]] = frozenset(
    {
        '06400',
        '17123',
        '20000',
        '20169',
        '20200',
        '20260',
        '29242',
        '29253',
        '29259',
        '29660',
        '29990',
        '50400',
        '50550',
        '56170',
        '56170',
        '56360',
        '56590',
        '83400',
        '85350',
    }
)
GERMANY_POSTAL_CODES: Final[frozenset[str]] = frozenset(
    {
        '18565',
        '25849',
        '25867',
        '25869',
        '25938',
        '25946',
        '25980',
        '25992',
        '25997',
        '26465',
        '26474',
        '26548',
        '26571',
        '26757',
        '27499',
    }
)
GERMANY_POSTAL_CODES_RANGE: Final[tuple[int, int]] = (25980, 25999)
ITALY_POSTAL_CODES: Final[frozenset[str]] = frozenset(
    {
        '04027',
        '04031',
        '19025',
        '30126',
        '30133',
        '30141',
        '30142',
        '34073',
        '57030',
        '57031',
        '57032',
        '57033',
        '57034',
        '57036',
        '57037',
        '57038',
        '57128',
        '58012',
        '58019',
        '71040',
        '80071',
        '80073',
        '80070',
        '80075',
        '80077',
        '80079',
        '80124',
        '87020',
    }
)
ITALY_REGEX_POSTAL_CODES: Final[frozenset[str]] = frozenset(
    {
        r'^9\S{4}$',  # matches 9****
        r'^0[789]\S{3}$',  # matches any 07*** or 08*** or 09***
    }
)
NETHERLANDS_POSTAL_CODES: Final[frozenset[str]] = frozenset(
    {'8881', '8882', '8883', '8884', '8899', '9161', '9162', '9163', '9164', '9166'}
)
NETHERLANDS_POSTAL_CODES_RANGE: Final[tuple[int, int]] = (1791, 1797)
PORTUGAL_POSTAL_CODES_RANGE: Final[tuple[int, int]] = (9000, 9385)
SPAIN_POSTAL_CODES: Final[frozenset[str]] = frozenset(
    {'070', '071', '072', '073', '074', '075', '076'}
)

UK_POSTAL_CODES: Final[frozenset[str]] = frozenset(
    {'BT', 'GY', 'HS', 'IM', 'JE', 'KW', 'LL', 'NE', 'PA', 'SA', 'TQ', 'TR', 'ZE'}
)
