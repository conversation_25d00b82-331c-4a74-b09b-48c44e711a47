import json
import re

from pathlib import Path
from typing import Final

from django.conf import settings

from checkout.services.postal_codes import (
    outside_of_tax_union,
    sofa_delivery,
)
from regions.types import RegionLikeObject

SPECIAL_TERRITORIES_PATH: Final[Path] = (
    Path(settings.BASE_DIR) / 'checkout/services/postal_codes/special_territories.json'
)
with open(SPECIAL_TERRITORIES_PATH, 'r') as file:
    SPECIAL_TERRITORIES_POSTAL_CODES: Final[dict[str, list[str]]] = json.load(file)


class RegionRestrictionService:
    def __init__(self, postal_code: str, region: RegionLikeObject):
        self.postal_code = postal_code.strip()
        self.region_name = region.name

    def is_outside_of_tax_union(self) -> bool:
        """
        Validate if a postal code belongs to the special territories of EU countries.

        This function checks whether a given postal code falls within
        the special territories of European Union countries.
        These territories are subject to specific restrictions,
        and products may not be delivered to customers in these areas.
        https://cstm-tasks.atlassian.net/browse/ECO-4037
        """

        if self.region_name == 'united_kingdom':
            return any(
                self.postal_code.startswith(area_code)
                for area_code in outside_of_tax_union.UK_POSTAL_CODES
            )
        return self.postal_code in SPECIAL_TERRITORIES_POSTAL_CODES.get(
            self.region_name, []
        )

    def is_sofa_delivery_unavailable(self) -> bool:
        """Most addresses are islands, so delivery is overpriced"""
        match self.region_name:
            case 'denmark':
                return self.postal_code in sofa_delivery.DENMARK_POSTAL_CODES
            case 'france':
                return self.postal_code in sofa_delivery.FRANCE_POSTAL_CODES
            case 'germany':
                is_restricted = self.postal_code in sofa_delivery.GERMANY_POSTAL_CODES
                try:
                    first_5_digits = int(self.postal_code[:5])
                except ValueError:
                    return is_restricted
                return is_restricted or (
                    sofa_delivery.GERMANY_POSTAL_CODES_RANGE[0]
                    <= first_5_digits
                    <= sofa_delivery.GERMANY_POSTAL_CODES_RANGE[1]
                )
            case 'italy':
                return self.postal_code in sofa_delivery.ITALY_POSTAL_CODES or any(
                    re.match(regex, self.postal_code)
                    for regex in sofa_delivery.ITALY_REGEX_POSTAL_CODES
                )
            case 'netherlands':
                is_restricted = (
                    self.postal_code in sofa_delivery.NETHERLANDS_POSTAL_CODES
                )
                try:
                    first_4_digits = int(self.postal_code[:4])
                except ValueError:
                    return is_restricted
                return is_restricted or (
                    sofa_delivery.NETHERLANDS_POSTAL_CODES_RANGE[0]
                    <= first_4_digits
                    <= sofa_delivery.NETHERLANDS_POSTAL_CODES_RANGE[1]
                )
            case 'portugal':
                try:
                    first_4_digits = int(self.postal_code[:4])
                except ValueError:
                    return False
                return (
                    sofa_delivery.PORTUGAL_POSTAL_CODES_RANGE[0]
                    <= first_4_digits
                    <= sofa_delivery.PORTUGAL_POSTAL_CODES_RANGE[1]
                )
            case 'spain':
                return any(
                    self.postal_code.startswith(area_code)
                    for area_code in sofa_delivery.SPAIN_POSTAL_CODES
                )
            case 'united_kingdom':
                return any(
                    self.postal_code.startswith(area_code)
                    for area_code in sofa_delivery.UK_POSTAL_CODES
                )
            case _:
                return False
