from django.utils.html import escapejs

from checkout.enums import TypeformSurveyMode
from checkout.services.typeform_url_generator import TypeformUrlGenerator
from frontend_cms.templatetags.util_tags import register


@register.simple_tag()
def get_typeform_url(language_code, order, escape_js=True):
    url = TypeformUrlGenerator(
        order=order,
        language_code=language_code,
        mode=TypeformSurveyMode.POST_CHECKOUT,
    ).get_url()
    return escapejs(url) if escape_js else url
