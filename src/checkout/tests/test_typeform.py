import pytest

from checkout.enums import TypeformSurveyMode
from checkout.services.typeform_url_generator import TypeformUrlGenerator
from custom.enums import (
    LanguageEnum,
    ShelfType,
)
from gallery.enums import FurnitureCategory


@pytest.mark.django_db
class TestTypeformURLGenerator:
    @pytest.mark.parametrize(
        ('langauge', 'url'),
        [
            ('en', 'https://form.typeform.com/to/BmtKwHe5#order_id'),
            ('de', 'https://form.typeform.com/to/Lpdxs8km#order_id'),
            ('DE', 'https://form.typeform.com/to/Lpdxs8km#order_id'),
            ('fr', 'https://form.typeform.com/to/rhvWR481#order_id'),
            ('FR', 'https://form.typeform.com/to/rhvWR481#order_id'),
            ('nl', 'https://form.typeform.com/to/BmtKwHe5#order_id'),
            ('es', 'https://form.typeform.com/to/BmtKwHe5#order_id'),
            ('es', 'https://form.typeform.com/to/BmtKwHe5#order_id'),
            ('la', 'https://form.typeform.com/to/BmtKwHe5#order_id'),
            ('pl', 'https://form.typeform.com/to/BmtKwHe5#order_id'),
        ],
    )
    def test_correct_language_for_post_delivery_survey(self, order, langauge, url):
        typeform_url = TypeformUrlGenerator(
            order=order,
            language_code=langauge,
            mode=TypeformSurveyMode.POST_DELIVERY,
        ).get_url()
        assert f'{url}={order.id}' in typeform_url

    @pytest.mark.parametrize(
        ('langauge', 'url'),
        [
            ('en', 'https://form.typeform.com/to/PeBjA0FF#order_id'),
            ('de', 'https://form.typeform.com/to/vOys9Vnt#order_id'),
            ('DE', 'https://form.typeform.com/to/vOys9Vnt#order_id'),
            ('fr', 'https://form.typeform.com/to/VlVtzYpQ#order_id'),
            ('FR', 'https://form.typeform.com/to/VlVtzYpQ#order_id'),
            ('nl', 'https://form.typeform.com/to/PeBjA0FF#order_id'),
            ('es', 'https://form.typeform.com/to/PeBjA0FF#order_id'),
            ('es', 'https://form.typeform.com/to/PeBjA0FF#order_id'),
            ('la', 'https://form.typeform.com/to/PeBjA0FF#order_id'),
            ('pl', 'https://form.typeform.com/to/PeBjA0FF#order_id'),
        ],
    )
    def test_correct_language_for_post_checkout_survey(self, order, langauge, url):
        typeform_url = TypeformUrlGenerator(
            order=order,
            language_code=langauge,
            mode=TypeformSurveyMode.POST_CHECKOUT,
        ).get_url()
        assert f'{url}={order.id}' in typeform_url

    @pytest.mark.parametrize(
        ('shelf_type', 'order_type'),
        [
            (ShelfType.TYPE01, 'type01'),
            (ShelfType.VENEER_TYPE01, 'type01v'),
            (ShelfType.TYPE02, 'type02'),
        ],
    )
    def test_order_type_jetty(
        self,
        order_factory,
        order_item_factory,
        jetty_factory,
        shelf_type,
        order_type,
    ):
        order = order_factory(items=None)
        jetty = jetty_factory(shelf_type=shelf_type)
        order_item_factory(order=order, order_item=jetty)
        expected_order_type = TypeformUrlGenerator(
            order=order,
            language_code=LanguageEnum.EN,
            mode=TypeformSurveyMode.POST_DELIVERY,
        )._get_order_type()
        assert expected_order_type == order_type

    @pytest.mark.parametrize(
        ('shelf_type', 'order_type'),
        [
            (ShelfType.TYPE03, 'type03'),
            (ShelfType.TYPE13, 'type13'),
        ],
    )
    def test_order_type_watty(
        self,
        order_factory,
        order_item_factory,
        watty_factory,
        shelf_type,
        order_type,
    ):
        order = order_factory(items=None)
        watty = watty_factory(shelf_type=shelf_type)
        order_item_factory(order=order, order_item=watty)
        expected_order_type = TypeformUrlGenerator(
            order=order,
            language_code=LanguageEnum.EN,
            mode=TypeformSurveyMode.POST_DELIVERY,
        )._get_order_type()
        assert expected_order_type == order_type

    def test_mixed_order_type(
        self,
        order_factory,
        order_item_factory,
        jetty_factory,
        watty_factory,
    ):
        order = order_factory(items=None)
        order_item_factory(
            order=order, order_item=jetty_factory(shelf_type=ShelfType.TYPE01)
        )
        order_item_factory(
            order=order, order_item=watty_factory(shelf_type=ShelfType.TYPE13)
        )
        expected_order_type = TypeformUrlGenerator(
            order=order,
            language_code=LanguageEnum.EN,
            mode=TypeformSurveyMode.POST_DELIVERY,
        )._get_order_type()
        assert 'type01' in expected_order_type
        assert 'type13' in expected_order_type

    def test_mixed_furniture_categories(
        self,
        order_factory,
        order_item_factory,
        watty_factory,
        jetty_factory,
    ):
        order = order_factory(items=None)
        order_item_factory(
            order=order,
            order_item=jetty_factory(
                shelf_category=FurnitureCategory.SIDEBOARD.value,
            ),
        )
        order_item_factory(order=order, order_item=watty_factory())
        expected_order_type = TypeformUrlGenerator(
            order=order,
            language_code=LanguageEnum.EN,
            mode=TypeformSurveyMode.POST_DELIVERY,
        )._get_order_category()
        assert 'sideboard' in expected_order_type
        assert 'wardrobe' in expected_order_type

    def test_order_with_sample_box(
        self,
        order_factory,
        order_item_factory,
        jetty_factory,
    ):
        order = order_factory(items=None)
        order_item_factory(
            order=order,
            order_item=jetty_factory(
                shelf_category=FurnitureCategory.SIDEBOARD.value,
                shelf_type=ShelfType.TYPE01,
            ),
        )
        order_item_factory(
            order=order,
            is_sample_box=True,
        )
        typeform_url = TypeformUrlGenerator(
            order=order,
            language_code=LanguageEnum.EN,
            mode=TypeformSurveyMode.POST_DELIVERY,
        ).get_url()
        assert '&shelf_category=sideboard' in typeform_url
        assert '&order_type=type01' in typeform_url
