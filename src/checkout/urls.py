from django.urls import re_path

from frontend_cms.views.static_pages import ReverseOnlyView

urlpatterns = [
    re_path(r'^cart/', ReverseOnlyView.as_view(), name='front-cart'),
    re_path(r'^checkout/', ReverseOnlyView.as_view(), name='front-checkout'),
    re_path(
        r'^confirmation/(?P<pk>[0-9]+)/',
        ReverseOnlyView.as_view(),
        name='front-confirmation',
    ),
    re_path(
        r'^payment_methods/(?P<pk>[0-9]+)?',
        ReverseOnlyView.as_view(),
        name='front-payment-method',
    ),
    re_path(
        r'^confirmation_pending/(?P<pk>[0-9]+)/',
        ReverseOnlyView.as_view(),
        name='front-confirmation-pending',
    ),
]
