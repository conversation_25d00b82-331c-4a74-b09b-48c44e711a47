import datetime

from decimal import Decimal

from django.contrib import admin
from django.contrib.admin import SimpleList<PERSON>ilter
from django.utils.encoding import force_str

from rangefilter.filters import DateRangeFilter

from complaints.models import (
    Responsibility,
    TypicalIssues,
)
from complaints.reproduction_days_elements import REPRODUCTION_CATEGORIES
from custom.filters import BaseMultipleChoiceListFilter


class MultipleChoiceProductionComplaintReproductionElementsFilter(
    BaseMultipleChoiceListFilter,
):
    title = 'Reproduction elements'
    parameter_name = 'reproduction_element_categories'
    query_parameter = 'reproduction_element_categories__contains'
    template = 'admin/custom_filters.html'

    def lookups(self, request, model_admin):
        extra_categories = (
            ('other', 'other'),
            ('fitting', 'fitting'),
        )
        return extra_categories + tuple(
            (category.name, category.name) for category in REPRODUCTION_CATEGORIES
        )

    def queryset(self, request, queryset):
        if self.value():
            allowed_values = {k for k, _ in self.lookup_choices}
            values = self.value().split(',')
            filter_values = [value for value in values if value in allowed_values]

            if filter_values:
                query_parameters = {self.query_parameter: filter_values}
                queryset = queryset.filter(**query_parameters)

        return queryset


class DeletedComplaintFilter(SimpleListFilter):
    title = 'deleted'
    parameter_name = 'deleted'

    def lookups(self, request, model_admin):
        return [
            (True, 'Yes'),
            (False, 'No'),
        ]

    def queryset(self, request, queryset):
        if self.value() == 'True':
            return queryset.filter(deleted=True)
        elif self.value() == 'False':
            return queryset.filter(deleted=False)

        return queryset

    def choices(self, changelist):
        """Overriden choices method to remove 'all' option."""
        yield {
            'selected': self.value() is None,
            'query_string': changelist.get_query_string({}, [self.parameter_name]),
        }
        for lookup, title in self.lookup_choices:
            yield {
                'selected': self.value() == force_str(lookup),
                'query_string': changelist.get_query_string(
                    {self.parameter_name: lookup}, []
                ),
                'display': title,
            }


class DateRangeFilterWithAnnotatedField(DateRangeFilter):
    def __init__(self, field, request, params, model, model_admin, field_path):
        field_path = self.field_name
        super().__init__(field, request, params, model, model_admin, field_path)

    def _make_query_filter(self, request, validated_data):
        query_params = {}
        date_value_gte = validated_data.get(self.lookup_kwarg_gte, None)
        date_value_lte = validated_data.get(self.lookup_kwarg_lte, None)

        if date_value_gte:
            query_params['{0}__gte'.format(self.field_path)] = str(
                self.make_dt_aware(
                    datetime.datetime.combine(date_value_gte, datetime.time.min),
                    self.get_timezone(request),
                )
            )
        if date_value_lte:
            query_params['{0}__lte'.format(self.field_path)] = str(
                self.make_dt_aware(
                    datetime.datetime.combine(date_value_lte, datetime.time.max),
                    self.get_timezone(request),
                )
            )

        return query_params


class DiscountValueFilter(SimpleListFilter):
    title = 'Discount value'
    parameter_name = 'no_discount_value'

    def lookups(self, request, model_admin):
        return [
            (True, 'zero'),
            (False, 'more than zero'),
        ]

    def queryset(self, request, queryset):
        if self.value() == 'True':
            return queryset.exclude(complaint_costs__refund_in_euro__gt=Decimal('0.0'))
        elif self.value() == 'False':
            return queryset.filter(complaint_costs__refund_in_euro__gt=Decimal('0.0'))
        return queryset


class SerializedLogisticOrderDeliveredDateRangeJSONFilter(
    DateRangeFilterWithAnnotatedField
):
    field_name = 'product__order__serialized_logistic_info__0__delivered_date'


class SerializedLogisticOrderSentToCustomerRangeJSONFilter(
    DateRangeFilterWithAnnotatedField
):
    field_name = 'product__order__serialized_logistic_info__0__sent_to_customer'


class ComplaintTypicalIssuesByNameFilter(SimpleListFilter):
    title = 'typical_issues'
    parameter_name = 'typical_issues'

    def lookups(self, request, model_admin):
        typical_issues = TypicalIssues.objects.values_list('name', 'name').distinct()

        return (
            *typical_issues,
            ('null', '-'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'null':
            return queryset.filter(typical_issues__isnull=True)
        elif self.value():
            return queryset.filter(typical_issues__name=self.value())


class ComplaintResponsibilityByNameFilter(admin.SimpleListFilter):
    title = 'responsibility'
    parameter_name = 'responsibility_custom'

    def lookups(self, request, model_admin):
        """
        Returns a list of tuples. The first element in each
        tuple is the coded value for the option that will
        appear in the URL query. The second element is the
        human-readable name for the option that will appear
        in the right sidebar.
        """
        responsibilities = (
            (responsibility.replace(' ', '_'), responsibility)
            for responsibility in Responsibility.objects.values_list(
                'name', flat=True
            ).distinct()
        )

        return [
            *responsibilities,
            ('null', '-'),
        ]

    def queryset(self, request, queryset):
        """
        Returns the filtered queryset based on the value
        provided in the query string and retrievable via
        `self.value()`.
        """
        if self.value() == 'null':
            return queryset.filter(responsibility__isnull=True)
        elif self.value():
            actual_value = self.value().replace('_', ' ')
            return queryset.filter(responsibility__name=actual_value)
        return queryset
