# Generated by Django 3.2.16 on 2022-11-22 13:35

import django.core.validators
import django.db.models.deletion

from django.db import (
    migrations,
    models,
)

import complaints.models
import complaints.utils


class Migration(migrations.Migration):

    dependencies = [
        (
            'producers',
            '0041_remove_priority_complaint_for_products_squashed_0091_alter_manufactor_invoice_company_name_and_more',  # noqa E501
        ),
        ('complaints', '0037_migrate_old_complaints_tree_to_new_schema'),
    ]

    operations = [
        migrations.CreateModel(
            name='DamageData',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('ready_since', models.DateTimeField(null=True)),
                (
                    'status',
                    models.PositiveSmallIntegerField(
                        choices=[(1, 'Draft'), (2, 'Ready')], default=1
                    ),
                ),
                (
                    'was_reported_to_delivery_company',
                    models.BooleanField(
                        choices=[(True, 'Yes'), (False, 'No')],
                        verbose_name='Did you report the damage '
                        + 'to the delivery company?',
                    ),
                ),
                (
                    'are_any_elements_of_the_shelf_damaged',
                    models.BooleanField(
                        choices=[(True, 'Yes'), (False, 'No')],
                        verbose_name='Are any elements of the shelf itself damaged?',
                    ),
                ),
                (
                    'are_any_elements_missing',
                    models.BooleanField(
                        choices=[(True, 'Yes'), (False, 'No')],
                        verbose_name='Are there any elements missing?',
                    ),
                ),
                ('list_of_damaged_elements', models.TextField()),
                ('additional_comments', models.TextField(blank=True)),
                (
                    'product',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='producers.product',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='DamageImage',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True)),
                (
                    'image',
                    models.ImageField(
                        max_length=150,
                        upload_to=complaints.models.damage_form_image_file_name,
                        validators=[
                            django.core.validators.FileExtensionValidator(
                                allowed_extensions=['jpg', 'jpeg', 'png']
                            ),
                            complaints.utils.validate_image_size,
                        ],
                    ),
                ),
                (
                    'image_type',
                    models.CharField(
                        choices=[
                            ('damaged_package', 'Damaged package'),
                            ('damaged_elements', 'Damaged elements'),
                        ],
                        max_length=32,
                    ),
                ),
                (
                    'damage_form_data',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='complaints.damagedata',
                    ),
                ),
            ],
        ),
    ]
