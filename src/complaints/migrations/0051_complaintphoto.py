# Generated by Django 4.1.9 on 2024-05-15 10:56

import django.core.files.storage
import django.db.models.deletion

from django.db import (
    migrations,
    models,
)

import producers.utils


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0048_orderbarterdata'),
        ('complaints', '0050_add_complaintcosts_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ComplaintPhoto',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('file_name', models.CharField(blank=True, max_length=255)),
                (
                    'photo',
                    models.FileField(
                        blank=True,
                        max_length=400,
                        storage=django.core.files.storage.FileSystemStorage(),
                        upload_to=producers.utils.RandomizedUploadTo(
                            'complaints/photo/%Y/%m'
                        ),
                    ),
                ),
                ('dixa_url_path', models.CharField(blank=True, max_length=1000)),
                ('dixa_conversation_id', models.CharField(blank=True, max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('email', models.EmailField(blank=True, max_length=254)),
                (
                    'complaint',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='photos',
                        to='complaints.complaint',
                    ),
                ),
                (
                    'orders',
                    models.ManyToManyField(
                        blank=True, related_name='complaint_photos', to='orders.order'
                    ),
                ),
            ],
        ),
    ]
