import typing

from datetime import datetime
from decimal import Decimal

from django.contrib.auth.models import User

from complaints.choices import NotificationTypes
from complaints.enums import (
    ComplaintPriorityChoices,
    ComplaintStatus,
)
from complaints.models import (
    <PERSON><PERSON><PERSON><PERSON>,
    ComplaintCosts,
    SottyDamagePhoto,
    TypicalIssues,
)
from complaints.reproduction_days_elements import get_elements_categories
from complaints.reproduction_elements import (
    ElementDetail,
    get_complaint_element_dict,
    get_element_details,
    get_elements_for_normal_complaint,
    get_elements_for_priority_complaint,
    get_max_reproduction_days_for_priority_elements,
    is_any_priority_in_elements,
)
from complaints.services.deprecated_elements import is_element_deprecated

if typing.TYPE_CHECKING:
    from producers.models import Product


def create_complaint_from_elements(
    elements: typing.Iterable[ElementDetail],
    priority: bool,
    product: 'Product',
    owner: 'User',
    material: str | None = None,
    reporter: User | None = None,
    notification_type: int = NotificationTypes.NO_INFO.value,
    reported_date: datetime = datetime.now(),
    typical_issues: TypicalIssues | None = None,
    reproduction: bool = False,
    free_return_prevention: bool = False,
    refund: bool = False,
    assembly_team_intervention: bool = False,
    boxes_damaged: bool = False,
    refund_reason: str = '',
    additional_info: str = '',
    conversation_link: str = '',
    packages_info_was_provided: bool = False,
    is_repeated: bool = False,
    fittings_only: bool = False,
    complaint_costs: ComplaintCosts | None = None,
    has_deprecated_elements: bool = False,
    created_by_assembly_team: bool = False,
    is_confirmed: bool = True,
    sotty_photos: list | None = None,
) -> Complaint:
    sotty_photos = sotty_photos or []
    status = (
        ComplaintStatus.VERIFICATION.value
        if product.is_sotty
        else ComplaintStatus.NEW.value
    )
    complaint_priority = (
        ComplaintPriorityChoices.PRIORITY
        if priority
        else ComplaintPriorityChoices.NORMAL
    )
    complaint_elements = get_complaint_element_dict(elements)
    element_categories = get_elements_categories(complaint_elements)
    responsibility = typical_issues.responsibility if typical_issues else None

    complaint = Complaint.objects.create(
        status=status,
        priority=complaint_priority,
        elements=complaint_elements,
        reproduction_element_categories=element_categories,
        product=product,
        owner=owner,
        notification_type=notification_type,
        material=material,
        reporter=reporter,
        reported_date=reported_date,
        typical_issues=typical_issues,
        reproduction=reproduction,
        free_return_prevention=free_return_prevention,
        refund=refund,
        assembly_team_intervention=assembly_team_intervention,
        boxes_damaged=boxes_damaged,
        refund_reason=refund_reason,
        additional_info=additional_info,
        conversation_link=conversation_link,
        packages_info_was_provided=packages_info_was_provided,
        is_repeated=is_repeated,
        fittings_only=fittings_only,
        complaint_costs=complaint_costs,
        has_deprecated_elements=has_deprecated_elements,
        created_by_assembly_team=created_by_assembly_team,
        is_confirmed=is_confirmed,
        responsibility=responsibility,
        manufactor_fault=(
            responsibility.is_manufacturer_responsibility() if responsibility else False
        ),
    )
    for photo in sotty_photos:
        SottyDamagePhoto.objects.create(
            photo=photo,
            file_name=photo.name,
            complaint=complaint,
        )
    return complaint


def create_complaints_with_costs(
    elements: dict,
    product: 'Product',
    request_user: 'User',
    reporter: User | None = None,
    reported_date: datetime = datetime.now(),
    notification_type: int = NotificationTypes.NO_INFO.value,
    typical_issues: TypicalIssues | None = None,
    reproduction: bool = False,
    total_cost: Decimal = Decimal('0.00'),
    free_return_prevention: bool = False,
    refund: bool = False,
    assembly_team_intervention: bool = False,
    boxes_damaged: bool = False,
    refund_amount: Decimal = Decimal('0.00'),
    refund_currency: str = '',
    refund_reason: str = '',
    additional_info: str = '',
    conversation_link: str = '',
    packages_info_was_provided: bool = False,
    is_repeated: bool = False,
    fittings_only: bool = False,
    split_complaint: bool = False,
    created_by_assembly_team: bool = False,
    is_confirmed: bool = True,
    sotty_photos: list | None = None,
) -> list[Complaint]:
    """
    1. Get elements' details based on data from Create Complaint form
    1b. If there are no priority elements - create normal Complaint
    2. All elements with reproduction days lower or equal than max priority
        belong to priority Complaint
    3. All elements with reproduction days more than max priority
        belong to normal Complaint
    """
    complaints_created = []
    complaint_costs = ComplaintCosts.objects.create(
        currency=refund_currency,
        refund_amount=refund_amount,
        total_cost=total_cost,
    )
    element_details = get_element_details(elements, product)
    supported_elements, deprecated_elements = separate_deprecated_drawer_elements(
        product, element_details
    )
    if deprecated_elements:
        complaints_created.append(
            create_complaint_from_elements(
                elements=deprecated_elements,
                priority=False,
                product=product,
                owner=request_user,
                material=getattr(product.batch, 'material_description', None),
                reporter=reporter,
                reported_date=reported_date,
                typical_issues=typical_issues,
                reproduction=True,
                free_return_prevention=free_return_prevention,
                refund=False,
                notification_type=notification_type,
                assembly_team_intervention=assembly_team_intervention,
                boxes_damaged=boxes_damaged,
                additional_info=additional_info,
                conversation_link=conversation_link,
                packages_info_was_provided=packages_info_was_provided,
                is_repeated=is_repeated,
                fittings_only=False,
                complaint_costs=complaint_costs,
                has_deprecated_elements=True,
                created_by_assembly_team=created_by_assembly_team,
                is_confirmed=is_confirmed,
                sotty_photos=sotty_photos,
            )
        )
        complaint_costs = ComplaintCosts.objects.create()

    is_assembly_without_deprecated = (
        assembly_team_intervention and not deprecated_elements
    )

    if not supported_elements and not refund and not is_assembly_without_deprecated:
        return complaints_created

    if not supported_elements:
        reproduction = False

    if not split_complaint or not is_any_priority_in_elements(supported_elements):
        complaints_created.append(
            create_complaint_from_elements(
                elements=supported_elements,
                priority=False,
                product=product,
                owner=request_user,
                material=getattr(product.batch, 'material_description', None),
                reporter=reporter,
                reported_date=reported_date,
                typical_issues=typical_issues,
                reproduction=reproduction,
                free_return_prevention=free_return_prevention,
                refund=refund,
                notification_type=notification_type,
                assembly_team_intervention=assembly_team_intervention,
                boxes_damaged=boxes_damaged,
                refund_reason=refund_reason,
                additional_info=additional_info,
                conversation_link=conversation_link,
                packages_info_was_provided=packages_info_was_provided,
                is_repeated=is_repeated,
                fittings_only=fittings_only,
                complaint_costs=complaint_costs,
                created_by_assembly_team=created_by_assembly_team,
                is_confirmed=is_confirmed,
                sotty_photos=sotty_photos,
            )
        )
        return complaints_created
    max_priority = get_max_reproduction_days_for_priority_elements(supported_elements)
    elements_for_priority = get_elements_for_priority_complaint(
        supported_elements,
        max_priority,
    )
    elements_for_normal = get_elements_for_normal_complaint(
        supported_elements,
        max_priority,
    )

    if elements_for_priority:
        complaints_created.append(
            create_complaint_from_elements(
                elements_for_priority,
                priority=True,
                product=product,
                owner=request_user,
                material=getattr(product.batch, 'material_description', None),
                reporter=reporter,
                reported_date=reported_date,
                typical_issues=typical_issues,
                notification_type=notification_type,
                reproduction=reproduction,
                free_return_prevention=free_return_prevention,
                refund=refund,
                assembly_team_intervention=assembly_team_intervention,
                boxes_damaged=boxes_damaged,
                refund_reason=refund_reason,
                additional_info=additional_info,
                conversation_link=conversation_link,
                packages_info_was_provided=packages_info_was_provided,
                is_repeated=is_repeated,
                fittings_only=fittings_only,
                complaint_costs=complaint_costs,
                created_by_assembly_team=created_by_assembly_team,
                is_confirmed=is_confirmed,
                sotty_photos=sotty_photos,
            )
        )

    if elements_for_normal:
        complaints_created.append(
            create_complaint_from_elements(
                elements_for_normal,
                priority=False,
                product=product,
                owner=request_user,
                material=getattr(product.batch, 'material_description', None),
                reporter=reporter,
                reported_date=reported_date,
                typical_issues=typical_issues,
                reproduction=reproduction,
                notification_type=notification_type,
                free_return_prevention=free_return_prevention,
                refund=refund,
                assembly_team_intervention=assembly_team_intervention,
                boxes_damaged=boxes_damaged,
                refund_reason=refund_reason,
                additional_info=additional_info,
                conversation_link=conversation_link,
                packages_info_was_provided=packages_info_was_provided,
                is_repeated=is_repeated,
                fittings_only=fittings_only,
                complaint_costs=complaint_costs,
                created_by_assembly_team=created_by_assembly_team,
                is_confirmed=is_confirmed,
                sotty_photos=sotty_photos,
            )
        )
    return complaints_created


def separate_deprecated_drawer_elements(product, elements):
    from complaints.services.deprecated_elements import (
        has_deprecated_drawers,
        translate_trex_drawer_element_to_newer,
    )
    from custom.enums import PhysicalProductVersion

    if not has_deprecated_drawers(product):
        return elements, []
    deprecated_elements = []
    supported_elements = []
    for element in elements:
        if is_element_deprecated(element.element_name):
            if (
                product.cached_physical_product_version
                == PhysicalProductVersion.TREX.value
            ):
                new_name = translate_trex_drawer_element_to_newer(element.element_name)
                if not new_name:
                    raise ValueError('Translate trex drawer element to newer failed')
                element = ElementDetail(
                    element_name=new_name,
                    element_category=element.element_category,
                    reproduction_days_element=element.reproduction_days_element,
                    number_of_fittings=element.number_of_fittings,
                )
            deprecated_elements.append(element)
        else:
            supported_elements.append(element)
    return supported_elements, deprecated_elements
