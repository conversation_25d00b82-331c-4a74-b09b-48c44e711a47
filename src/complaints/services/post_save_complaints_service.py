from dataclasses import dataclass
from decimal import Decimal
from typing import Optional

from django.conf import settings
from django.contrib import messages
from django.core.mail import EmailMessage
from django.db import transaction
from django.http import HttpRequest

from complaints.enums import ComplaintPriorityChoices
from complaints.internal_api.events import (
    ComplaintAssemblyTeamInterventionRequiredEvent,
    ComplaintChangeToReproductionServiceEvent,
    ComplaintRefreshEvent,
    ComplaintServiceDeleteEvent,
    ComplaintUpdateShippingPriceServiceEvent,
)
from complaints.models import Complaint
from complaints.services import express_replacement
from complaints.tasks import update_complaint_cost
from custom.internal_api.dto import LogisticOrderDTO
from custom.utils.slack import notify_about_complaint_to_verification
from customer_service.correction_request_strategies import (
    CorrectionRequestAmountStrategy,
)
from customer_service.enums import CSCorrectionRequestStatus
from customer_service.models import CSCorrectionRequest
from invoice.choices import InvoiceStatus
from invoice.enums import InvoiceItemTag
from invoice.models import Invoice
from orders.internal_api.events import OrderRefreshEvent
from orders.models import Order


@dataclass
class PostSaveComplaintServiceMixin:
    request: HttpRequest
    complaint: Complaint
    message: str

    elements: dict
    refund_amount: Decimal
    refund: bool
    refund_reason: str

    def run(self):
        raise NotImplementedError

    def update_express_replacement(self) -> bool:
        express_possible = express_replacement.is_express_replacement_possible(
            self.complaint
        )
        if self.complaint.express_replacement and not express_possible:
            self.complaint.express_replacement = False
            self.complaint.save(update_fields=['express_replacement'])
            return False
        elif express_possible and not self.complaint.express_replacement:
            self.complaint.express_replacement = True
            self.complaint.save(update_fields=['express_replacement'])
            return True
        return False

    def update_complaint_costs(self) -> None:
        transaction.on_commit(
            lambda: update_complaint_cost.delay(complaint_id=self.complaint.id)
        )

    def process_correction_request(self):
        if self.refund and self.complaint.priority == ComplaintPriorityChoices.NORMAL:
            order = self.complaint.product.order
            if order.is_klarna_payment():
                messages.add_message(
                    self.request,
                    messages.WARNING,
                    'Klarna payment, correction request not created',
                )
            else:
                last_invoice = self.get_last_invoice(order)
                if self.can_create_correction_request(last_invoice):
                    self.create_correction_request(last_invoice)

    @staticmethod
    def get_last_invoice(order: Order) -> Invoice:
        return (
            Invoice.objects.filter(
                status__in=[
                    InvoiceStatus.ENABLED,
                    InvoiceStatus.CORRECTING,
                    InvoiceStatus.ENABLED_VAT_REGION_CORRECTION,
                ],
                order_id=order.id,
            )
            .order_by('id')
            .last()
        )

    def can_create_correction_request(self, invoice: Invoice) -> bool:
        can_create = False
        error_msg_prefix = 'Correction request not created - '
        if not invoice:
            messages.add_message(
                self.request, messages.ERROR, f'{error_msg_prefix} missing invoice'
            )
        elif invoice.pending_correction_requests.exists():
            messages.add_message(
                self.request,
                messages.ERROR,
                f'{error_msg_prefix} pending request already exists',
            )
        elif invoice.get_total_net() == 0:
            messages.add_message(
                self.request, messages.ERROR, f'{error_msg_prefix} zero value invoice'
            )
        else:
            can_create = True

        return can_create

    def create_correction_request(self, last_invoice: Invoice):
        correction_request = CSCorrectionRequest.objects.create(
            issuer=self.request.user,
            status=CSCorrectionRequestStatus.STATUS_NEW,
            correction_amount_gross=self.refund_amount,
            correction_context=self.elements,
            invoice=last_invoice,
            tag=InvoiceItemTag.DISCOUNT_QUALITY_DISSATISFACTION.value,
            discount_tag=self.refund_reason,
            complaint=self.complaint,
        )
        strategy = CorrectionRequestAmountStrategy(correction_request)
        strategy.prepare_correction_request()

    @classmethod
    def add_note_about_deprecated_elements(cls, orders):
        note = (
            f'Zamówienia w razie możliwości wysłać razem: '
            f'{", ".join([str(order.id) for order in orders])}.'
            f'Jeśli reklamacje są produkowane u innych producentów, wysłać '
            f'oddzielnie, w tym samym dniu.'
        )
        for order in orders:
            order.set_order_notes(note)
            OrderRefreshEvent(order)


class PostCreateComplaintService(PostSaveComplaintServiceMixin):
    def run(self):
        """
        1. Update express replacement
        2. Create production complaint if reproduction = True
        3. Create ComplaintService
        4. Create ComplaintCosts
        5. Update complaint costs
        6. Process correction request
        7. Add message
        """
        if not self.complaint.is_verified:
            notify_about_complaint_to_verification(
                self.complaint.id, self.complaint.conversation_link
            )
            send_email_to_gala_about_complaint_creation(self.complaint)
            return
        message = self.message
        message_level = messages.SUCCESS

        express_added = self.update_express_replacement()
        if self.complaint.has_deprecated_elements:
            message = '{} New drawer type'.format(message)
            message_level = messages.ERROR
        if express_added:
            message = f'{self.message} EXPRESS REPLACEMENT POSSIBLE'
            message_level = messages.ERROR
        reproduction_logistic_order = self.process_to_production()
        self.create_service_for_complaint(reproduction_logistic_order)
        self.update_complaint_costs()
        self.process_correction_request()
        self.send_email_about_complaint_creation_by_assembly_team()
        messages.add_message(self.request, message_level, message)

    def process_to_production(self) -> Optional[LogisticOrderDTO]:
        if not self.complaint.reproduction:
            return None
        return self.complaint.process_complaint_to_production()

    def create_service_for_complaint(self, reproduction_logistic_order) -> None:
        if self.complaint.assembly_team_intervention:
            ComplaintAssemblyTeamInterventionRequiredEvent(
                self.complaint,
                reproduction_logistic_order.id if reproduction_logistic_order else None,
            )

    def send_email_about_complaint_creation_by_assembly_team(self):
        if self.complaint.created_by_assembly_team:
            from complaints.tasks import send_email_reproduction_created

            send_email_reproduction_created.delay(
                self.complaint.reproduction_order.id,
            )


@dataclass
class PostUpdateComplaintsService(PostSaveComplaintServiceMixin):
    request: HttpRequest
    complaint: Complaint
    message: str

    elements: dict
    refund_amount: Decimal
    refund: bool
    refund_reason: str

    is_refund_added: bool
    reproduction_elements_changed: bool
    is_reproduction_added: bool

    def run(self):
        """
        1. Update express replacement
        2. Update production complaint if reproduction = True
        3. Update service ComplaintService
        4. Update ComplaintCosts
        5. Update complaint costs
        6. Create correction request if needed
        7. Add message
        """
        message = self.message
        message_level = messages.SUCCESS

        if not self.complaint.is_verified:
            notify_about_complaint_to_verification(
                self.complaint.id, self.complaint.conversation_link
            )
            return
        express_added = self.update_express_replacement()
        if express_added:
            message = f'{self.message} EXPRESS REPLACEMENT POSSIBLE'
            message_level = messages.ERROR
        reproduction_logistic_order = self.update_or_create_production_complaint()
        self.update_or_create_service_for_complaint(reproduction_logistic_order)

        self.update_complaint_costs()
        self.process_correction_request()
        messages.add_message(self.request, message_level, message)

    def update_or_create_production_complaint(self) -> Optional[LogisticOrderDTO]:
        reproduction_logistic_order = None
        if self.complaint.reproduction:
            if not self.complaint.reproduction_product:
                # case when reproduction was added to complaint
                reproduction_logistic_order = (
                    self.complaint.process_complaint_to_production()
                )
            else:
                reproduction_logistic_order = (
                    self.complaint.update_production_complaint()
                )
        return reproduction_logistic_order

    def update_or_create_service_for_complaint(
        self, reproduction_logistic_order
    ) -> Optional[dict]:
        complaint_service = None
        if self.complaint.assembly_team_intervention:
            reproduction_logistic_order_id = (
                reproduction_logistic_order.id if reproduction_logistic_order else None
            )
            if self.complaint.complaintservice and self.is_reproduction_added:
                complaint_service = ComplaintChangeToReproductionServiceEvent(
                    self.complaint, reproduction_logistic_order_id
                )
            elif not self.complaint.complaintservice:
                complaint_service = ComplaintAssemblyTeamInterventionRequiredEvent(
                    self.complaint, reproduction_logistic_order_id
                )
            elif self.reproduction_elements_changed and not self.is_reproduction_added:
                complaint_service = ComplaintUpdateShippingPriceServiceEvent(
                    self.complaint, reproduction_logistic_order_id
                )
            elif self.is_reproduction_added and not self.complaint.complaintservice:
                complaint_service = ComplaintAssemblyTeamInterventionRequiredEvent(
                    self.complaint, reproduction_logistic_order_id
                )

        else:
            if self.complaint.complaintservice:
                ComplaintServiceDeleteEvent(self.complaint.complaintservice.id)
        self.invalidate_complaintservice()
        ComplaintRefreshEvent(self.complaint)
        return complaint_service

    def update_complaint_costs(self) -> None:
        if self.is_refund_added:
            self.complaint.complaint_costs.refund_amount = self.refund_amount
            self.complaint.complaint_costs.save(update_fields=['refund_amount'])
        super().update_complaint_costs()

    def invalidate_complaintservice(self):
        complaintservice = 'complaintservice'
        if complaintservice in self.complaint.__dict__:
            self.complaint.__dict__.pop(complaintservice, None)

    def process_correction_request(self):
        if self.is_refund_added:
            super().process_correction_request()


def send_email_to_gala_about_complaint_creation(complaint: Complaint):
    if settings.IS_LOCAL:
        return
    logistic_order = complaint.product.get_logistic_order()
    delivered_date = logistic_order.delivered_date if logistic_order else ''
    body = (
        f'Produkt: {complaint.product_id}, \n'
        f'Zamówienie: {complaint.product.order_id}, \n'
        f'Produkt dostarczony: {delivered_date}, \n'
        f'Reklamacja zgłoszone: {complaint.reported_date}, \n'
        f'Powód reklamacji: '
        f'{complaint.typical_issues.name if complaint.typical_issues.name else "-" },\n'
        f'Powód rabatu: {complaint.refund_reason}, \n'
        f'Dodatkowe informacje: {complaint.additional_info}, \n'
        f'Uszkodzone elementy: {complaint.get_elements_description()}, \n'
    )
    message = EmailMessage(
        subject=f'Zgłoszono nową reklamację {complaint.id}',
        body=body,
        from_email=settings.SOTTY_COMPLAINT_EMAIL_GROUP,
        to=settings.GALA_COMPLAINT_RECIPIENTS,
    )
    for photo in complaint.sotty_photos.all():
        message.attach(filename=photo.file_name, content=photo.photo.read())
    message.send(fail_silently=True)
