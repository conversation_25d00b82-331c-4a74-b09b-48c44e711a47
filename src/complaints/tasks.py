from datetime import timedelta

from django.conf import settings
from django.utils import timezone

from celery import shared_task
from celery.utils.log import get_task_logger

from complaints.models import (
    Complaint,
    ComplaintImage,
)
from complaints.services.complaint_photos import create_zipfile_with_complaint_photos
from mailing.templates import (
    NotifyClientReproductionCreated,
    NotifyLogisticAbortingComplaintDone,
    NotifyLogisticDamageFormReady,
)
from user_profile.models import RetargetingBlacklistToken

task_logger = get_task_logger('celery_task')

VERTICAL_SPARE_PARTS_LEVEL_ALERT = 20


@shared_task
def clean_orphaned_images():
    cleanup_threshold = timezone.now() - timedelta(
        seconds=settings.CONTACT_FILE_CLEANUP_TIME
    )
    ComplaintImage.objects.filter(
        customer_contact=None,
        created__lt=cleanup_threshold,
    ).delete()


@shared_task
def update_complaint_cost(complaint_id: int) -> None:
    complaint = Complaint.objects.get(id=complaint_id)
    complaint.update_complaint_cost()


@shared_task
def notify_logistic_about_aborting_done_complaint_task(order_id):
    for logistic_department, logistic_admin_email in settings.LOGISTIC_RECIPIENTS:
        NotifyLogisticAbortingComplaintDone(
            to_address=logistic_admin_email,
            data_html={
                'order_id': order_id,
            },
        ).send()


@shared_task
def notify_logistic_about_damage_form_ready(damage_form_id, order_id):
    for _, logistic_admin_email in settings.LOGISTIC_RECIPIENTS_DAMAGE_FORM:
        NotifyLogisticDamageFormReady(
            to_address=logistic_admin_email,
            data_html={
                'damage_form_id': damage_form_id,
                'order_id': order_id,
            },
            topic_variables={'order_id': order_id},
        ).send()


@shared_task
def send_mail_with_complaint_photos_task(complaint_ids: list[int], email: str):
    complaints = Complaint.objects.filter(id__in=complaint_ids).prefetch_related(
        'photos'
    )
    report_file = create_zipfile_with_complaint_photos(complaints)
    report_file.send_as_email_attachment(
        emails=[
            email,
        ],
        subject='Damages for carriers',
    )


@shared_task
def send_email_reproduction_created(order_id: int) -> None:
    from orders.models import Order

    order = Order.objects.get(id=order_id)

    NotifyClientReproductionCreated(
        to_address=order.email,
        data_html={
            'order_id': order.id,
            'user': order.first_name,
            'blacklist_token': (
                RetargetingBlacklistToken.get_or_create_for_email(
                    email=order.email
                ).token
            ),
        },
    ).send()
