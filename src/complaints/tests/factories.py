import datetime

from django.db.models.signals import post_save

import factory

from factory import fuzzy


class TypicalIssuesFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = 'complaints.TypicalIssues'


class AreaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = 'complaints.Area'


class ComplaintTypeFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = 'complaints.ComplaintType'


class ResponsibilityFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = 'complaints.Responsibility'


@factory.django.mute_signals(post_save)
class ComplaintFactory(factory.django.DjangoModelFactory):
    owner = factory.SubFactory('user_profile.tests.factories.UserFactory')
    product = factory.SubFactory('producers.tests.factories.ProductFactory')
    reporter = factory.SubFactory('user_profile.tests.factories.UserFactory')

    reported_date = fuzzy.FuzzyDate(start_date=datetime.date(2020, 1, 1))
    complaint_costs = factory.SubFactory(
        'complaints.tests.factories.ComplaintCostsFactory'
    )

    class Meta:
        model = 'complaints.Complaint'


class ComplaintCostsFactory(factory.django.DjangoModelFactory):
    currency = 'EUR'

    class Meta:
        model = 'complaints.ComplaintCosts'


class CustomerContactFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = 'complaints.CustomerContact'


class ComplaintImageFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = 'complaints.ComplaintImage'
