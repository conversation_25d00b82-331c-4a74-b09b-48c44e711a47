import pytest

from complaints.admin_actions import generate_complaint_report
from complaints.models import Complaint


@pytest.mark.django_db
@pytest.mark.nbp
class TestComplaintAdmin:
    model_class = Complaint

    def test_generate_complaint_report(self, complaint_factory):
        complaint_factory(
            elements={
                'elements': ['h13 - BOX:6'],
                'features': ['test'],
                'other': ['test'],
                'fittings': {'fitting_slide_indaux_right-rail-drawer-350': 1},
            }
        )
        complaints = Complaint.objects.all()

        response = generate_complaint_report({}, {}, queryset=complaints)

        assert response['Content-Type'] == 'text/csv'
