from unittest.mock import patch

import pytest

from complaints.exceptions import ComplaintNotReproductionException
from orders.enums import OrderStatus
from orders.models import Order
from producers.choices import ProductStatus


@pytest.mark.django_db
@pytest.mark.nbp
@patch('producers.tasks.generate_product_files')
@patch(
    'producers.product_updater.ProductSerializationUpdater.update_product_serialization'
)
@patch('complaints.services.create_related_objects.ComplaintPushedToProductionEvent')
class TestComplaintsOrderReplacementFlow:
    @pytest.fixture(autouse=True)
    def mock_external_dependencies(self, mocker):
        mocker.patch(
            'complaints.models.Complaint.get_reproduction_time_calculated',
            return_value=1,
        )

    def test_order_created(
        self,
        complaint_pushed_to_production_event,
        mock_update_product_serialization,
        mock_generate_product_files,
        complaint_fixture,
        django_capture_on_commit_callbacks,
        logistic_order_dto,
    ):
        # TODO split assertion into smaller ones
        complaint_pushed_to_production_event.return_value.logistic_order = (
            logistic_order_dto
        )
        objects_count_before_send_complaint_to_production = Order.objects.count()
        with django_capture_on_commit_callbacks(execute=True) as callbacks:
            complaint_fixture.process_complaint_to_production()

        assert (
            Order.objects.count()
            == objects_count_before_send_complaint_to_production + 1
        )
        complaint_order = complaint_fixture.reproduction_product.order
        assert complaint_order == complaint_fixture.reproduction_order
        assert complaint_order.status == OrderStatus.IN_PRODUCTION
        assert complaint_order.items.count() == 1
        assert complaint_fixture.id == complaint_order.get_complaint_related().id
        assert complaint_fixture.reproduction is True
        assert complaint_order.id == complaint_fixture.reproduction_product.order_id
        for field in Order.ADDRESS_FIELDS + (
            'order_notes',
            'notes',
            'cs_notes',
            'assembly',
        ):
            if field == 'order_notes':
                assert complaint_order.order_notes == ''
            else:
                assert getattr(complaint_order, field) == getattr(
                    complaint_order.parent_order, field
                )
        assert len(callbacks) == 2
        assert mock_generate_product_files.delay.call_count == 1
        assert mock_update_product_serialization.call_count == 1

    def test_product_created(
        self,
        mock_complaint_pushed_to_production_event,
        mock_update_product_serialization,
        mock_generate_product_files,
        complaint_fixture,
        logistic_order_dto,
    ):
        mock_complaint_pushed_to_production_event.return_value.logistic_order = (
            logistic_order_dto
        )

        complaint_fixture.process_complaint_to_production()
        product = complaint_fixture.reproduction_product
        assert product.status == ProductStatus.NEW
        assert product.priority == complaint_fixture.product.priority
        assert product.order == complaint_fixture.reproduction_order

    def test_logistic_order_to_be_shipped_created(
        self,
        mock_complaint_pushed_to_production_event,
        mock_update_product_serialization,
        mock_generate_product_files,
        logistic_order_dto,
        complaint_fixture,
    ):

        mock_complaint_pushed_to_production_event.return_value.logistic_order = (
            logistic_order_dto
        )

        logistic_order_order = complaint_fixture.process_complaint_to_production()

        assert logistic_order_order == logistic_order_dto
        assert complaint_fixture.reproduction_order.status == OrderStatus.IN_PRODUCTION

    def test_send_complaint_to_production_assembly_team_intervention(
        self,
        mock_complaint_pushed_to_production_event,
        mock_update_product_serialization,
        mock_generate_product_files,
        complaint_fixture,
        logistic_order_dto,
    ):
        mock_complaint_pushed_to_production_event.return_value.logistic_order = (
            logistic_order_dto
        )

        complaint_fixture.assembly_team_intervention = True
        complaint_fixture.save()

        complaint_fixture.process_complaint_to_production()
        new_order_created = complaint_fixture.reproduction_product.order
        assert 'Assembly team intervention' in new_order_created.order_notes

    def test_complaint_not_reproduction_not_processed_to_production(
        self,
        mock_complaint_pushed_to_production_event,
        mock_update_product_serialization,
        mock_generate_product_files,
        complaint_fixture,
    ):
        complaint_fixture.reproduction = False
        complaint_fixture.save()
        with pytest.raises(ComplaintNotReproductionException):
            complaint_fixture.process_complaint_to_production()


@pytest.mark.django_db
@pytest.mark.nbp
class TestProcessComplaintExpressReplacement:
    @patch(
        'producers.product_updater.ProductSerializationUpdater.update_product_serialization'
    )
    @patch('producers.tasks.generate_product_files')
    @patch(
        'complaints.models.Complaint.get_reproduction_time_calculated', return_value=1
    )
    @patch(
        'complaints.services.create_related_objects.ComplaintPushedToProductionEvent'
    )
    def test_complaint_express_replacement_to_be_shipped_created(
        self,
        mock_complaint_pushed_to_production_event,
        mock_get_reproduction_time_calculated,
        mock_update_product_serialization,
        mock_generate_product_files,
        complaint_fixture,
        logistic_order_dto,
    ):
        mock_complaint_pushed_to_production_event.return_value.logistic_order = (
            logistic_order_dto
        )

        complaint_fixture.reproduction = True
        complaint_fixture.express_replacement = True
        complaint_fixture.save()

        logistic_order = complaint_fixture.process_complaint_to_production()
        assert logistic_order == logistic_order_dto

    @patch(
        'complaints.services.create_related_objects.ComplaintPushedToProductionEvent'
    )
    @patch(
        'producers.product_updater.ProductSerializationUpdater.update_product_serialization'
    )
    @patch('producers.tasks.generate_product_files')
    @patch(
        'complaints.models.Complaint.get_reproduction_time_calculated', return_value=1
    )
    def test_process_complaint_to_production_when_complaint_express_replacement_should_generate_complaintpushedtoproduction_event(
        self,
        mock_get_reproduction_time_calculated,
        mock_generate_product_files,
        mock_update_product_serialization,
        mock_complaintpushedtoproductionevent,
        complaint_fixture,
        logistic_order_dto,
    ):
        complaint_fixture.reproduction = True
        complaint_fixture.express_replacement = True
        complaint_fixture.save()

        mock_complaintpushedtoproductionevent.return_value.logistic_order = (
            logistic_order_dto
        )
        complaint_fixture.process_complaint_to_production()

        mock_complaintpushedtoproductionevent.assert_called_once_with(complaint_fixture)

    @patch(
        'producers.product_updater.ProductSerializationUpdater.update_product_serialization'
    )
    @patch('producers.tasks.generate_product_files')
    @patch(
        'complaints.models.Complaint.get_reproduction_time_calculated', return_value=1
    )
    @patch(
        'complaints.services.create_related_objects.ComplaintPushedToProductionEvent'
    )
    def test_complaint_express_replacement__is_fittings_only(
        self,
        complaint_pushed_to_production_event,
        mock_get_reproduction_time_calculated,
        mock_update_product_serialization,
        mock_generate_product_files,
        complaint_fixture,
        logistic_order_dto,
    ):
        complaint_pushed_to_production_event.return_value.logistic_order = (
            logistic_order_dto
        )

        complaint_fixture.reproduction = True
        complaint_fixture.express_replacement = True
        complaint_fixture.fittings_only = True
        complaint_fixture.save()
        complaint_fixture.elements = {
            'fittings': ['fitting_connector_00_modular-halfblocking']
        }
        complaint_fixture.process_complaint_to_production()
        assert complaint_fixture.reproduction_order

    @patch(
        'producers.product_updater.ProductSerializationUpdater.update_product_serialization'
    )
    @patch('producers.tasks.generate_product_files')
    @patch(
        'complaints.models.Complaint.get_reproduction_time_calculated', return_value=1
    )
    @patch(
        'complaints.services.create_related_objects.ComplaintPushedToProductionEvent'
    )
    def test_complaint_express_replacement__not_fittings_only(
        self,
        mock_complaint_pushed_to_production_event,
        mock_get_reproduction_time_calculated,
        mock_update_product_serialization,
        mock_generate_product_files,
        complaint_fixture,
        logistic_order_dto,
    ):
        mock_complaint_pushed_to_production_event.return_value.logistic_order = (
            logistic_order_dto
        )

        complaint_fixture.reproduction = True
        complaint_fixture.express_replacement = True
        complaint_fixture.save()
        complaint_fixture.elements = {
            'fittings': ['fitting_connector_00_modular-halfblocking'],
            'elements': {'C': 1},
        }
        complaint_fixture.process_complaint_to_production()
        assert complaint_fixture.reproduction_product
        assert complaint_fixture.reproduction_order


@pytest.mark.django_db
@pytest.mark.nbp
@patch(
    'producers.product_updater.ProductSerializationUpdater.update_product_serialization'
)
@patch('producers.tasks.generate_product_files')
@patch('complaints.models.Complaint.get_reproduction_time_calculated', return_value=1)
class TestUpdateComplaintReproductionFlow:
    @pytest.fixture(autouse=True)
    def mock_external_update_dependencies(self, mocker, logistic_order_dto):
        mock_complaintpushedtoproductionevent = mocker.patch(
            'complaints.services.create_related_objects.ComplaintPushedToProductionEvent'
        )

        mock_complaintpushedtoproductionevent.return_value.logistic_order = (
            logistic_order_dto
        )

    def test_complaint_updated(
        self,
        mock_get_reproduction_time_calculated,
        mock_update_product_serialization,
        mock_generate_product_files,
        complaint_fixture,
    ):
        complaint_fixture.process_complaint_to_production()
        product = complaint_fixture.reproduction_product
        assert product.status == ProductStatus.NEW
        assert product.priority == complaint_fixture.product.priority
        assert product.order == complaint_fixture.reproduction_order

    def test_product_updated(
        self,
        mock_get_reproduction_time_calculated,
        mock_update_product_serialization,
        mock_generate_product_files,
        complaint_fixture,
    ):
        complaint_fixture.process_complaint_to_production()
        product = complaint_fixture.reproduction_product
        assert product.status == ProductStatus.NEW
        assert product.priority == complaint_fixture.product.priority
        assert product.order == complaint_fixture.reproduction_order

    def test_only_update_already_created_reproduction_product(
        self,
        mock_get_reproduction_time_calculated,
        mock_update_product_serialization,
        mock_generate_product_files,
        complaint_fixture,
    ):
        complaint_fixture.process_complaint_to_production()
        product = complaint_fixture.reproduction_product
        assert product.status == ProductStatus.NEW
        assert product.priority == complaint_fixture.product.priority
        assert product.order == complaint_fixture.reproduction_order

    def test_logistic_order_serialized_product_updated(
        self,
        mock_get_reproduction_time_calculated,
        mock_update_product_serialization,
        mock_generate_product_files,
        complaint_fixture,
    ):
        complaint_fixture.process_complaint_to_production()
        product = complaint_fixture.reproduction_product
        assert product.status == ProductStatus.NEW
        assert product.priority == complaint_fixture.product.priority
        assert product.order == complaint_fixture.reproduction_order
