import io

from random import choice

from django.contrib.auth.models import User
from django.urls import reverse

import pytest

from PIL import Image
from rest_framework import status


class ContactFormTestCase:
    view_name = 'customercontact-list'
    url = reverse(view_name)

    def test_create(self, client, user, complaint_image_factory):
        client.force_authenticate(user=user)

        test_image = complaint_image_factory(owner=user)
        test_image2 = complaint_image_factory(owner=user)

        data = {
            'first_name': 'Testname',
            'email': '<EMAIL>',
            'message': 'text message',
            'topic': 'order_status',
            'order': None,
            'missing_elements': False,
            'missing_elements_desc': '',
            'damaged': {
                'packaging': False,
                'shelf_elements': False,
                'reported_to_delivery_company': False,
                'images': [test_image.id, test_image2.id],
                'damages_description': '',
            },
        }
        response = client.post(self.url, data=data, format='json')
        assert response.status_code == status.HTTP_201_CREATED
        test_image.refresh_from_db()
        customer_contact = test_image.customer_contact
        assert customer_contact.first_name == data['first_name']
        assert customer_contact.email == data['email']
        assert customer_contact.message == data['message']
        assert customer_contact.topic == data['topic']
        assert customer_contact.has_missing_elements == data['missing_elements']
        assert (
            customer_contact.missing_elements_description
            == data['missing_elements_desc']
        )
        assert customer_contact.has_damaged_packaging == data['damaged']['packaging']

    def test_different_owenr_image_error(self, client, user, complaint_image_factory):
        client.force_authenticate(user=user)
        new_guest_user = User.objects.create_user(
            ''.join(choice('0123456789ABCDEF!@#$%^&*()&^') for _ in range(29))
        )

        image = complaint_image_factory(owner=new_guest_user)
        data = {
            'first_name': 'Testname',
            'email': '<EMAIL>',
            'message': 'text message',
            'topic': 'order_status',
            'order': None,
            'missing_elements': False,
            'missing_elements_desc': '',
            'damaged': {
                'packaging': False,
                'shelf_elements': False,
                'reported_to_delivery_company': False,
                'images': [image.id],
                'damages_description': '',
            },
        }
        response = client.post(self.url, data=data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            'image_owner' in response.data['error_codes']['damaged']['non_field_errors']
        )


class ContactImageUploadTestCase:
    view_name = 'complaintimage-list'
    url = reverse(view_name)

    @pytest.fixture(autouse=True)
    def set_up(self, user, client):
        self.guest_user = user
        client.force_authenticate(user=self.guest_user)
        self.client = client

    def test_create(self):
        image_file = io.BytesIO()
        image = Image.new('RGB', size=(25, 25), color=(0, 0, 0))
        image.save(image_file, 'jpeg')
        image_file.name = 'test.jpeg'
        image_file.seek(0)

        data = {'image': image_file, 'image_type': 'damaged_elements'}
        response = self.client.post(self.url, data=data, format='multipart')

        assert response.status_code == status.HTTP_201_CREATED
        assert 'id' in response.data

    def test_image_type_validation(self):
        image_file = io.BytesIO()
        image = Image.new('RGB', size=(25, 25), color=(0, 0, 0))
        image.save(image_file, 'jpeg')
        image_file.name = 'test.svg'
        image_file.seek(0)

        data = {'image': image_file, 'image_type': 'damaged_elements'}
        response = self.client.post(self.url, data=data, format='multipart')

        assert response.status_code == status.HTTP_400_BAD_REQUEST
