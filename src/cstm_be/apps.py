from django.contrib.admin.apps import AdminConfig


class CSTMAdminConfig(AdminConfig):
    default_site = 'admin_customization.sites.SitePlus'

    def ready(self):
        """Monkey patch ``django.contrib.admin.site`` to lazy loaded object."""
        from django.contrib import admin

        from cstm_be.admin import DefaultAdminSite

        admin.site = DefaultAdminSite()
        super().ready()
