import environ

APPS_DIR = environ.Path(__file__) - 4
BASE_DIR = str(APPS_DIR)
ROOT_PATH = str(APPS_DIR)


# Load OS environment variables
env = environ.Env()
# .env file, should load only in development environment
READ_DOT_ENV_FILE = env.bool('DJANGO_READ_DOT_ENV_FILE', default=False)
if READ_DOT_ENV_FILE:
    # OS environment variables have precedence over variables defined in the
    # `.env` file.
    env_file = str(APPS_DIR.path('.env'))
    env.read_env(env_file)
    print('The "{}" env file has been loaded..'.format(env_file))

STATIC_URL = env('STATIC_URL', default='/r_static/')

# ENVIRONMENT
IS_TESTING = False
IS_DEV = False
IS_PRODUCTION = False
IS_LOCAL = False


# SITES
SITE_ID = env.int('DJANGO_SITE_ID', default=1)
SITE_URL = env.str('SITE_URL', default='https://tylko.com')


# VERSION CONTROL
GIT_BRANCH = env.str('GIT_BRANCH', default=None)
GIT_COMMIT = env.str('GIT_COMMIT', default=None)
