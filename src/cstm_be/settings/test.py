import os
import pwd

from .base import *

# DEBUG
DEBUG = False


# SECRET KEY
SECRET_KEY = env.str(
    'DJANGO_SECRET_KEY',
    default='test@wwp+)=fxtqj+ulrdtndguod3%tho1un^hl@q4c%fa+3e6wo_@',
)


# FIXTURES
FIXTURE_DIRS = [
    str(APPS_DIR.path('fixtures/')),
]

# DATABASES
_username = getattr(pwd.getpwuid(os.getuid()), 'pw_name', 'cstm')
DATABASES['default']['TEST'] = {
    'NAME': 'testdb_{}'.format(env.str('PROJECT_NAME', default=_username)),
}

# TEMPLATES
TEMPLATES[0]['OPTIONS']['debug'] = DEBUG
TEMPLATES[0]['OPTIONS']['loaders'] = [
    'django.template.loaders.filesystem.Loader',
    'django.template.loaders.app_directories.Loader',
]

# MEDIA
DEFAULT_FILE_STORAGE = 'django.core.files.storage.FileSystemStorage'
PRIVATE_FILE_STORAGE = 'django.core.files.storage.FileSystemStorage'
MEDIA_ROOT = str(APPS_DIR('media'))
MEDIA_URL = '/uploaded/'

# CACHES
CACHES = {
    'default': {
        'BACKEND': 'custom.dummy_cache.CstmDummyCache',
    },
}


# EMAILS
EMAIL_HOST = 'localhost'
EMAIL_PORT = 1025
EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'


# GLOBALIZATION (I18N/L10N)
USE_TZ = True


# HTTP
ALLOWED_HOSTS = ['*']


# STATIC FILES
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'


# AUTHENTICATION
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]


# WEBPACK LOADER
WEBPACK_LOADER['DEFAULT'][
    'LOADER_CLASS'
] = 'custom.webpack_loader.CstmMockWebpackLoader'
WEBPACK_LOADER['DEFAULT']['CACHE'] = not DEBUG
WEBPACK_LOADER['DEFAULT']['STATS_FILE'] = str(
    APPS_DIR.path('webpack-stats-test.json'),
)
WEBPACK_LOADER['ES5']['CACHE'] = not DEBUG
WEBPACK_LOADER['ES5']['STATS_FILE'] = str(
    APPS_DIR.path('webpack-stats-test.json'),
)


# REST FRAMEWORK
REST_FRAMEWORK['DEFAULT_THROTTLE_RATES'] = {
    'anon': '10/s',
    'user': '30/s',
    'heavy_throttle': '50/hour',
    'production_files': '50/s',
}


# DJANGO TEST MIGRATIONS
# install Django check that produces warnings for all migrations with
# autogenerated names
INSTALLED_APPS.append('django_test_migrations.contrib.django_checks.AutoNames')


# LOCAL GENERAL SETTINGS
IS_TESTING = True
IS_DEV = False
IS_PRODUCTION = False


# MAILING
MAILING_FLOW_PERIOD = 2


# LOCAL GENERAL SETTINGS
VAT_VALIDATOR = 'orders.tests.utils.MockVatValidator'

# DYNAMIC DELIVERY
ACCOUNTING_ORDER_ABORTED = [('Accounting', '<EMAIL>')]

# CELERY
CELERY_TASK_ALWAYS_EAGER = True
CELERY_BROKER_URL = 'memory://'
CELERY_RESULT_BACKEND = 'cache+memory://'

ABTESTS_CACHE_TTL_SECONDS = 0

# BRAZE
PROCESS_TRANSACT_FLOWS_ON_BE = False
BRAZE_SMS_SUBSCRIPTION_GROUPS = {}
LOGISTIC_MOCK_REQUEST_STRATEGY = True
