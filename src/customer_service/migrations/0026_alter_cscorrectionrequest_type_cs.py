# Generated by Django 3.2.18 on 2023-05-05 12:02

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('customer_service', '0025_improve_b2b_reward'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cscorrectionrequest',
            name='type_cs',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, 'Amount'),
                    (1, 'Items'),
                    (2, 'Address'),
                    (3, 'Neutralization'),
                    (4, 'Normal VAT to B2B'),
                    (5, 'B2B to normal VAT'),
                    (6, 'Add assembly'),
                    (7, 'Recalculation'),
                    (8, 'Switch item'),
                    (9, 'Free return'),
                ],
                default=0,
            ),
        ),
    ]
