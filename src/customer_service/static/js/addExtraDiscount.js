const addAsterisk = (label) => {
    if (!label.querySelector('.asteriskField')) {
        const asterisk = document.createElement('span');
        asterisk.className = 'asteriskField';
        asterisk.textContent = '*';
        label.appendChild(asterisk);
    }
};

const removeAsterisk = (label) => {
    const asterisk = label.querySelector('.asteriskField');
    if (asterisk) {
        label.removeChild(asterisk);
    }
};

document.addEventListener('DOMContentLoaded', () => {
    const toggleFields = (dropdownId, fieldMappings) => {
        const dropdown = document.querySelector(`#${dropdownId}`);

        const handleToggle = () => {
            const selectedValue = dropdown.value;

            fieldMappings.forEach(({value, inputId}) => {
                const field = document.querySelector(`#div_${inputId}`);
                const input = document.querySelector(`#${inputId}`);
                const label = document.querySelector(`label[for="${inputId}"]`);

                if (selectedValue === value) {
                    field.style.display = 'block';
                    input.setAttribute('required', 'required');
                    addAsterisk(label);
                } else {
                    field.style.display = 'none';
                    input.removeAttribute('required');
                    removeAsterisk(label);
                }
            });
        };

        dropdown.addEventListener('change', handleToggle);
        handleToggle();
    };

    toggleFields('id_kind_of', [
        {value: 'code', inputId: 'id_code'},
        {value: 'percentage', inputId: 'id_percentage'},
    ]);
});
