{% extends "admin/change_list.html" %}

{% block extrahead %}
    {{ block.super }}
    <script type="text/javascript">
        document.addEventListener("DOMContentLoaded", function (event) {
            const result_list = document.getElementById('result_list')
            for (let row of result_list.rows) {
                if (row.cells[11].textContent !== '-') {
                    row.style['background-color'] = "rgb(168,219,168)"
                }
            }
        });
    </script>
{% endblock extrahead %}
