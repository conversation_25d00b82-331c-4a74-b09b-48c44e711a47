{% load defaulttags %}
{% load invoice_tags %}
{% if corrections %}
    corrected by:
    {% for correction in corrections %}
        <p>{{ correction.pretty_id }}, {{ correction.total_value}}{{ correction.currency_symbol }}</p>
    {% endfor %}
{% endif %}
<table id="main-details" class="table table-bordered">
    <tr>
        <td id="invoice-details">
        {% if invoice.status == 2 %}
            <h5>PROFORMA </h5>
            <h5>no. {{ invoice.order }}</h5>
        {% else %}
            <h5>Invoice</h5>
            <h5>no. {{invoice.pretty_id}}</h5>
        {% endif %}
            <p><em>Invoice Date: {{invoice.issued_at|date:"d.m.Y"}} Warsaw</em></p>
            <p><em>Sale Date: {{invoice.sell_at|date:"d.m.Y"}} Warsaw</em></p>
            {% if invoice.additional_top != None %}
                <p>{{ invoice.additional_top|safe }}</p>
            {% endif %}
{#            Temporary disabled - it will back when we split assembly service to new invoice #}
{#            {% if invoice.vat_status == 1 %}#}
{#                <p>VAT REVERSE CHARGE UNDER art.44 OF VAT DIRECTIVE 2006/112/ES</p>#}
{#            {% endif %}#}
            {% if invoice.delivery_address.outside_eu %}
                <p>Delivery terms: Incoterms DAP (Delivered at Place)</p>
                <p>Country of origin: Poland</p>
            {% endif %}
        </td>
        {% if invoice.show_both_address == True %}
        <td></td>
        {% endif %}
    </tr>
    <tr>
        <td id="client-details">
        {% if invoice.additional_address_1 %}
            {{ invoice.additional_address_1|safe }}
        {% else %}
            {% if invoice.invoice_address.company_name %}
                <h5>{{invoice.invoice_address.company_name}} <br/> {{invoice.invoice_address.first_name}} {{invoice.invoice_address.last_name}} </h5>
            {% else %}
                <h5>{{invoice.invoice_address.first_name}} {{invoice.invoice_address.last_name}} </h5>
            {% endif %}
            <p>{{invoice.invoice_address.street_address_1}} {{invoice.invoice_address.street_address_2}} </p>
            <p>{{invoice.invoice_address.postal_code}} {{invoice.invoice_address.city}} </p>
            <p>{% country_trans invoice.invoice_address.country %} </p>
            {% if invoice.invoice_address.vat %}
                <p>TAX: <em>{{invoice.invoice_address.vat}}</em></p>
            {% endif %}
        {% endif %}
        </td>
        {% if invoice.show_both_address == True %}
            {% if invoice.invoice_address.additional_address_2  %}
            <td>
            <h5>Delivery Address:</h5>
                {{ invoice.invoice_address.additional_address_2|safe }}
            </td>
            {% else %}
            <td>
            <h5>Delivery Address:</h5>
            {% if invoice.delivery_address.company_name %}
                <h5>{{invoice.delivery_address.company_name}} <br/> {{invoice.delivery_address.first_name}} {{invoice.delivery_address.last_name}} </h5>
            {% else %}
                <h5>{{invoice.delivery_address.first_name}} {{invoice.delivery_address.last_name}} </h5>
            {% endif %}
            <p>{{invoice.delivery_address.street_address_1}} {{invoice.delivery_address.street_address_2}} </p>
            <p>{{invoice.delivery_address.postal_code}} {{invoice.delivery_address.city}} </p>
            <p>{% country_trans invoice.delivery_address.country %} </p>
            {% if invoice.invoice_address.vat %}
                <p>TAX: <em>{{invoice.invoice_address.vat}}</em></p>
            {% endif %}
            </td>
            {% endif %}
    {% endif %}
    </tr>
</table>

<table id="order-items-details" class="inverted-fonts table table-bordered">
    <thead>
        <tr class="table-bar--preview">
            <td class="nowrap">Item Description</td>
            <td>Id</td>
            <td>Quantity</td>
            <td>Net price before discount</td>
            {% if invoice.promo_amount > 0 %}
            <td>Discount value</td>
            {% endif %}
            <td>Net value</td>
            <td>VAT rate</td>
            <td>VAT amount</td>
            {% if invoice.delivery_address.outside_eu %}
                <td>Net weight</td>
                <td>Gross weight</td>
            {% endif %}
            <td>Gross price</td>
        </tr>
    </thead>
    <tbody>
    {% for item in invoice_items.0 %}
        <tr>
            <td>
                {{item.item_name}}
                {% if item.item_type == 0 %}
                    <br/>{{item.item_material}}
                    <br/>{{item.item_dimensions}}
                {% endif %}
            </td>
            <td class="nowrap"><em>{{item.id}}</em></td>
            <td class="nowrap"><em>{{item.quantity}}</em></td>
            <td class="nowrap"><em>{{item.net_price}}{{ invoice.currency_symbol }}</em></td>
            {% if invoice.promo_amount > 0 %}
            <td class="nowrap"><em>{{item.discount_value}}{{ invoice.currency_symbol }}</em></td>
            {% endif %}
            <td class="nowrap"><em>{{item.net_value}}{{ invoice.currency_symbol }}</em></td>
            <td class="nowrap"><em>{% as_percentage item.vat_rate %}%</em></td>
            <td class="nowrap"><em>{{item.vat_amount}}{{ invoice.currency_symbol }}</em></td>
            {% if invoice.delivery_address.outside_eu %}
                <td>{{item.net_weight}} kg</td>
                <td>{{item.gross_weight}} kg</td>
            {% endif %}
            <td class="nowrap"><em>{{item.gross_price}}{{ invoice.currency_symbol }}</em></td>
        </tr>
    {% endfor %}
    {% for item in invoice_items.1 %}
        <tr>
            <td>
                {{item.item_name}}
                {% if item.item_type == 0 %}
                    <br/>{{item.item_material}}
                    <br/>{{item.item_dimensions}}
                {% endif %}
            </td>
            <td class="nowrap"><em>{{item.quantity}}</em></td>
            <td class="nowrap"><em>{{item.net_price}}{{ invoice.currency_symbol }}</em></td>
            {% if invoice.promo_amount > 0 %}
            <td class="nowrap"><em>{{item.discount_value}}{{ invoice.currency_symbol }}</em></td>
            {% endif %}
            <td class="nowrap"><em>{{item.net_value}}{{ invoice.currency_symbol }}</em></td>
            <td class="nowrap"><em>{% as_percentage item.vat_rate %}%</em></td>
            <td class="nowrap"><em>{{item.vat_amount}}{{ invoice.currency_symbol }}</em></td>
            {% if invoice.delivery_address.outside_eu %}
                <td>{{item.net_weight}} kg</td>
                <td>{{item.gross_weight}} kg</td>
            {% endif %}
            <td class="nowrap"><em>{{item.gross_price}}{{ invoice.currency_symbol }}</em></td>
        </tr>
    {% endfor %}
    {% for item in invoice_items.2 %}
        <tr>
            <td>
                {{item.item_name}}
                {% if item.item_type == 0 %}
                    <br/>{{item.item_material}}
                    <br/>{{item.item_dimensions}}
                {% endif %}
            </td>
            <td class="nowrap"><em>{{item.quantity}}</em></td>
            <td class="nowrap"><em>{{item.net_price}}{{ invoice.currency_symbol }}</em></td>
            {% if invoice.promo_amount > 0 %}
            <td class="nowrap"><em>{{item.discount_value}}{{ invoice.currency_symbol }}</em></td>
            {% endif %}
            <td class="nowrap"><em>{{item.net_value}}{{ invoice.currency_symbol }}</em></td>
            <td class="nowrap"><em>{% as_percentage item.vat_rate %}%</em></td>
            <td class="nowrap"><em>{{item.vat_amount}}{{ invoice.currency_symbol }}</em></td>
            {% if invoice.delivery_address.outside_eu %}
                <td>{{item.net_weight}} kg</td>
                <td>{{item.gross_weight}} kg</td>
            {% endif %}
            <td class="nowrap"><em>{{item.gross_price}}{{ invoice.currency_symbol }}</em></td>
        </tr>
    {% endfor %}
    </tbody>
</table>

<table id="order-summary" class="table table-bordered">
    <tbody>
        <tr>
            <td><p>Net value:</p></td>
            <td><p>{{invoice.net_value}}{{ invoice.currency_symbol }}</p></td>
        </tr>
        <tr>
            <td><p>Vat:</p></td>
            <td><p>{{invoice.vat_value}}{{ invoice.currency_symbol }}</p></td>
        </tr>
    </tbody>
    <tfoot>
        <tr>
            <td><h5>Total:</h5></td>
            <td><h5>{{invoice.total_value}}{{ invoice.currency_symbol }}</h5></td>
        </tr>
        {% if invoice.additional_total_text and invoice.additional_total_value %}
        <tr>
            <td style="border-top: 0px;"><h5>{{ invoice.additional_total_text }}</h5></td>
            <td style="border-top: 0px;"><h5>{{ invoice.additional_total_value }}</h5></td>
        </tr>
        {% endif %}
    </tfoot>
</table>
