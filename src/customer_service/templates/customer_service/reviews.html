{% extends "customer_service/base.html" %}
{% load i18n %}
{% load admin_urls %}
{% load activity_tags %}
{% load static %}
{% load humanize %}
{% load admin_tags %}
{% load crispy_forms_tags %}

{% block extrahead %}
    <link href="{% static 'css/bootcards-desktop.css' %}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <script src="{% static 'js/bootcards.min.js' %}"></script>
{% endblock %}


{% block content %}
 <table class="table">
<thead>
<caption>TrustedShops Reviews</caption>
<tr>
    <th>Mark</th>
    <th>Comment</th>
    <th>Email</th>
    <th>Connected customer</th>
</tr>
</thead>
<tbody>
{% for review in ts_reviews %}
    <tr><td>{{ review.mark }}/5</td>
        <td>{{ review.comment }}</td>
        <td>{{ review.email }}</td>
        {% if review.order_owner %}
            <td><a href="{% url 'cs_user_overview' review.order_owner %}" class="btn btn-info">User overview</a></td>
        {% else %}
            <td><a href="" disabled='disabled' class="btn btn-info">User overview</a></td>
        {% endif %}
    </tr>
{% endfor %}</tbody></table>

<table class="table">
<thead>
<caption>TrustPilot Reviews</caption>
<tr>
    <th>Mark</th>
    <th>Comment</th>
    <th>Author</th>
</tr>
</thead>
<tbody>
{% for review in tp_reviews %}
    <tr><td>{{ review.mark }}/5</td>
        <td><i>{{ review.headline }}</i>  {{ review.comment }}</td>
        <td>{{ review.author_name }}</td>
        <!--{% if review.order_owner %}-->
            <!--<td><a href="{% url 'cs_user_overview' review.order_owner %}" class="btn btn-info">User overview</a></td>-->
        <!--{% else %}-->
            <!--<td><a href="" disabled='disabled' class="btn btn-info">User overview</a></td>-->
        <!--{% endif %}-->
    </tr>
{% endfor %}</tbody></table>
{% endblock %}
