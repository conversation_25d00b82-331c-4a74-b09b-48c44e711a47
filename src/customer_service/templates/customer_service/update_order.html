{% extends "customer_service/base.html" %}
{% load i18n %}
{% load admin_urls %}
{% load activity_tags %}
{% load humanize %}
{% load static %}
{% load cs_tags %}
{% load crispy_forms_tags %}

{% block extrahead %}
    <link href="{% static 'css/bootcards-desktop.css' %}" rel="stylesheet">
    <script src="{% static 'js/bootcards.min.js' %}"></script>
{% endblock %}


{% block content %}
    {% show_short_order_overview order %}
    <form action="" method="post">{% csrf_token %}
        {{ form|crispy }}
        <input type="submit" class="btn btn-default" value="Send" />
    </form>
{% endblock %}
