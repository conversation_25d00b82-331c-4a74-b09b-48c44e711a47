import pytest

from orders.enums import OrderStatus


@pytest.mark.django_db(transaction=True)
def test_returning_client_cs(order_factory):
    """
    We treat returning customer
    if there is at least two orders from the same customer
    and those orders are at least Processed to Production.
    After second order is processed to production
    we set previous order as "returning_client"
    """
    email = '<EMAIL>'
    order1 = order_factory(email=email, status=OrderStatus.CART, returning_client=False)
    order1.change_status(status_to_change=OrderStatus.IN_PRODUCTION)
    assert not order1.returning_client

    order2 = order_factory(email=email, status=OrderStatus.CART, returning_client=False)
    order2.change_status(status_to_change=OrderStatus.IN_PRODUCTION)
    assert order2.returning_client

    order1.refresh_from_db()
    assert order1.returning_client
