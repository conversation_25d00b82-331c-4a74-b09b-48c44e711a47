from datetime import timedelta

from django.utils import timezone

from celery import shared_task

from django_mailer import settings as mailer_settings
from django_mailer.engine import send_all
from django_mailer.models import Message


@shared_task
def clear_messages():
    messages = Message.objects.filter(
        date_created__lt=timezone.now() - timedelta(days=30),
        encoded_message__isnull=False,
    ).exclude_invoicing()
    messages.update(encoded_message='')


@shared_task
def send_mail():
    block_size = 500
    send_all(
        block_size,
        backend=mailer_settings.USE_BACKEND,
        devs_only=False,
    )
