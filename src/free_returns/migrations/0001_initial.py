# Generated by Django 1.11.24 on 2020-02-17 21:54
from __future__ import unicode_literals

import django.contrib.postgres.indexes

from django.db import (
    migrations,
    models,
)

import loose_ends.models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='FreeReturn',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'invoice_date',
                    models.DateField(
                        blank=True, db_index=True, default=None, null=True
                    ),
                ),
                (
                    'notification_date',
                    models.DateField(
                        blank=True, db_index=True, default=None, null=True
                    ),
                ),
                (
                    'is_packed',
                    models.Bo<PERSON>anField(verbose_name='Furniture already packed'),
                ),
                (
                    'is_need_packaging',
                    models.BooleanField(verbose_name='Send packaging materials'),
                ),
                (
                    'is_send_asap',
                    models.BooleanField(
                        default=False, verbose_name='Ready to pickup, send courier'
                    ),
                ),
                ('status', models.CharField(max_length=1000)),
                ('reason', models.TextField(blank=True, null=True)),
                ('reason_tag', models.CharField(max_length=255)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True, db_index=True)),
                (
                    'finished_at',
                    models.DateTimeField(
                        blank=True, default=None, editable=False, null=True
                    ),
                ),
                (
                    'aborted_at',
                    models.DateTimeField(
                        blank=True, default=None, editable=False, null=True
                    ),
                ),
                (
                    'tracking_number',
                    models.CharField(
                        blank=True, default=None, max_length=300, null=True
                    ),
                ),
            ],
            options={
                'ordering': ['-aborted_at', '-finished_at', '-updated_at'],
            },
            bases=(loose_ends.models.ModelHasLooseEnd, models.Model),
        ),
        migrations.AddIndex(
            model_name='freereturn',
            index=models.Index(
                fields=['-aborted_at', '-finished_at'],
                name='free_return_aborted_ab7c84_idx',
            ),
        ),
        migrations.AddIndex(
            model_name='freereturn',
            index=models.Index(
                fields=['-updated_at'], name='free_return_updated_227bb2_idx'
            ),
        ),
        migrations.AddIndex(
            model_name='freereturn',
            index=django.contrib.postgres.indexes.BrinIndex(
                fields=['created_at'], name='free_return_created_b693eb_brin'
            ),
        ),
        migrations.AddIndex(
            model_name='freereturn',
            index=django.contrib.postgres.indexes.BrinIndex(
                fields=['updated_at'], name='free_return_updated_af7632_brin'
            ),
        ),
    ]
