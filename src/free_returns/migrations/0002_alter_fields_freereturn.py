# Generated by Django 3.2.9 on 2022-02-15 17:58

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('free_returns', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='freereturn',
            name='invoice_date',
        ),
        migrations.AlterField(
            model_name='freereturn',
            name='notes',
            field=models.TextField(blank=True, default=''),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='freereturn',
            name='reason',
            field=models.TextField(default=''),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='freereturn',
            name='reason_tag',
            field=models.CharField(
                choices=[
                    ('quality', "didn't like quality/changed his mind"),
                    ('look', "didn't like the looks"),
                    ('damaged', 'damaged elements'),
                    ('transport', 'disappointed with transport'),
                    ('assembly_service', 'disappointed with assembly service'),
                    ('assembly', 'hard assembly/production mistakes'),
                    ('design', 'change of design'),
                    ('size_color', 'incorrect size/color'),
                    ('mistake', 'made by mistake'),
                    ('other', 'other'),
                ],
                max_length=255,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name='freereturn',
            name='tracking_number',
            field=models.CharField(blank=True, default='', max_length=300),
            preserve_default=False,
        ),
    ]
