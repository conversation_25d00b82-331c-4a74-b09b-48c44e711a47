from dataclasses import dataclass
from datetime import timedelta

from django.conf import settings
from django.contrib.auth import get_user_model
from django.db.models import Count
from django.utils import timezone

from celery import shared_task
from celery.utils.log import get_task_logger

from customer_service.tasks import send_email_missing_invoice
from customer_service.utils import (
    create_free_return_invoice_correction_request,
    create_neutralize_invoice_correction_request,
)
from free_returns.enums import (
    FreeReturnStatusChoices,
    FreeReturnTransportChoices,
)
from free_returns.models import FreeReturn
from free_returns.tasks_utils import check_tnt_orders_and_set_date
from invoice.choices import (
    InvoiceItemType,
    InvoiceStatus,
)
from invoice.enums import InvoiceItemTag
from invoice.models import Invoice
from orders.models import OrderItem

task_logger = get_task_logger('celery_task')
User = get_user_model()


@shared_task
def check_for_tnt_orders():
    # 90 days is how long the TNT tracking number is suppose to be valid
    max_past_date = (timezone.now() - timedelta(days=90)).date()
    tnt_free_returns = FreeReturn.objects.filter(
        tracking_number__isnull=False,
        transport_method=FreeReturnTransportChoices.TNT.value,
        finished_at__isnull=True,
        notification_date__gte=max_past_date,
    ).order_by('id')
    tnt_free_returns.filter(finished_at__isnull=True).update(
        status=FreeReturnStatusChoices.IN_TRANSPORT
    )
    check_tnt_orders_and_set_date(tnt_free_returns)
    tnt_orders_ids = [tnt_order.id for tnt_order in tnt_free_returns]
    FreeReturn.objects.filter(id__in=tnt_orders_ids, finished_at__isnull=False).update(
        status=FreeReturnStatusChoices.DELIVERED
    )


@shared_task
def check_for_finished_returns():
    free_returns = (
        (
            FreeReturn.objects.filter(
                finished_at__isnull=False,
                status=FreeReturnStatusChoices.DELIVERED,
            )
        )
        .prefetch_related('orderitem_set')
        .order_by('id')
    )
    celery_user = User.objects.get_by_natural_key(settings.CELERY_SUPERUSER_USERNAME)
    for free_return in free_returns:
        order_id = free_return.orders_ids[0]
        invoice = (
            Invoice.objects.filter(
                status__in=[
                    InvoiceStatus.ENABLED,
                    InvoiceStatus.CORRECTING,
                    InvoiceStatus.ENABLED_VAT_REGION_CORRECTION,
                ],
                order_id=order_id,
            )
            .order_by('id')
            .last()
        )
        if not invoice:
            # Case for Klarna when invoice hasn't been generated yet
            send_email_missing_invoice.delay(order_id, free_return.id)
        elif CorrectionRequestFreeReturnHandler.can_create_correction_request(invoice):
            correction_handler = CorrectionRequestFreeReturnHandler(
                free_return, order_id, invoice, celery_user
            )
            correction_handler.create_correction()


@dataclass
class CorrectionRequestFreeReturnHandler:
    free_return: FreeReturn
    order_id: int
    invoice: Invoice
    user: User

    def create_correction(self):
        if (
            self.is_return_for_all_order_items
            and not self.invoice.invoice_items.filter(
                item_type__in=[
                    InvoiceItemType.ASSEMBLY,
                    InvoiceItemType.SERVICE,
                    InvoiceItemType.DELIVERY,
                ]
            ).exists()
        ):
            correction_request = create_neutralize_invoice_correction_request(
                self.invoice,
                self.user,
                None,
                InvoiceItemTag.FREE_RETURN.value,
            )
            self.free_return.correction_request = correction_request
            self.free_return.save()
        else:
            free_return_items = self.free_return.orderitem_set.all()
            invoice_deleted_items = self.invoice.invoice_items.filter(
                order_item_id__in=free_return_items,
                item_type=InvoiceItemType.ITEM,
            )

            correction_request = create_free_return_invoice_correction_request(
                self.invoice,
                self.user,
                invoice_deleted_items,
                self.free_return,
                None,
                InvoiceItemTag.FREE_RETURN.value,
            )
        self.free_return.status = FreeReturnStatusChoices.CORRECTION_REQUEST
        self.free_return.save(update_fields=['status'])

    @classmethod
    def can_create_correction_request(cls, invoice: Invoice) -> bool:
        can_create = False
        if invoice.pending_correction_requests.exists():
            task_logger.error(f'Pending request already exists, invoice: {invoice.id}')
        elif invoice.get_total_net() == 0:
            task_logger.error(f'Unable to neutralize zero value invoice. {invoice.id}')
        else:
            can_create = True

        return can_create

    @property
    def is_return_for_all_order_items(self) -> bool:
        """
        Count and check return order items for past free returns
        and currently processing free return
        """
        total_order_items = OrderItem.objects.filter(order_id=self.order_id).count()
        return_order_items = OrderItem.objects.filter(
            free_return__isnull=False,
            order_id=self.order_id,
        )
        already_returned_items_count = return_order_items.filter(
            free_return__status__gte=FreeReturnStatusChoices.ACCEPTED_CORRECTION
        ).aggregate(returned_items=Count('free_return'))['returned_items']
        currently_return_items_count = return_order_items.filter(
            free_return__status=FreeReturnStatusChoices.DELIVERED,
            free_return=self.free_return,
        ).aggregate(returned_items=Count('free_return'))['returned_items']
        return total_order_items == (
            already_returned_items_count + currently_return_items_count
        )
