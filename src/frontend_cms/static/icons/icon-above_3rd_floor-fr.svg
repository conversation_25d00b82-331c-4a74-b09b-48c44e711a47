<svg xmlns="http://www.w3.org/2000/svg" viewBox="-13524 5477 218 87">
  <defs>
    <style>
      .cls-1, .cls-3, .cls-5 {
        fill: none;
      }

      .cls-1 {
        stroke: transparent;
      }

      .cls-2 {
        fill: #7c7d81;
      }

      .cls-3 {
        stroke: #7c7e81;
        stroke-miterlimit: 10;
        stroke-width: 1.3px;
      }

      .cls-4 {
        stroke: none;
      }
    </style>
  </defs>
  <g id="Group_4981" data-name="Group 4981" transform="translate(-13883 4531)">
    <g id="Rectangle_418" data-name="Rectangle 418" class="cls-1" transform="translate(359 946)">
      <rect class="cls-4" width="218" height="87" rx="6"/>
      <rect class="cls-5" x="0.5" y="0.5" width="217" height="86" rx="5.5"/>
    </g>
    <path id="Path_1027" data-name="Path 1027" class="cls-2" d="M.574-2.379H2.1a1.216,1.216,0,0,0,.434.993,1.651,1.651,0,0,0,1.02.308,1.566,1.566,0,0,0,1-.3.9.9,0,0,0,.381-.729v-.732q0-.721-.768-.721H2.473V-4.811H4.078A.676.676,0,0,0,4.632-5a.78.78,0,0,0,.155-.5v-.65a.773.773,0,0,0-.343-.694A1.508,1.508,0,0,0,3.6-7.078a1.547,1.547,0,0,0-.917.252,1.013,1.013,0,0,0-.39.814H.768a2.247,2.247,0,0,1,.205-.973,2.053,2.053,0,0,1,.577-.732,2.667,2.667,0,0,1,.894-.46A3.9,3.9,0,0,1,3.6-8.338a3.961,3.961,0,0,1,1.14.155,2.649,2.649,0,0,1,.885.442,1.978,1.978,0,0,1,.565.694,2.019,2.019,0,0,1,.2.9,2.222,2.222,0,0,1-.3,1.2A2,2,0,0,1,5.2-4.2a2.277,2.277,0,0,1,1,.732,2.076,2.076,0,0,1,.334,1.248,2.311,2.311,0,0,1-.217,1.014A2.076,2.076,0,0,1,5.7-.451a2.889,2.889,0,0,1-.94.469,4.132,4.132,0,0,1-1.2.164A4.169,4.169,0,0,1,2.385.023a2.818,2.818,0,0,1-.946-.475,2.177,2.177,0,0,1-.636-.8A2.608,2.608,0,0,1,.574-2.379ZM8.2,0V-8.162h5.613v1.324H9.762v2h3.5v1.318h-3.5v2.2h4.055V0ZM18.445,0V-8.162h5.613v1.324H20v2h3.5v1.318H20v2.2h4.055V0Zm1.787-8.965.885-1.459H22.8l-1.33,1.459Zm4.729,2.127V-8.162h6.311v1.324H28.9V0H27.334V-6.838ZM31.74,0l2.783-8.162h1.658L38.953,0H37.271l-.539-1.74H33.949L33.422,0Zm2.613-3.059h1.98l-.943-3.217h-.053Zm5.508-1a6.756,6.756,0,0,1,.214-1.764,3.737,3.737,0,0,1,.653-1.351,2.926,2.926,0,0,1,1.11-.864,3.806,3.806,0,0,1,1.585-.3,3.817,3.817,0,0,1,1.2.179,2.766,2.766,0,0,1,.946.521,2.652,2.652,0,0,1,.647.829,2.729,2.729,0,0,1,.281,1.084H44.936Q44.8-7.02,43.313-7.02a1.811,1.811,0,0,0-1.283.457,1.591,1.591,0,0,0-.492,1.225v2.5a1.679,1.679,0,0,0,.138.683,1.629,1.629,0,0,0,.378.539,1.728,1.728,0,0,0,.565.352,1.955,1.955,0,0,0,.712.126,1.9,1.9,0,0,0,1.263-.4,1.535,1.535,0,0,0,.472-1.245v-.352H43.389V-4.453h3.24V0H45.176l-.035-.744a2.2,2.2,0,0,1-1.957.926,3.142,3.142,0,0,1-1.45-.319,2.945,2.945,0,0,1-1.04-.882,3.942,3.942,0,0,1-.624-1.339A6.542,6.542,0,0,1,39.861-4.055ZM48.41,0V-8.162h5.613v1.324H49.969v2h3.5v1.318h-3.5v2.2h4.055V0ZM.662,11.922a4.7,4.7,0,0,1,.946-3.179A3.38,3.38,0,0,1,4.295,7.662,3.389,3.389,0,0,1,6.984,8.743a4.686,4.686,0,0,1,.949,3.179A4.686,4.686,0,0,1,6.984,15.1a3.389,3.389,0,0,1-2.689,1.081A3.38,3.38,0,0,1,1.608,15.1,4.7,4.7,0,0,1,.662,11.922Zm1.676,1.26A1.567,1.567,0,0,0,2.5,13.9a1.636,1.636,0,0,0,.422.527,1.747,1.747,0,0,0,.615.322,2.64,2.64,0,0,0,.762.105,2.67,2.67,0,0,0,.765-.105,1.744,1.744,0,0,0,.618-.322,1.561,1.561,0,0,0,.574-1.248v-2.52a1.55,1.55,0,0,0-.574-1.248,1.781,1.781,0,0,0-.618-.325,2.6,2.6,0,0,0-.765-.108,2.574,2.574,0,0,0-.762.108,1.785,1.785,0,0,0-.615.325,1.636,1.636,0,0,0-.422.527,1.567,1.567,0,0,0-.158.721Zm7.178-.088V7.838h1.559v5.32a1.8,1.8,0,0,0,.407,1.245,1.558,1.558,0,0,0,1.222.454,1.594,1.594,0,0,0,1.236-.448,1.785,1.785,0,0,0,.41-1.251V7.838H15.9v5.256a3.5,3.5,0,0,1-.223,1.28,2.706,2.706,0,0,1-.636.973,2.809,2.809,0,0,1-1,.618,4.2,4.2,0,0,1-2.663,0,2.888,2.888,0,0,1-1-.612,2.6,2.6,0,0,1-.636-.967A3.577,3.577,0,0,1,9.516,13.094ZM20.771,16V7.838h3.322a2.627,2.627,0,0,1,1.934.63,2.727,2.727,0,0,1,.627,1.989,2.564,2.564,0,0,1-.7,1.954,2.585,2.585,0,0,1-1.852.659H22.33V16Zm1.559-4.248h1.934a.745.745,0,0,0,.589-.205.9.9,0,0,0,.179-.609V9.93a.849.849,0,0,0-.167-.589.805.805,0,0,0-.6-.179H22.33ZM28.1,16V7.838H29.66v6.844h3.457V16Zm6.141-2.906V7.838H35.8v5.32a1.8,1.8,0,0,0,.407,1.245,1.558,1.558,0,0,0,1.222.454,1.594,1.594,0,0,0,1.236-.448,1.785,1.785,0,0,0,.41-1.251V7.838h1.547v5.256a3.5,3.5,0,0,1-.223,1.28,2.706,2.706,0,0,1-.636.973,2.809,2.809,0,0,1-1,.618,4.2,4.2,0,0,1-2.663,0,2.888,2.888,0,0,1-1-.612,2.6,2.6,0,0,1-.636-.967A3.577,3.577,0,0,1,34.242,13.094Zm7.816.469h1.559a1.377,1.377,0,0,0,.551,1.028,2.069,2.069,0,0,0,1.178.308,2.189,2.189,0,0,0,1.154-.284.984.984,0,0,0,.2-1.515,1.833,1.833,0,0,0-.826-.4l-1.594-.422a2.747,2.747,0,0,1-1.494-.864,2.3,2.3,0,0,1-.486-1.5,1.979,1.979,0,0,1,.211-.908,2.106,2.106,0,0,1,.595-.715,2.851,2.851,0,0,1,.935-.466,4.255,4.255,0,0,1,1.236-.167,4.462,4.462,0,0,1,1.157.144,2.674,2.674,0,0,1,.932.442,2.326,2.326,0,0,1,.647.768,2.863,2.863,0,0,1,.311,1.1H46.764a1.354,1.354,0,0,0-.173-.562,1.144,1.144,0,0,0-.343-.366A1.282,1.282,0,0,0,45.785,9a2.654,2.654,0,0,0-.533-.053,1.772,1.772,0,0,0-1.022.258.812.812,0,0,0-.372.7.876.876,0,0,0,.29.712,2.307,2.307,0,0,0,.876.4l1.389.363a3.319,3.319,0,0,1,1.579.832,2.163,2.163,0,0,1,.536,1.564,2.111,2.111,0,0,1-.876,1.764,3.054,3.054,0,0,1-1,.475,4.716,4.716,0,0,1-1.28.164,5.263,5.263,0,0,1-1.266-.144,2.961,2.961,0,0,1-1.02-.457,2.359,2.359,0,0,1-.709-.812A2.919,2.919,0,0,1,42.059,13.563Z" transform="translate(459 985)"/>
    <g id="Group_4952" data-name="Group 4952" transform="translate(387 974.556)">
      <path id="Path_1015" data-name="Path 1015" class="cls-3" d="M25.4,1" transform="translate(-9.243 -0.146)"/>
      <line id="Line_280" data-name="Line 280" class="cls-3" y2="28.18" transform="translate(21.437 0.664)"/>
      <line id="Line_281" data-name="Line 281" class="cls-3" x1="34.032" transform="translate(21.437 2.318)"/>
      <path id="Path_1016" data-name="Path 1016" class="cls-3" d="M7.9,28.844V.6H58.662V28.844" transform="translate(-2.875)"/>
      <path id="Path_1017" data-name="Path 1017" class="cls-3" d="M87.8,45" transform="translate(-31.949 -16.156)"/>
      <line id="Line_282" data-name="Line 282" class="cls-3" x2="16.603" transform="translate(4.834 22.228)"/>
      <line id="Line_283" data-name="Line 283" class="cls-3" x2="16.603" transform="translate(4.834 8.933)"/>
      <line id="Line_284" data-name="Line 284" class="cls-3" x2="16.603" transform="translate(4.834 15.549)"/>
      <line id="Line_285" data-name="Line 285" class="cls-3" x2="16.603" transform="translate(4.834 2.318)"/>
      <line id="Line_286" data-name="Line 286" class="cls-3" y1="6.616" x2="10.051" transform="translate(11.387 22.228)"/>
      <line id="Line_287" data-name="Line 287" class="cls-3" y1="6.679" x2="10.051" transform="translate(11.387 15.549)"/>
      <line id="Line_288" data-name="Line 288" class="cls-3" y1="6.616" x2="10.051" transform="translate(11.387 8.933)"/>
      <line id="Line_289" data-name="Line 289" class="cls-3" y1="6.616" x2="10.051" transform="translate(11.387 2.318)"/>
      <line id="Line_290" data-name="Line 290" class="cls-3" y1="6.616" x2="10.114" transform="translate(4.834 22.228)"/>
      <line id="Line_291" data-name="Line 291" class="cls-3" y1="6.679" x2="10.114" transform="translate(4.834 15.549)"/>
      <line id="Line_292" data-name="Line 292" class="cls-3" y1="6.616" x2="10.114" transform="translate(4.834 8.933)"/>
      <line id="Line_293" data-name="Line 293" class="cls-3" y1="6.616" x2="10.114" transform="translate(4.834 2.318)"/>
      <line id="Line_294" data-name="Line 294" class="cls-3" x2="60.177" transform="translate(0 28.844)"/>
      <g id="Group_4947" data-name="Group 4947" transform="translate(27.544 5.307)">
        <rect id="Rectangle_1947" data-name="Rectangle 1947" class="cls-3" width="2.544" height="1.908" transform="translate(9.796 18.638)"/>
        <rect id="Rectangle_1948" data-name="Rectangle 1948" class="cls-3" width="2.544" height="1.908" transform="translate(9.796 6.17)"/>
        <rect id="Rectangle_1949" data-name="Rectangle 1949" class="cls-3" width="2.544" height="1.908" transform="translate(9.796)"/>
        <rect id="Rectangle_1950" data-name="Rectangle 1950" class="cls-3" width="2.544" height="1.908" transform="translate(9.796 12.404)"/>
        <rect id="Rectangle_1951" data-name="Rectangle 1951" class="cls-3" width="2.544" height="1.908" transform="translate(0 18.638)"/>
        <rect id="Rectangle_1952" data-name="Rectangle 1952" class="cls-3" width="2.544" height="1.908" transform="translate(0 6.17)"/>
        <rect id="Rectangle_1953" data-name="Rectangle 1953" class="cls-3" width="2.544" height="1.908"/>
        <rect id="Rectangle_1954" data-name="Rectangle 1954" class="cls-3" width="2.544" height="1.908" transform="translate(0 12.404)"/>
        <rect id="Rectangle_1955" data-name="Rectangle 1955" class="cls-3" width="2.544" height="1.908" transform="translate(19.592 18.638)"/>
        <rect id="Rectangle_1956" data-name="Rectangle 1956" class="cls-3" width="2.544" height="1.908" transform="translate(19.592 6.17)"/>
        <rect id="Rectangle_1957" data-name="Rectangle 1957" class="cls-3" width="2.544" height="1.908" transform="translate(19.592)"/>
        <rect id="Rectangle_1958" data-name="Rectangle 1958" class="cls-3" width="2.544" height="1.908" transform="translate(19.592 12.404)"/>
      </g>
    </g>
  </g>
</svg>
