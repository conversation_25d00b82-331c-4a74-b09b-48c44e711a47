{"asset": {"version": "2.0", "generator": "babylon.js glTF exporter for 3dsmax 2019 v20210212.2"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"mesh": 0, "translation": [0.00500000035, 0.0, 0.0], "name": "T03_box_door_R"}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2, "TEXCOORD_0": 3, "TEXCOORD_1": 4}, "indices": 0}], "name": "T03_box_door_R"}], "accessors": [{"bufferView": 0, "componentType": 5123, "count": 36, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "componentType": 5126, "count": 36, "max": [4.99500036, 5.0, 0.0], "min": [-5.005, -5.0, -10.0], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 432, "componentType": 5126, "count": 36, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "componentType": 5126, "count": 36, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 2, "byteOffset": 288, "componentType": 5126, "count": 36, "type": "VEC2", "name": "accessorUV2s"}], "bufferViews": [{"buffer": 0, "byteLength": 72, "name": "bufferViewScalar"}, {"buffer": 0, "byteOffset": 72, "byteLength": 864, "byteStride": 12, "name": "bufferViewFloatVec3"}, {"buffer": 0, "byteOffset": 936, "byteLength": 576, "byteStride": 8, "name": "bufferViewFloatVec2"}], "buffers": [{"uri": "T03_box_door_R.bin", "byteLength": 1512}]}