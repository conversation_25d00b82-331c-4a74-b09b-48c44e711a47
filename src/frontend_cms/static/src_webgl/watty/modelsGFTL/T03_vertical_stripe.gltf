{"asset": {"version": "2.0", "generator": "babylon.js glTF exporter for 3dsmax 2019 v20210212.2"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"mesh": 0, "name": "T03_vertical_stripe"}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2, "TEXCOORD_0": 3, "TEXCOORD_1": 4}, "indices": 0}], "name": "T03_vertical_stripe"}], "accessors": [{"bufferView": 0, "componentType": 5123, "count": 24, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "componentType": 5126, "count": 24, "max": [5.0, 5.0, 10.0], "min": [-5.0, -5.0, 0.0], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 288, "componentType": 5126, "count": 24, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "componentType": 5126, "count": 24, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 2, "byteOffset": 192, "componentType": 5126, "count": 24, "type": "VEC2", "name": "accessorUV2s"}], "bufferViews": [{"buffer": 0, "byteLength": 48, "name": "bufferViewScalar"}, {"buffer": 0, "byteOffset": 48, "byteLength": 576, "byteStride": 12, "name": "bufferViewFloatVec3"}, {"buffer": 0, "byteOffset": 624, "byteLength": 384, "byteStride": 8, "name": "bufferViewFloatVec2"}], "buffers": [{"uri": "T03_vertical_stripe.bin", "byteLength": 1008}]}