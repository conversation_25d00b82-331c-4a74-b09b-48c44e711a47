{"asset": {"version": "2.0", "generator": "babylon.js glTF exporter for 3dsmax 2019 v20210212.2"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"mesh": 0, "name": "hinge_frame_part_L"}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2, "TEXCOORD_0": 3, "TEXCOORD_1": 4}, "indices": 0}], "name": "hinge_frame_part_L"}], "accessors": [{"bufferView": 0, "componentType": 5123, "count": 408, "type": "SCALAR", "name": "accessorIndices"}, {"bufferView": 1, "componentType": 5126, "count": 408, "max": [31.3683, 12.9842443, -17.015337], "min": [1.90985262, -12.7183838, -81.71661], "type": "VEC3", "name": "accessorPositions"}, {"bufferView": 1, "byteOffset": 4896, "componentType": 5126, "count": 408, "type": "VEC3", "name": "accessorNormals"}, {"bufferView": 2, "componentType": 5126, "count": 408, "type": "VEC2", "name": "accessorUVs"}, {"bufferView": 2, "byteOffset": 3264, "componentType": 5126, "count": 408, "type": "VEC2", "name": "accessorUV2s"}], "bufferViews": [{"buffer": 0, "byteLength": 816, "name": "bufferViewScalar"}, {"buffer": 0, "byteOffset": 816, "byteLength": 9792, "byteStride": 12, "name": "bufferViewFloatVec3"}, {"buffer": 0, "byteOffset": 10608, "byteLength": 6528, "byteStride": 8, "name": "bufferViewFloatVec2"}], "buffers": [{"uri": "hinge_frame_part_L.bin", "byteLength": 17136}]}