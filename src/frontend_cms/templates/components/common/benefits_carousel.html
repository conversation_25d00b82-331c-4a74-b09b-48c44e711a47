{% load static %}
{% load user_agents %}
{% load i18n %}
{% load components %}

<figure class="benefits-container">
    <ul class="benefits-box__container {% if new_ds %} benefits-box__container--new-ds {% endif %}">
        {% for benefit in benefits %}
            <li class="col-sm-4 benefits-box {% if forloop.counter == 1 %}benefits-box--active{% endif %} pt-24 px-24 md:px-8 md:pt-0 lg:p-0" style="background-color: {{ benefit.background_color }}">
                <picture class="benefits-box__image flex" lazy>
                    <source srcset="{% placeholder_svg width='256' height='240' img=False %}" type="image/webp"
                            data-lazysrc="{% static ''%}{{benefit.img_mobile}}.webp, {% static '' %}{{benefit.img_mobile}}@2x.webp 2x" media="(max-width: 1023px)">
                    <source srcset="{% placeholder_svg width='256' height='240' img=False %}" type="image/jpeg"
                            data-lazysrc="{% static '' %}{{benefit.img_mobile}}.jpg, {% static '' %}{{benefit.img_mobile}}@2x.jpg 2x" media="(max-width: 1023px)">
                    <source srcset="{% placeholder_svg width='323' height='130' img=False %}" type="image/webp"
                            data-lazysrc="{% static '' %}{{benefit.img_desktop}}.webp, {% static '' %}{{benefit.img_desktop}}@2x.webp 2x" media="(min-width: 1024px)">
                    <source srcset="{% placeholder_svg width='323' height='130' img=False %}" type="image/jpeg"
                            data-lazysrc="{% static '' %}{{benefit.img_desktop}}.jpg, {% static '' %}{{benefit.img_desktop}}@2x.jpg 2x" media="(min-width: 1024px)">
                    <img
                        class="benefits-box__image-cell"
                        src="{% placeholder_svg width='1' height='1' img=False %}"
                        data-lazysrc="{% static benefit.img_desktop|add:'.jpg' %}"
                        alt="{{ benefit.title }}"
                    >
                </picture>

                {% if new_ds %}
                    <div class="benefits-box__text-container benefits-box__text-container--new-ds pt-16 pb-32 md:pb-0 lg:py-0 text-center z-10">
                        <div class="benefits-box__row md:px-16 text-center">
                          <h2 class="normal-24 lg:normal-28 {% if data.theme_light %}text-offblack-600 lg:text-offwhite-800{% else %}text-offwhite-800{% endif %} pb-8">
                              {{ benefit.title }}
                          </h2>
                          <p class="normal-16 md:normal-14 lg:normal-20 {% if data.theme_light %}text-offblack-600 lg:text-offwhite-800{% else %}text-offwhite-800{% endif %} pb-16 px-8 md:px-0 lg:pt-8 lg:pb-16 benefits-box__text">
                              {{ benefit.text }}
                          </p>
                        </div>
                        <i class="benefits-box__icon {% if with_button %} benefits-box__icon--with-button {% endif %}">
                            {% if with_button %}
                                <a href="{% url 'front-product-shelf' benefit.link %}" class="btn-cta benefits-box__button">
                                  {{ benefit.cta }}
                                </a>
                                <span class="benefits-box__icon-border"></span>
                            {% endif %}
                        </i>
                    </div>
                {% else %}
                    <div class="benefits-box__text-container pt-24 px-8 lg:p-0 text-center z-10">
                        <h2 class="th-2-d th-4-t th-2-m tfc-white">
                            {{ benefit.title }}
                        </h2>
                        <p class="tp-small-m tp-default-t tfc-white pt-8 px-24 md:px-0 lg:pt-16 benefits-box__text">
                            {{ benefit.text }}
                        </p>
                        <i class="mt-16 benefits-box__icon"></i>
                    </div>
                {% endif %}
            </li>

        {% endfor %}
    </ul>
    <div class="benefits-box__progressbar mx-auto mt-32">
        <span class="benefits-box__progressbar--active"></span>
    </div>
</figure>
