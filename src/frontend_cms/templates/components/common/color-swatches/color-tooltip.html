{% load i18n util_tags components static %}

<div class="color-tooltip z-10">
  <picture class="color-tooltip__picture" lazy>
    <source src="{% placeholder_svg width=160 height=160 img=False %}" data-lazysrc="{% static 'products-list/color-swatches/tooltip/'|add:image|add:'.jpg' %} 1x, {% static 'products-list/color-swatches/tooltip/'|add:image|add:'@2x.jpg' %} 2x" type="image/jpeg">
    <img
      src="{% placeholder_svg width=160 height=160 img=False %}"
      data-lazysrc="{% static 'products-list/color-swatches/tooltip/'|add:image|add:'.jpg' %}"
      class="color-tooltip__image"
      width="160"
      height="160"
    />
  </picture>
  <span class="color-tooltip__apla text-center normal-10 pt-4 text-offblack-600 bg-grey-600 pb-8 px-32">
      {% trans name %}
  </span>
</div>
