{% load i18n static user_agents %}
{% spaceless %}
<form
    id="newsletter-form"
    action="/api/v1/newsletter/?source={{ source }}"
    method="post"
    class="newsletter newsletter--validate js-form-ajax js-validate position-relative pt-40 m-0{% if type == "waitlist" %} newsletter--waitlist{% endif %}"
    data-track-lead="{{ source }}"
    data-no-notify="true"
    data-thank-you="true"
    data-script="readMore"
>
    <div class="newsletter__group flex flex-column middle-sm">
        <button
            id="newsletter-button"
            class="btn-cta {% if theme_dark %}btn-cta--border{% else %}btn-cta--border-white{% endif %}"
        >
            <img
                class="newsletter__icon"
                src="{% if theme_dark %}{% static 'landing-page/hero-newsletter/accept_dark.svg' %}{% else %}{% static 'landing-page/hero-newsletter/accept.svg' %}{% endif %}"
            />
            <span class="newsletter__button">{{ cta|safe }}</span>
        </button>
        <label
            class="newsletter__label normal-10 {% if theme_dark %}text-offblack-600{% else %}text-offwhite-700{% endif %}"
            for="subscribe-email-mobile"
        >
        </label>
        <input
            id="subscribe-email-mobile"
            type="email"
            placeholder="{{ placeholder }}"
            name="email"
            required=""
            class="newsletter__input bg-offwhite-700 border-offwhite-700 normal-16 text-offblack-600 md:mr-16 xl:mr-24 px-16 {% if theme_dark %}newsletter__input--dark{% endif %}"
        />
    </div>
    <div class="newsletter__terms newsletter__terms--long position-relative flex start-xs p-0 pt-8 mb-64 md:mb-0 {% if checkbox3 %}middle-md md:mb-96{% endif %}">
        <input
            type="checkbox"
            id="newsletter-rodo-checkbox"
            class="newsletter__checkbox mr-8 {% if theme_dark %}newsletter__checkbox--dark border-grey-900{% else %}border-grey-800{% endif %}"
        />
        <img
            class="newsletter__checkbox__icon {% if checkbox3 %}newsletter__checkbox__icon--center{% endif %}"
            src="{% if theme_dark %}{% static 'icons/rodo/checkbox_dark.svg' %}{% else %}{% static 'icons/rodo/checkbox.svg' %}{% endif %}"
        />
        {% if checkbox3 %}
            <label for="newsletter-rodo-checkbox" class="normal-10 newsletter__checkbox-label {% if theme_dark %}text-grey-900{% else %}text-grey-800{% endif %}">
                <span class="newsletter__checkbox__center text-offblack-600">{{ checkbox3|safe }}</span>
                <span class="newsletter__subtext newsletter__short mt-12">
                    <span class="text-offblack-600 mr-4">{{ checkbox4|safe }}</span>
                    <a href="javascript:void(0);" class="newsletter__read text-orange-900">{{ data.checkbox2 }}</a>
                    <span class="newsletter__long mt-4 display-none">
                        {{ checkbox1|safe }}
                        <a href="javascript:void(0);" class="newsletter__read newsletter__read--less text-orange-900">{{ data.checkbox5 }}</a>
                    </span>
                </span>
            </label>
        {% else %}
            <label for="newsletter-rodo-checkbox" class="normal-10 newsletter__checkbox-label {% if theme_dark %}text-grey-900{% else %}text-grey-800{% endif %}">
            <span class="newsletter__short">
                {{ checkbox1|safe|truncatechars:100 }}
                <a href="javascript:void(0);" class="newsletter__read text-orange-900">{{ data.checkbox2 }}</a>
            </span>
            <span class="newsletter__long display-none">
                {{ checkbox1|safe }}
            </span>
        </label>
        {% endif %}
    </div>
</form>
{% endspaceless %}
