{% load components  %}
{% load user_agents %}

{% spaceless %}
    <div class="position-relative">
        <div class="mobile-hidden">
            <!-- mega-menu on desktop-->
            <input id="megaMenuDesktop" class="header__checkbox header__checkbox__desktop" type="checkbox">
            {% include './mega-menu/_mega-menu-desktop.html' %}

            <!-- inspiration-menu on desktop-->
            <input id="inspirationMenuDesktop" class="header__checkbox header__checkbox__desktop" type="checkbox">
            {% include './mega-menu/_inspiration-menu-desktop.html' %}

            <!-- regions on desktop-->
            <input id="regionsDesktop" class="header__checkbox header__checkbox__desktop" type="checkbox">
            {% include './regions/_regions-desktop.html' %}
        </div>

        <input id="orderStatusForm" class="header__checkbox header__checkbox__desktop" type="checkbox">
        {% include './order-status/_order-status-desktop.html' %}
    </div>

{% endspaceless %}
