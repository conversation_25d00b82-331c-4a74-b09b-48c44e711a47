{% load i18n static util_tags components %}

{% spaceless %}
        <a href="{% if url_kwargs|length > 0 %}{% url url_name url_kwargs %}{% else %}{% url url_name %}{% endif %}{{ url_suffix }}"
            class="text-offblack-700 col-xs-5 col-md-4 mb-12 lg:mb-0 link {{ extra_class }}"
            data-testid="redirect-{% if not is_desktop %}mobile-{% endif %}discover-{{ type }}"
            onclick="Site.Track.trackMegaMenuNavigation({'eventAction': 'discover', 'eventLabel': '{{event_label}}'})"
        >
        <div class="relative">
            <picture class="mega-menu__picture" lazy>
                <source srcset="{% placeholder_svg width='165' height='160' img=False %}" type="image/webp"
                        data-lazysrc="{% static 'header-optimized/mega-menu/'|add:type|add:'.webp' %} 1x, {% static 'header-optimized/mega-menu/'|add:type|add:'@2x.webp' %} 2x">
                <source srcset="{% placeholder_svg width='165' height='160' img=False %}" type="image/jpeg"
                        data-lazysrc="{% static 'header-optimized/mega-menu/'|add:type|add:'.jpg' %} 1x, {% static 'header-optimized/mega-menu/'|add:type|add:'@2x.jpg' %} 2x">
                <img
                    class="mega-menu__picture"
                    src="{% placeholder_svg width='1' height='1' img=False %}"
                    data-lazysrc="{% static 'header-optimized/mega-menu/'|add:type|add:'@2x.jpg' %}"
                    alt=""
                >
            </picture>
            {% if label %}
                <span class="text-orange bold-10-2 mega-menu__badge mega-menu__sale absolute bg-white rounded-2 px-8">{{ label|safe }}</span>
            {% endif %}
        </div>
        <h5 class="normal-14 lg:normal-16 pt-4 lg:pt-8">
            {{ heading|safe }}
        </h5>
    </a>
{% endspaceless %}
