{% load i18n user_agents %}

<ul class="regions col-md-10" data-regions-wardrobe-available="{{ T03_REGIONS }}">
    <li class="mt-12 mobile-visible">
        <button
            class="normal-16 text-orange bg-transparent flex middle-xs py-12 pl-32 text-capitalize position-relative link regions__button"
            data-testid="{% if not is_desktop %}mobile-{% endif %}region-change-{{ user_region.name }}"
        >
            {% if not user_region.name == "_other" %}
                <i class="regions__sprite regions__sprite--{{ user_region.name }}"></i>
                {% if not user_region.name == "united_kingdom" %}
                    {{ user_region.name }}
                {% else %}
                    {% trans 'region_menu_country_United Kingdom' %}
                {% endif %}
            {% else %}
                <i class="regions__sprite regions__sprite--other-regions"></i>
                {% trans 'region_menu_country_other_regions' %}
            {% endif %}
        </button>
    </li>
    {% for c, c_trans, c_currency in COUNTRIES %}
        {% if c != '_other' %}
        	<li class="col-md-3 col-lg-2 mobile-hidden">
            <button data-region="{{ c }}" data-testid="region-change-{{ c }}"
                    class="normal-16 text-offblack-700 bg-transparent flex middle-xs py-12 pl-32 text-capitalize position-relative link regions__button">
                <i data-region="{{ c }}" class="regions__sprite regions__sprite--{{ c }}"></i>
                {% trans c_trans %}
            </button>
            </li>
            <li class="mobile-visible">
                <button data-region="{{ c }}" data-testid="mobile-region-change-{{ c }}"
                        class="normal-16 text-offblack-700 bg-transparent flex middle-xs py-12 pl-32 text-capitalize position-relative link regions__button">
                    <i data-region="{{ c }}" class="regions__sprite regions__sprite--{{ c }}"></i>
                    {% trans c_trans %}
                </button>
            </li>
        {% endif %}
    {% endfor %}
    <li class="col-md-3 col-lg-2">
        <button
            data-testid="{% if not is_desktop %}mobile-{% endif %}region-change-other"
            data-region="_other"
            class="text-offblack-700 normal-16 bg-transparent flex middle-xs py-12 pl-32 text-capitalize position-relative link regions__button"
        >
            <i data-region="_other" class="regions__sprite regions__sprite--other-regions"></i>
            {% trans 'region_menu_country_other_regions' %}
        </button>
    </li>
</ul>
