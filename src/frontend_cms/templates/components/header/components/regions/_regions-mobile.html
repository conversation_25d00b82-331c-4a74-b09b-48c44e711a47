{% load i18n %}

{% spaceless %}
    <label for="country-container" class="header__country-label"></label>
    <section class="flex header-regions">
        <input id='nav-item-country' type="radio" name="regions-nav-item" checked
               class="header-regions__hidden-input header-regions__hidden-input--country"
        >
        <label for="nav-item-country" data-testid="mobile-open-region-tab"
               class="text-center text-offblack-700 py-8 normal-20 bg-offwhite-600 header-regions__label">
            {% trans 'emily_mobile_region_menu_hearder_1_2' %}
        </label>
        <input id='nav-item-language' type="radio" name="regions-nav-item"
               class="header-regions__hidden-input header-regions__hidden-input--language"
        >
        <label for="nav-item-language" data-testid="mobile-open-language-tab"
               class="text-center text-offblack-700 py-8 normal-20 bg-offwhite-600 header-regions__label">
            {% trans 'emily_mobile_region_menu_hearder_1_1' %}
        </label>
        <div class="px-16 pb-32 header-regions__country">
            {% include './_regions-countries.html' with is_desktop=False %}
        </div>
        <ul class="px-16 header-language header-regions__language">
            {% include './_regions-languages.html' with is_desktop=False %}
        </ul>
    </section>
{% endspaceless %}
