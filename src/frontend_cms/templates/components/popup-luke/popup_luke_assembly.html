{% load static i18n components %}
{% load voucher_tags %}
{% load user_agents %}

{% spaceless %}

    <div class="popup-container-body" id="{{ content_name }}">
        <div class="luke-popup-content tplr-l-d tpb-m-d shadow-bottom display-block-bottom tfc-color-black" style="{% if not with_cta %} padding-bottom: 24px; {% endif %}">
            <h2 class="luke-popup-title th-2-m th-2-t tmb-xs-m tplr-s-m tmb-s-d">{% trans "dorothy_assembly_service_cart_popup_header" %}</h2>
            <div class="scroll-area">
                <p class="tfc-color-black tp-small-m tmt-s-m tmb-s">{% trans "dorothy_assembly_service_cart_popup_text_0" %}</p>
                <div class="tp-small-m tpl-m-m assembly-list">
                    <ul class="luke-wardrobe-assembly-info">
                        {% trans "dorothy_assembly_service_cart_t03_popup_list" %}
                    </ul>
                    <ul class="luke-assembly-info">
                        {% trans "dorothy_assembly_service_cart_popup_list" %}
                    </ul>
                </div>
{#                {% if delivery_delay %}#}
{#                    <p class="tfc-color-black tp-small-m tmt-s">{% trans "dorothy_assembly_service_cart_popup_text_2" %}</p>#}
{#                {% endif %}#}

                {% if not user_region.name == 'france' %}
                    <p class="tp-small-m tmb-s tmt-s tfc-gray">{% trans "dorothy_assembly_service_cart_popup_text_1" %}</p>
                {% endif %}

                <p class="tfc-color-black tp-small-m tmb-m-m link-wrapper">
                    {% if user_region.name == 'france' %}
                        {% trans "dorothy_assembly_service_cart_popup_text_1.3" %}
                    {% else %}
                        {% trans "dorothy_assembly_service_cart_popup_text_1.1" %} <a href="{% url 'front-faq' %}" target="_blank" class="tfc-red red-arrow">{% trans "dorothy_assembly_service_cart_popup_text_1.2" %}</a>
                    {% endif %}
                </p>
            </div>
        </div>
        {% if with_cta %}
            <div class="luke-popup-cta tplr-s-m tplr-l-d tpb-l-d">
                <div class="cta-wrapper tpt-s-m tpb-l-m tpt-l-d text-center">
                    <div class="text-center lg:text-left">
                        <p class="th-2-m th-4-t tfc-color-black tmb-xs assembly-popup-price"></p>
                        <p
                                class="tp-sub-m assembly-popup-items-wrapper"
                                data-one-item-text=" {% trans "dorothy_assembly_service_popup_one_item" %}"
                                data-more-items-text=" {% trans "dorothy_assembly_service_popup_more_items" %}"
                        >
                            {% trans "dorothy_assembly_service_cart_for" %}
                            <span class="assembly-popup-items"></span>
                            <span class="assembly-popup-items-text"></span>
                        </p>
                    </div>
                    <button class="btn-cta btn-cta--border tmt-s-m" id="modalAddAssemblyButton"><span>{% trans "dorothy_assembly_service_cart_popup_button" %}</span></button>
                </div>
            </div>
        {% endif %}

    </div>

{% endspaceless %}
