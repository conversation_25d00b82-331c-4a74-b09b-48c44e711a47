{% load static i18n components user_agents %}
{% get_current_language as LANGUAGE_CODE %}

<div class="basic-popup-body send-furniture-popup">
    <!-- RODO {  -->
    {% include 'components/rodo/save/rodo_tooltip.html' %}
    <!-- } RODO -->
    <h2 style="font-size: 27px;">{% trans 'popup_save_send_header1' %}</h2>
    <p>{% trans 'popup_save_send_text' %}
    <form action="#" class="js-validate" data-script="Forms" id="form_save_furniture" data-track-lead="save_for_later">
        {% csrf_token %}
        <ul class="form new-cart" style="padding-top: 10px; text-align: left;">

            {% input_text "email" "form_email" addresses.email required=True input_type="email" %}

        </ul>
        <input style="margin-top: 15px;" class="btn-cta w-full mb-8" type="submit" name="action_confirmation" value="{% trans "popup_save_send_button" %}" />
        <!-- RODO {  -->
        {% include 'components/rodo/save/rodo_checkbox.html'  with id="save-furniture-popup-rodo" %}
        <!-- } RODO -->
    </form>
</div>
<div class="basic-popup-body spinner">
    <div class="vertical-center"><div class="vertical-center-content"><div class="spinner-loader-wrap"><div id="floatingCirclesG"> <div class="f_circleG" id="frotateG_01"></div><div class="f_circleG" id="frotateG_03"></div><div class="f_circleG" id="frotateG_04"></div><div class="f_circleG" id="frotateG_05"></div><div class="f_circleG" id="frotateG_02"></div><div class="f_circleG" id="frotateG_06"></div><div class="f_circleG" id="frotateG_07"></div><div class="f_circleG" id="frotateG_08"></div></div></div></div></div>
</div>


