{% load i18n static components user_agents util_tags cache galleries %}
{% load promotion_tags region_tags seo_tags voucher_tags %}
{% load checkout_tags tracking %}
{% load webpack_loader %}

<!doctype html>
{% get_current_language as LANGUAGE_CODE %}
<html class="no-js {% block extra_html_class %}{% endblock %}" lang="{{ LANGUAGE_CODE }}">
<head>
    {% block extraheadfirst %}
    {% endblock %}
    <meta charset="utf-8">
    <meta name="google" value="notranslate">
    <title>{% block title %}{% blocktrans %}metatags_homepage_1{% endblocktrans %}{% endblock %}</title>
    <link rel="stylesheet" href="{% static "dist/css/ds.css" %}?1">
    <link rel="stylesheet" href="{% static "dist/css/style.css" %}?1">
    <meta name="title" content="{% block meta_title %}{% blocktrans %}metatags_homepage_1{% endblocktrans %}{% endblock %}">
    <meta name="description" content="{% block description %}{% blocktrans %}metatags_homepage_description_1{% endblocktrans %}{% endblock %}">
    <meta name="viewport" content="viewport-fit=cover, width=device-width, minimum-scale=1, maximum-scale=1">
    {% block extra_head_prefetch %}{% endblock %}
    <meta name="p:domain_verify" content="af453366b8129efaca68bf9c659fa83f"/>

    <!-- Place favicon.ico in the root directory -->
    {% if IS_PRODUCTION %}
        {% block robots %}
            <meta name="robots" content="INDEX,FOLLOW">
        {% endblock %}
    {% else %}
        <meta name="robots" content="noindex, nofollow">
    {% endif %}
    <link rel="manifest" href="{% static 'favicons/manifest.json' %}">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="theme-color" content="#ffffff">
    <meta property="fb:app_id" content="739339722848856" />
    <meta name="p:domain_verify" content="64f280ea528bae6144728bdb8f2dcd19"/>
    <meta name="p:domain_verify" content="af453366b8129efaca68bf9c659fa83f"/>
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@tylko_furniture">
    <meta name="twitter:creator" content="@tylko_furniture">
    <meta name="twitter:title" content="{% block twitter_title %}{% blocktrans %}metatags_homepage_1{% endblocktrans %}{% endblock %}">
    <meta name="twitter:description" content="{% block twitter_description %}{% blocktrans %}metatags_homepage_description_1{% endblocktrans %}{% endblock %}">

    <link rel="canonical" href="{{ SCHEME }}://{{ SITE }}{% block canonical_url %}{{ request.get_full_path }}{% endblock %}" />
    <script src="https://unpkg.com/pubsub-js@1.9.2/src/pubsub.js"></script>


    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        '//www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-5KWDFT');
    </script>
    <!-- End Google Tag Manager -->

    <script src="//ajax.googleapis.com/ajax/libs/jquery/2.1.3/jquery.min.js"></script>
    <script type="text/javascript">
        {% with region=user_region %}
            window.cstm_i18n = {
                "language": "{{ LANGUAGE_CODE }}",
                "currency": "{{ user_region.currency.code }}",
                "locale": "{{ user_region.country.locale }}",
                "error": "{% trans 'addreview_page_upload_problem' as translated_text %}{{ translated_text|escapejs }}",
                "account": "{% trans 'Account' as translated_text %}{{ translated_text|escapejs }}",
                "cart": "{% trans 'Cart' as translated_text %}{{ translated_text|escapejs }}",
                "added_to_cart": "{% trans 'Added to cart' as translated_text %}{{ translated_text|escapejs }}",
                "removed_from_cart": "{% trans 'Removed from cart' as translated_text %}{{ translated_text|escapejs }}",
                "dorothy_assembly_service_cart_ribbon_4": "{% trans 'dorothy_assembly_service_cart_ribbon_4' as translated_text %}{{ translated_text|escapejs }}",
                "dorothy_assembly_service_cart_ribbon_2": "{% trans 'dorothy_assembly_service_cart_ribbon_2' as translated_text %}{{ translated_text|escapejs }}",
                "promo_accepted": "{% trans 'Promo Code accepted.' as translated_text %}{{ translated_text|escapejs }}",
                "promo_not_accepted": "{% trans 'Please enter a valid promo code.' as translated_text %}{{ translated_text|escapejs }}",
                "user_agent": "{{ request.user_agent.ua_string|lower }}",
                "delivery_dynamic_text" : "{% trans 'delivery_dynamic_text_with_br' as translated_text %}{{ translated_text|escapejs }}",
                "delivery_dynamic_text_with_broken_copy" : "{% trans 'delivery_dynamic_text_with_tick' as translated_text %}{{ translated_text|escapejs }}",
                "shelf_saved" : "{% trans 'Your piece has been successfully saved.' as translated_text %}{{ translated_text|escapejs }}",
                "shelf_not_saved" : "{% trans 'Saving failed. Please try again.' as translated_text %}{{ translated_text|escapejs }}",
                "email_sent" : "{% trans 'popup_save_createaccount_preheader' as translated_text %}{{ translated_text|escapejs }}",
                "vat_included": "{% trans 'common_includes_vat' as translated_text %}{{ translated_text|escapejs }}",
                "region_name": "{{ region.name }}",
                "last_clouse_up": 0,
                "twitter_message": "{% trans 'emily_exit_popup_sharing_twitter_input_headline_1' as translated_text %}{{ translated_text|escapejs }}",
                "product_page_configurator_width_wider": "{% trans 'product_page_configurator_width_wider' as translated_text %}{{ translated_text|escapejs }}",
                "product_page_configurator_width_narrower": "{% trans 'product_page_configurator_width_narrower' as translated_text %}{{ translated_text|escapejs }}",
                "region_currency_code": "{{ USER_REGION_CURRENCY.code }}",
                "region_currency_symbol": "{{ USER_REGION_CURRENCY.symbol }}",
                regions: (( function() {
                    try {
                        return JSON.parse('{{ regions | safe }}')
                    } catch (err) {
                        return []
                    }
                })()),
            };
        {% endwith %}

    {% if request.user.is_authenticated == True %}
        urlParams = new URLSearchParams(window.location.search);
        mailUserId = urlParams.get('userId');
        if (mailUserId){
            window.mailUserId = mailUserId
        }
        if (typeof dataLayer != 'undefined') {
            dataLayer.push({
                'userId': 'user-{{ request.user.id }}',
                'event': 'authentication',
                'userLanguage': '{{ LANGUAGE_CODE }}'
            });
        }
        {% if "userGaId" not in request.COOKIES %}
                var d = new Date();
                d.setTime(d.getTime()+1000*60*60*24*365*2);
                var expires = 'expires='+d.toGMTString();
                document.cookie = 'userGaId="user-{{ request.user.id }}"; '+expires+'; path=/';
            {% endif %}
        {% else %}
            {% if "userGaId" in request.COOKIES %}
                if (typeof dataLayer != 'undefined') {
                    window.dataLayer.push({
                        'userId': '{{ request.COOKIES.userGaId }}',
                        'event': 'authentication',
                        'userLanguage': '{{ LANGUAGE_CODE }}'
                    });
                }
            {% else %}

                if (typeof dataLayer != 'undefined') {
                    window.dataLayer.push({
                        'userId': undefined,
                        'event': 'authentication',
                        'userLanguage': '{{ LANGUAGE_CODE }}'
                    });
                }
            {% endif %}
    {% endif %}

    </script>
    <script type="text/javascript">
        const pathname = window.location.pathname;
        const languagePrefixes = ['/en/', '/de/', '/fr/', '/es/', '/nl/', '/pl/', '/it/', '/sv/', '/da/', '/no/'];

        const removeLanguagePrefix = (path) => {
            const foundPrefix = languagePrefixes.find(prefix => path.startsWith(prefix));
            return foundPrefix ? path.substring(foundPrefix.length) : path.substring(1);
        };
        const pathnameWithoutLanguagePrefix = removeLanguagePrefix(pathname);

        cstm_language_change = (language) => {
            window.location = `${window.location.origin}/${language}/${pathnameWithoutLanguagePrefix}`;
        }
    </script>
    {% block extrahead %}
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://tylko.com/" />
        <meta property="og:title" content="{% blocktrans %}metatags_homepage_1{% endblocktrans %}" />
        <meta property="og:description" content="{% blocktrans %}metatags_homepage_description_1{% endblocktrans %}" />
        {% if request|is_ab_test:"ff_hp_hero_icons"  %}
        <meta property="og:image" content="https://tylko.com/r_static/share-preview-images/tylko-meta-preview-image-icons.jpg" />
        {% else %}
        <meta property="og:image" content="https://tylko.com/r_static/share-preview-images/tylko-meta-preview-image.jpg" />
        {% endif %}
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
    {% endblock %}

    <link rel="stylesheet" href="https://unpkg.com/swiper@7.0.8/swiper-bundle.min.css" />
    <script src="https://unpkg.com/swiper@7.0.8/swiper-bundle.min.js"></script>
    <script src="https://www.google.com/recaptcha/api.js?render=6LdqB9ocAAAAAGEOAE3ljZxaehMNQNzVUU18P7bV"></script>

    </head>
<body class="{% block extra_body_class %}{% endblock %} {% if promotion != None %}has-ribbon{% endif %}" style="{% block extra_body_style %}{% endblock %}" {% block extra_body_property %}{% endblock %} data-script="lazyLoadingImg" data-lazyLoadingStrategy="{ strategy: 'default' }">

<!-- Google Tag Manager -->
<noscript><iframe src="//www.googletagmanager.com/ns.html?id=GTM-5KWDFT"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager -->

<!-- Google Tag Manager helper init -->
{% block gtmTrack %}
    {% trackECommerce %}
{% endblock %}


<div class="wrapper {% if request|is_pc or request|is_tablet %} block-width{% else %}mobile{% endif %}" style="{% block wrapper_style %}{% endblock %}">
    {#  HEADER  #}
    {% block header %}
        {% include 'components/header/_header-logistic.html' %}
    {% endblock %}
    <main class="content {% if promotion != None %}promo{% endif %}" id="content" data-title="tylko" style="background: transparent;{% block content_style %}{% endblock %}">
        {% if messages %}
            {% for message in messages %}
                <span class="visually-hidden" data-notify='{"status":"{% if message.level == 40 %}error{% else %}success{% endif %}", "message":"{{ message}}", "timeout": 100000}'>
                </span>
            {% endfor %}
        {% endif %}

        {% block content %}
        {% endblock %}
    </main>
</div>

{% block dixa %}
    <script>
        window.dixa_token_en = "{{ DIXA_TOKEN_EN }}";
        window.dixa_token_de = "{{ DIXA_TOKEN_DE }}";
        window.dixa_token_fr = "{{ DIXA_TOKEN_FR }}";
        window.gtm_load_dixa = true;
    </script>
{% endblock %}

{% include 'front/_cookiebar.html' %}

</body>
</html>
