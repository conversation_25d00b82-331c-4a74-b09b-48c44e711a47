{% extends "front/base.html" %}
{% load i18n util_tags %}
{% load components %}

{% block extraheadfirst %}
    {{ block.super }}
    <meta name="referrer" content="origin"/>
{% endblock %}

{% block content %}
    <section class="change-password bg-white {% if promotion and promotion.ribbon_enabled %}lg:pt-48{% endif %}">
        <div class="container-cstm-fluid">
            <div class="col-xs-12 col-sm-8 col-sm-offset-2 col-md-5 col-md-offset-0 col-lg-4 col-lg-offset-1 mt-16">
                <div class="row left-xs">
                    <div class="col-xs-12">
                        <h2 class="bold-32 text-offblack-800 text-capitalize-first-letter">
                            {% trans "Change password" %}
                        </h2>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 col-sm-8 col-sm-offset-2 col-md-6 col-md-offset-1 col-lg-5 col-lg-offset-1 mt-32 md:mt-16 mb-64">
                    <form action="#" method="post" class="js-validate" data-script="Forms">
                        {% csrf_token %}
                        {% if not token %}
                                {% input_account input_name="password" type="password" placeholder="Current Password" id="login-password1" required=True label="Password" error_message="error" autofocus=True %}
                                <div class="mt-8 mb-32">
                                    {% password_account input_name="show-hide--current" field="login-password1" %}
                                </div>
                        {% else %}
                                <input type="hidden" name="token" value="{{ token }}">
                        {% endif %}
                        {% input_account input_name="new_password" type="password" placeholder="New Password" id="login-password2" required=True label="Password" error_message="error" %}
                        <div class="mt-8 mb-32">
                            {% password_account input_name="show-hide--new" field="login-password2" %}
                        </div>
                        {% input_account input_name="new-password2" type="password" placeholder="Confirm New Password" id="login-password3" required=True label="Password" error_message="error" %}
                        <div class="mt-8 mb-32">
                            {% password_account input_name="show-hide--confirm" field="login-password3" %}
                        </div>
                        <div class="row center-sm start-md mt-24">
                            <div class="col-xs-12 col-sm-6">
                                <button class="btn-cta w-full lg:w-auto text-nowrap px-32 mt-0" type="submit">
                                    <span class="text-capitalize-first-letter">{% trans "Save" %}</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
{% endblock %}
