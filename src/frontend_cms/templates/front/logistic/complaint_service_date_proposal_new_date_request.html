{% extends "front/logistic/_complaint_service_date_proposal.html" %}
{% load i18n %}
{% load static %}

{% block container %}
    <link rel="stylesheet" href="{% static "logistic/new_date_request.css" %}?1">
    {% if confirmation_page %}
        {% trans 'assembly_service_request_new_date_confirmation' %}
    {% else %}
        <h2>{% trans 'assembly_service_ask_for_preferred_date_1' %}</h2>
        <p class="mw-unset normal-16 text-offblack-600" style="padding-bottom: 10px; padding-top: 10px">
            {% trans 'assembly_service_ask_for_preferred_date_2' %}
        </p>
        <form method="post">
            <div>
                <label for="customer_note" hidden="hidden"></label>
                <input
                    type="text"
                    id="customer_note"
                    name="customer_note"
                    class="text-input"
                    placeholder="{% trans 'assembly_service_ask_for_preferred_date_placeholder' %}"
                >
            </div>
            <input type="submit" value="{% trans 'Submit' %}" class="submit-btn">
        </form>
    {% endif %}
{% endblock %}

