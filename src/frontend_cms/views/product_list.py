from urllib import parse

from django.shortcuts import redirect
from django.views import View


class ProductListRedirectView(View):
    template_name = 'front/products-list.html'

    nuxt_path_map = {
        'shelves': 'furniture-c',
        'etageres': 'meubles-c',
        'regale': 'mobel-c',
        'tvstand': 'tv-stand',
        'products': 'furniture-c',
    }

    def get(self, request, *args, **kwargs):
        url = self._build_redirect_url_for_nuxt()
        return redirect(url)

    def _build_redirect_url_for_nuxt(self):
        absolute_url = self.request.build_absolute_uri()
        parse_result = parse.urlparse(absolute_url)
        path = parse_result.path

        for django, nuxt in self.nuxt_path_map.items():
            if django in path:
                path = path.replace(django, nuxt)

        return path
