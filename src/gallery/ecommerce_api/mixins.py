from urllib.parse import (
    parse_qs,
    urlparse,
)

from custom.enums import Furniture
from gallery.services.color_overwrite import get_color_override


class ColorOverrideViewMixin:
    def get_object(self):
        instance = super().get_object()
        if instance.furniture_type == Furniture.sotty.value:
            # TODO add material override logic for sotty
            return instance
        color_value = self.request.GET.get('cv', self._get_cv_from_referer())
        color_override = get_color_override(color_value, instance)
        instance.material = color_override
        return instance

    def _get_cv_from_referer(self):
        x_forwarded_for = self.request.META.get('HTTP_REFERER')
        parsed_url = urlparse(x_forwarded_for)
        values = parse_qs(parsed_url.query).get('cv')
        return values[0] if values else ''
