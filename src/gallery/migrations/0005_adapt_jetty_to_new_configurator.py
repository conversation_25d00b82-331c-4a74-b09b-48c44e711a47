# Generated by Django 1.11.24 on 2020-06-04 10:08
from __future__ import unicode_literals

from django.db import (
    migrations,
    models,
)

import jsonfield.fields

import custom.models


class Migration(migrations.Migration):

    dependencies = [
        ('gallery', '0004_create_model_for_samplebox'),
    ]

    operations = [
        migrations.AddField(
            model_name='customdna',
            name='configurator_type',
            field=models.PositiveSmallIntegerField(
                choices=[(1, 'ROW'), (2, 'COLUMN')], default=1
            ),
        ),
        migrations.AddField(
            model_name='customdna',
            name='features',
            field=custom.models.ChoiceArrayField(
                base_field=models.PositiveSmallIntegerField(
                    choices=[
                        (1, 'TESTS_PASSED'),
                        (2, 'INSERTS'),
                        (3, 'CABLE_MGMT'),
                        (4, 'PLINTH'),
                        (5, 'LONG_LEGS'),
                        (6, 'LOCAL_EDGE'),
                        (7, 'DENSITY_STEPPER'),
                    ]
                ),
                blank=True,
                null=True,
                size=None,
            ),
        ),
        migrations.AddField(
            model_name='customdna',
            name='furniture_type',
            field=models.PositiveSmallIntegerField(
                blank=True, choices=[(1, 'SIDEBOARD'), (2, 'SHOERACK')], null=True
            ),
        ),
        migrations.AddField(
            model_name='jetty',
            name='dna_preset',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='customdna',
            name='pattern_slot',
            field=models.IntegerField(
                blank=True,
                help_text=(
                    'Type1&2 - 0=slant, 1=gradient, 2=pattern, 3=grid, '
                    + 'leave empty for configurator+'
                ),
            ),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='horizontals',
            field=jsonfield.fields.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='pattern',
            field=models.IntegerField(blank=True, default=0),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='property1',
            field=models.FloatField(blank=True, default=0),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='rows',
            field=jsonfield.fields.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='verticals',
            field=jsonfield.fields.JSONField(blank=True, default=list),
        ),
    ]
