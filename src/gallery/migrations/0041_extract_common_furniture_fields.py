# Generated by Django 3.1.8 on 2021-04-21 17:28

from django.db import (
    migrations,
    models,
)

import jsonfield.fields

import custom.enums


class Migration(migrations.Migration):

    dependencies = [
        ('gallery', '0040_remove_catalogue_info'),
    ]

    operations = [
        migrations.AlterField(
            model_name='jetty',
            name='backpanel_styles',
            field=jsonfield.fields.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='backs',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='depth',
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='doors',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='drawers',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='joints',
            field=jsonfield.fields.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='legs',
            field=jsonfield.fields.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='modules',
            field=jsonfield.fields.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='pattern',
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='row_styles',
            field=jsonfield.fields.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='supports',
            field=jsonfield.fields.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='watty',
            name='depth',
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='watty',
            name='digital_product_version',
            field=models.PositiveSmallIntegerField(
                choices=[(1, 'BILBO'), (2, 'GANDALF')],
                default=1,
                help_text='BILBO - old shelves/shoeracks, GANDALF - crow/c+',
            ),
        ),
        migrations.AlterField(
            model_name='watty',
            name='dna_name',
            field=models.CharField(blank=True, default='', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='watty',
            name='height',
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='watty',
            name='physical_product_version',
            field=models.PositiveSmallIntegerField(
                choices=[(1, 'TREX'), (2, 'RAPTOR'), (3, 'DIPLO')],
                default=custom.enums.PhysicalProductVersion['TREX'],
                help_text=(
                    'TREX - old shelves, RAPTOR - Sideboard+ T02, DIPLO - further'
                ),
            ),
        ),
        migrations.AlterField(
            model_name='watty',
            name='width',
            field=models.IntegerField(default=0),
        ),
    ]
