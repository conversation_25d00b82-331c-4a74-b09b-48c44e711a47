# Generated by Django 3.2.12 on 2022-06-27 13:46

from django.db import (
    migrations,
    models,
)

import custom.enums


class Migration(migrations.Migration):

    dependencies = [
        ('gallery', '0070_jetty_desk_beams'),
    ]

    operations = [
        migrations.AlterField(
            model_name='jetty',
            name='physical_product_version',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, 'TREX'),
                    (2, 'RAPTOR'),
                    (3, 'DIPLO'),
                    (4, 'PTERO'),
                    (5, 'BAMBI'),
                    (6, 'STEGO'),
                    (7, 'BRONTO'),
                ],
                default=custom.enums.PhysicalProductVersion['TREX'],
                help_text=(
                    'TREX - old shelves, '
                    + 'RAPTOR - 2020 Sideboard+ T02, '
                    + 'DIPLO - early 2021, '
                    + 'PTERO - drawers 3.0, '
                    + 'BAMBI - chipboard supports T01, '
                    + 'STEGO - chipboard drawer T02, '
                    + 'BRONTO - non-spring pins in backs and supports'
                ),
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='watty',
            name='physical_product_version',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, 'TREX'),
                    (2, 'RAPTOR'),
                    (3, 'DIPLO'),
                    (4, 'PTERO'),
                    (5, 'BAMBI'),
                    (6, 'STEGO'),
                    (7, 'BRONTO'),
                ],
                default=custom.enums.PhysicalProductVersion['TREX'],
                help_text=(
                    'TREX - old shelves, '
                    + 'RAPTOR - 2020 Sideboard+ T02, '
                    + 'DIPLO - early 2021, '
                    + 'PTERO - drawers 3.0, '
                    + 'BAMBI - chipboard supports T01, '
                    + 'STEGO - chipboard drawer T02, '
                    + 'BRONTO - non-spring pins in backs and supports'
                ),
            ),
        ),
    ]
