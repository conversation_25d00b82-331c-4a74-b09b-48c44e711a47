# Generated by Django 3.2.16 on 2022-12-13 12:01

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('gallery', '0081_restrict_shelf_type_choices'),
    ]

    operations = [
        migrations.AlterField(
            model_name='furnitureimage',
            name='color',
            field=models.PositiveIntegerField(
                blank=True,
                choices=[
                    (
                        0,
                        'T01 White / T02 White / T03 White / T01 Veneer Ash / T13 '
                        'White',
                    ),
                    (
                        1,
                        'T01 Black / T02 Terracotta / T03 Cashmere Beige / T01 Veneer '
                        'Oak / T13 Sand + Midnight Blue',
                    ),
                    (2, 'T02 Midnight Blue / T03 Graphite Grey / T13 Mustard Yellow'),
                    (
                        3,
                        'T01 Grey / T02 Sand + Midnight Blue / T03 Cashmere + Antique '
                        'Pink / T13 Gray',
                    ),
                    (4, 'T03 White Pink / T13 Dark Gray'),
                    (5, 'T13 White Plywood'),
                    (6, 'T01 Classic Red / T02 Matte Black / T13 Gray Plywood'),
                    (7, 'T01 Yellow / T02 Sky Blue / T13 Black Plywood'),
                    (8, 'T01 Dusty Pink / T02 Burgundy Red'),
                    (9, 'T02 Cotton Beige'),
                    (10, 'T02 Gray'),
                    (11, 'T02 Dark Gray'),
                    (12, 'T02 Mustard Yellow'),
                ],
                null=True,
            ),
        ),
    ]
