# Generated by Django 3.2.16 on 2022-10-14 14:29

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('gallery', '0083_alter_samplebox_box_variant'),
    ]

    operations = [
        migrations.AlterField(
            model_name='jetty',
            name='backpanel_styles',
            field=models.JSO<PERSON>ield(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='horizontals',
            field=models.JSO<PERSON>ield(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='joints',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='legs',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='modules',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.Alter<PERSON>ield(
            model_name='jetty',
            name='row_styles',
            field=models.J<PERSON><PERSON><PERSON>(blank=True, default=list),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='jetty',
            name='rows',
            field=models.J<PERSON><PERSON>ield(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='supports',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='verticals',
            field=models.JSONField(blank=True, default=list),
        ),
    ]
