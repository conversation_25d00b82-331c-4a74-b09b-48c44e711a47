# Generated by Django 4.1.13 on 2025-05-12 10:21

from django.db import (
    migrations,
    models,
)

import custom.enums.enums


class Migration(migrations.Migration):

    dependencies = [
        ('gallery', '0122_sotty_weight'),
    ]

    operations = [
        migrations.AlterField(
            model_name='jetty',
            name='physical_product_version',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, 'TREX'),
                    (2, 'RAPTOR'),
                    (3, 'DIPLO'),
                    (4, 'PTERO'),
                    (5, 'BAMBI'),
                    (6, 'STEGO'),
                    (7, 'BRONTO'),
                    (8, 'PACHY'),
                    (9, 'BRACHIO'),
                    (10, 'TRICE'),
                    (11, 'KARNO'),
                ],
                default=custom.enums.enums.PhysicalProductVersion['TREX'],
                help_text='TREX - old shelves, RAPTOR - 2020 Sideboard+ T02, DIPLO - early 2021, PTERO - drawers 3.0, BAMBI - chipboard supports T01, STEGO - chipboard drawer T02, BRONTO - non-spring pins in backs and supports, PACHY - 19 mm thick plywood T01, BRACHIO - chipboard drawers T01, TRICE - new handles T01v, ',
            ),
        ),
        migrations.AlterField(
            model_name='sotty',
            name='physical_product_version',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, 'TREX'),
                    (2, 'RAPTOR'),
                    (3, 'DIPLO'),
                    (4, 'PTERO'),
                    (5, 'BAMBI'),
                    (6, 'STEGO'),
                    (7, 'BRONTO'),
                    (8, 'PACHY'),
                    (9, 'BRACHIO'),
                    (10, 'TRICE'),
                    (11, 'KARNO'),
                ],
                default=custom.enums.enums.PhysicalProductVersion['TREX'],
                help_text='TREX - old shelves, RAPTOR - 2020 Sideboard+ T02, DIPLO - early 2021, PTERO - drawers 3.0, BAMBI - chipboard supports T01, STEGO - chipboard drawer T02, BRONTO - non-spring pins in backs and supports, PACHY - 19 mm thick plywood T01, BRACHIO - chipboard drawers T01, TRICE - new handles T01v, ',
            ),
        ),
        migrations.AlterField(
            model_name='watty',
            name='physical_product_version',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, 'TREX'),
                    (2, 'RAPTOR'),
                    (3, 'DIPLO'),
                    (4, 'PTERO'),
                    (5, 'BAMBI'),
                    (6, 'STEGO'),
                    (7, 'BRONTO'),
                    (8, 'PACHY'),
                    (9, 'BRACHIO'),
                    (10, 'TRICE'),
                    (11, 'KARNO'),
                ],
                default=custom.enums.enums.PhysicalProductVersion['TREX'],
                help_text='TREX - old shelves, RAPTOR - 2020 Sideboard+ T02, DIPLO - early 2021, PTERO - drawers 3.0, BAMBI - chipboard supports T01, STEGO - chipboard drawer T02, BRONTO - non-spring pins in backs and supports, PACHY - 19 mm thick plywood T01, BRACHIO - chipboard drawers T01, TRICE - new handles T01v, ',
            ),
        ),
    ]
