import base64

from datetime import datetime
from decimal import Decimal
from typing import (
    TYPE_CHECKING,
    Any,
    Final,
    Optional,
    Type,
    Union,
)

from django.conf import settings
from django.contrib.contenttypes.fields import GenericRelation
from django.core.exceptions import ValidationError
from django.core.files.base import ContentFile
from django.db import models
from django.db.models import (
    Q,
    QuerySet,
)
from django.db.models.fields.files import FieldFile
from django.urls import (
    reverse_lazy,
    translate_url,
)

from sorl.thumbnail import ImageField
from sorl.thumbnail.shortcuts import get_thumbnail

from custom import enums as custom_enums
from custom.enums import (
    ColorEnum,
    ShelfType,
)
from custom.enums.colors import Sofa01Color
from custom.utils.dimensions import Dimensions
from dynamic_delivery.choices import ShipInRangeChoice
from gallery import enums
from gallery.data_from_ps import get_weight_net
from gallery.enums import (
    Fabric,
    FurnitureCategory,
    FurnitureImageType,
    FurnitureStatusEnum,
    ShelfPatternEnum,
    SottyModuleType,
)
from gallery.exceptions import GenericForeignKeyProtectedException
from orders.choices import OrderSource
from pricing_v3.recycle_tax_rates import get_recycle_tax_value
from producers.choices import ProductStatus
from regions.cached_region import CachedRegionData
from regions.mixins import RegionCalculationsObject
from regions.models import (
    Currency,
    Region,
)
from regions.types import RegionLikeObject

if TYPE_CHECKING:
    from carts.models import Cart
    from orders.models import Order
    from promotions.models import Promotion


class SellableItemQueryset(models.QuerySet):
    def delete(self):
        if self.filter(catalogue_entry__isnull=False).exists():
            raise GenericForeignKeyProtectedException

    def filter_public_and_private_for(
        self, owner_id: Optional[int]
    ) -> QuerySet['SellableFurnitureAbstract']:
        """
        Returns all public and items owned by admin
        if `owner_id` is given, then also items owned by it.
        """
        from user_profile.constants import ADMIN_ID

        conditions = Q(owner_id=ADMIN_ID) | Q(preset=True)
        if owner_id:
            conditions |= Q(owner_id=owner_id)
        return self.filter(conditions)


class SellableItemManager(models.Manager):
    def get_queryset_class(self) -> Type[models.QuerySet]:
        return SellableItemQueryset

    def get_queryset(self) -> models.QuerySet:
        queryset_class = self.get_queryset_class()
        return queryset_class(self.model, using=self._db).filter(deleted=False)

    def update_for_grid(self, furniture_ids):
        queryset = self.get_queryset().filter(id__in=furniture_ids)
        queryset.update(preset=True, furniture_status=FurnitureStatusEnum.SPECIAL)


class SellableItemWithSoftDeletedManager(SellableItemManager):
    def get_queryset(self):
        return models.Manager.get_queryset(self)


class SellableItemStatisticsMixin(models.Model):
    """Fields that are used for analytics purposes"""

    base_preset = models.IntegerField(null=True, blank=True)
    grid_preset = models.IntegerField(null=True, blank=True)
    components = models.JSONField(default=dict, blank=True)

    class Meta:
        abstract = True


class SellableItemAbstract(models.Model):
    custom_order = models.BooleanField(
        default=False,
        help_text='Item has been customized manually',
    )

    price = models.FloatField('Furniture price', default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    description = models.TextField(blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted = models.BooleanField(default=False, db_index=True)

    created_platform = models.IntegerField(
        choices=OrderSource.choices,
        default=OrderSource.UNKNOWN_SOURCE,
    )

    ordered_item = GenericRelation('orders.OrderItem')
    catalogue_entry = GenericRelation('catalogue.CatalogueEntry')
    additional_images = GenericRelation(
        'gallery.FurnitureImage',
        content_type_field='furniture_content_type',
        object_id_field='furniture_object_id',
    )

    objects = SellableItemManager()
    objects_with_soft_deleted = SellableItemWithSoftDeletedManager()

    class Meta:
        abstract = True

    def get_delivery_time_max_week(self, *args, **kwargs):
        raise NotImplementedError

    def get_item_url(self):
        raise NotImplementedError

    def get_item_translated_url(self, language):
        url = self.get_item_url()
        return translate_url(url, language)

    def get_shelf_price_as_number(
        self, region, for_datetime: Optional[datetime] = None
    ):
        return 0

    def get_regionalized_price(
        self,
        region: Optional[RegionLikeObject] = None,
        currency: Optional[Currency] = None,
        region_calculations_object: Optional[RegionCalculationsObject] = None,
    ) -> Decimal:
        if not region_calculations_object:
            region_calculations_object = RegionCalculationsObject(region)
        price = self.get_shelf_price_as_number(region)
        return region_calculations_object.calculate_regionalized(price)

    def get_regionalized_price_display(
        self,
        region: Optional[CachedRegionData] = None,
        region_calculations_object: Optional[RegionCalculationsObject] = None,
    ) -> str:
        regionalized_price = self.get_regionalized_price(
            region=region,
            region_calculations_object=region_calculations_object,
        )
        return region.get_format_price(regionalized_price)

    def get_sale_price(
        self,
        regular_price: Decimal,
        promotion: Optional['Promotion'] = None,
    ) -> Decimal:
        promotion_value = promotion.promo_code.value if promotion else 0
        discount_value = Decimal(1 - Decimal(promotion_value) / 100)
        sale_price = regular_price * discount_value

        if promotion:
            for discount in promotion.promo_code.discounts.all():
                if discount.check_conditions(self):
                    sale_price = discount.calculate_price(regular_price)

        return sale_price

    @property
    def is_sku_product(self):
        return False


class SellableFurnitureAbstract(
    SellableItemStatisticsMixin, SellableItemAbstract, models.Model
):
    furniture_status = models.IntegerField(
        default=FurnitureStatusEnum.DRAFT,
        choices=FurnitureStatusEnum.choices,
        db_index=True,
    )
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        help_text='owner of shelf',
        on_delete=models.CASCADE,
    )
    preset = models.BooleanField(default=False, db_index=True)
    preset_initial_state = models.BooleanField(default=False, db_index=True)

    class Meta:
        base_manager_name = 'objects'
        abstract = True

    def __str__(self):
        return '%s %s configuration for: %s' % (
            self.get_furniture_type(),
            self.id,
            self.owner,
        )

    def delete(self, using=None, keep_parents=False, force_delete=False):
        if force_delete:
            super().delete(using, keep_parents)
        else:
            self.deleted = True
            self.save(update_fields=['deleted'])

    def get_pretty_id_name(self):
        return self.__class__.__name__.lower()

    def get_title(self):
        # lets always return default title, for old items.
        return self.default_title

    def get_weight(
        self,
        region: Region = None,
        cart_or_order: Union['Order', 'Cart'] | None = None,
    ) -> float:
        return get_weight_net(self, cart_or_order=cart_or_order)

    def get_weight_from_ps(self):
        try:
            return get_weight_net(self)
        except (AttributeError, KeyError):
            return 0

    def get_weight_from_cached_serialization(self):
        order_item = self.order_items.first()
        if not order_item:
            return 0
        product = order_item.product_set.exclude(status=ProductStatus.ABORTED).last()
        if not product:
            return 0
        try:
            return product.details.cached_serialization['item'].get('weight', 0)
        except Exception:
            return self.get_weight_from_ps()

    def get_accurate_weight_gross(self):
        raise NotImplementedError('get_accurate_weight_gross must be implemented')

    def get_estimated_weight_gross(self):
        return 0

    def get_delivery_time_summary(self):
        service = self.get_delivery_time_matrix_service()
        return service.get_matrix_as_list()

    def get_delivery_time_matrix_service(self):
        pass

    def get_delivery_time_max_week(self, *args, **kwargs):
        ship_in_range = self.get_delivery_time_weeks_range(*args, **kwargs)
        return ship_in_range['max']

    def get_delivery_time_days(self, *args, **kwargs):
        ship_in_range = self.get_delivery_time_weeks_range(*args, **kwargs)
        return ship_in_range['max'] * 7

    def get_delivery_time_weeks_range(
        self, region=None, use_extended_delivery=False, *args, **kwargs
    ) -> dict[str, int]:
        from dynamic_delivery.services.ship_in_range import extend_ship_in_range

        production_time_range = self._get_ship_in_range(*args, region=region, **kwargs)
        if use_extended_delivery:
            return extend_ship_in_range(
                ship_in_range=production_time_range
            ).get_weeks_range()
        return production_time_range.get_weeks_range()

    def _get_ship_in_range(self, *args, **kwargs):
        return ShipInRangeChoice.WEEKS_3_4

    def get_material_description(self):
        """
        Returns list of localised materials including colours
        :return: :class:`dict` {colour{name,html}, material}
        """
        return {}

    def get_dimensions(self):
        """
        Returns furniture dimensions
        :return: :class:`Dimensions` instance
        """
        return Dimensions()

    @staticmethod
    def get_price_netto(brutto: Union[Decimal, float]):
        if isinstance(brutto, Decimal):
            netto = brutto / Decimal(1.23)
        else:
            netto = brutto / 1.23
        return Decimal(netto).quantize(Decimal('.01'))

    def get_shelf_price_netto_as_number(self, region: Region):
        shelf_price = self.get_shelf_price_as_number(region=region)
        return self.get_price_netto(brutto=shelf_price)

    def get_assembly_price(
        self,
        country: Optional[str] = None,
        region: Optional[Region] = None,
        for_datetime: Optional[datetime] = None,
    ):
        return 0

    def get_delivery_price(self, region=None):
        return 0

    def get_absolute_url(self):
        return reverse_lazy(
            'front-product',
            args=[
                self.get_pretty_id_name().split()[0],
                self.id,
            ],
        )

    @property
    def translated_material_name(self) -> None:
        return None

    def get_discounts_overall(self):
        return {
            'percentage_amount_discount': 0.0,
            'delivery_time_reduction': 0,
        }

    def get_region(self, region):
        if region is None:
            region = self.owner.profile.get_region()
        return region

    def get_additional_images(self):
        return []

    def additional_data_for_boards(self):
        return dict(
            price=self.price,
            brand=self.get_pattern_name(),
            category=self.furniture_category
            if self.furniture_category is not None
            else '',
            imp_item_status_height=self.get_height(),
            imp_item_status_width=self.get_width(),
            imp_item_status_depth=self.get_depth(),
        )

    @property
    def assembly_service(self):
        order_item = self.ordered_item.first()
        if order_item:
            return order_item.order.assembly
        return False

    @property
    def is_assembly_service_required(self) -> bool:
        return False

    def set_as_preset(self):
        self.preset = True
        self.furniture_status = FurnitureStatusEnum.SPECIAL.value
        self.save(update_fields=['preset', 'furniture_status'])

    def duplicate(self) -> int:
        """Used for duplicating jetties and watties

        Returns: the id of the new furniture
        """
        self.pk = None
        self.id = None
        self._state.adding = True
        self.save()
        return self.id

    @property
    def is_s01(self) -> bool:
        return getattr(self, 'shelf_type', None) == ShelfType.SOFA_TYPE01

    @property
    def is_t03_wardrobe(self) -> bool:
        return getattr(self, 'shelf_type', None) == ShelfType.TYPE03

    @property
    def is_generating_unreal_image_possible(self) -> bool:
        return True


class FurnitureAbstract(SellableFurnitureAbstract):
    # image fields
    preview = ImageField(
        'Preview file',
        upload_to='gallery/sellable_item_abstract/%Y/%m',
        blank=True,
        null=True,
    )
    preview_webp = ImageField(
        upload_to='gallery/sellable_item_abstract/%Y/%m',
        blank=True,
        null=True,
    )

    # cached values, dimensions in mm
    width = models.IntegerField(default=0)
    height = models.IntegerField(default=0)
    depth = models.IntegerField(default=0)

    # material and styles
    material = models.IntegerField(default=0)

    # geometry fields
    configurator_params = models.JSONField(default=dict, blank=True)

    # dna and serialization/project
    dna_object = models.IntegerField(blank=True, null=True)
    dna_name = models.CharField(max_length=100, default='', blank=True, null=True)

    # grid fields
    grid_all_colors = models.FileField(
        upload_to='gallery/furniture_abstract/%Y/%m',
        blank=True,
        null=True,
    )
    grid_all_colors_webp = models.FileField(
        upload_to='gallery/furniture_abstract/%Y/%m',
        blank=True,
        null=True,
    )

    physical_product_version = models.PositiveSmallIntegerField(
        default=custom_enums.PhysicalProductVersion.DEFAULT,
        choices=custom_enums.PhysicalProductVersion.choices(),
        help_text=(
            'TREX - old shelves, '
            + 'RAPTOR - 2020 Sideboard+ T02, '
            + 'DIPLO - early 2021, '
            + 'PTERO - drawers 3.0, '
            + 'BAMBI - chipboard supports T01, '
            + 'STEGO - chipboard drawer T02, '
            + 'BRONTO - non-spring pins in backs and supports, '
            + 'PACHY - 19 mm thick plywood T01, '
            + 'BRACHIO - chipboard drawers T01, '
            + 'TRICE - new handles T01v, '
        ),
    )

    class Meta(object):
        abstract = True

    def save(self, *args, **kwargs):
        # create unreal studio image for every new Furniture
        order_preview = bool(not self.pk) and not self.deleted
        super().save(*args, **kwargs)
        if order_preview:
            from render_tasks.tasks import order_unreal_preview

            order_unreal_preview.delay(
                furniture_type=self.get_furniture_type(),
                furniture_id=self.pk,
            )

    @property
    def furniture_category(self):
        return FurnitureCategory(self.shelf_category)

    def get_thumbnail(self, geometry_string='700x525'):
        if self.preview:
            return get_thumbnail(self.preview, geometry_string)
        return None

    @property
    def grid_all_colors_render(self):
        return self.get_image_by_type(FurnitureImageType.RENDER)

    @property
    def grid_all_colors_render_small(self):
        return self.get_image_by_type(FurnitureImageType.RENDER_SMALL)

    @property
    def grid_all_colors_hover_render(self):
        return self.get_image_by_type(FurnitureImageType.HOVER_RENDER)

    @property
    def grid_all_colors_hover_render_small(self):
        return self.get_image_by_type(FurnitureImageType.HOVER_RENDER_SMALL)

    @property
    def s4l_render(self):
        return self.get_image_by_type(FurnitureImageType.S4L_RENDER)

    @property
    def s4l_unreal_scene(self):
        return self.get_image_by_type(FurnitureImageType.S4L_UNREAL_SCENE)

    @property
    def s4l_unreal_blender(self):
        return self.get_image_by_type(FurnitureImageType.S4L_UNREAL_BLENDER)

    @property
    def grid_all_colors_lg(self):
        return self.get_image_by_type(FurnitureImageType.GRID_LARGE)

    @property
    def configurator_preview(self):
        return self.get_image_by_type(FurnitureImageType.UNREAL_CONFIGURATOR_PREVIEW)

    @property
    def technical_sketch(self):
        return self.get_image_by_type(FurnitureImageType.TECHNICAL_SKETCH)

    @property
    def gallery_desktop_image(self):
        return self.get_image_by_type(FurnitureImageType.UNREAL_GALLERY_DESKTOP)

    @property
    def gallery_mobile_image(self):
        return self.get_image_by_type(FurnitureImageType.UNREAL_GALLERY_MOBILE)

    @property
    def color(self) -> ColorEnum:
        """
        Returns ColorEnum related to shelf_type.

        Example:
        shelf_type = 0 and material = 6 -> Type01Color.RED
        shelf_type = 1 and material = 6 -> Type02Color.MATTE_BLACK
        """
        shelf_type = ShelfType(self.shelf_type)
        return shelf_type.colors(self.material)

    @property
    def translated_material_name(self) -> str:
        return self.color.translated_color

    def get_recycle_tax_value(
        self,
        cart_or_order: Union['Order', 'Cart'] | None = None,
    ) -> float:
        return get_recycle_tax_value(self.get_weight(cart_or_order=cart_or_order))

    def get_regionalized_price(
        self,
        region: Optional[RegionLikeObject] = None,
        currency: Optional[Currency] = None,
        region_calculations_object: Optional[RegionCalculationsObject] = None,
    ):
        """
        Round regionalized price to bucket if applicable for a given region.

        Region Calculations Object may be passed to avoid additional queries.
        """
        if not region_calculations_object:
            region_calculations_object = RegionCalculationsObject(region)

        price = self.get_shelf_price_as_number(region)
        return region_calculations_object.calculate_regionalized(price)

    def set_images_from_base64(
        self,
        field: FieldFile,
        field_webp: FieldFile,
        magic_preview: str,
        file_name: str,
    ) -> None:
        if not magic_preview:
            return
        decoded_magic_preview = base64.b64decode(magic_preview)
        field.save(f'{file_name}.png', ContentFile(decoded_magic_preview))
        if field_webp:
            field_webp.save(
                f'{file_name}.webp',
                ContentFile(decoded_magic_preview),
            )
        self.save()

    def set_preview(
        self,
        magic_preview: str,
        file_name: str,
        webp: bool = True,
    ) -> None:
        webp_field = self.preview_webp if webp else None
        self.set_images_from_base64(
            field=self.preview,
            field_webp=webp_field,
            magic_preview=magic_preview,
            file_name=file_name,
        )

    def set_grid_image(
        self,
        magic_preview: str,
        file_name: str,
        webp: bool = True,
    ) -> None:
        webp_field = self.preview_webp if webp else None
        self.set_images_from_base64(
            field=self.grid_all_colors,
            field_webp=webp_field,
            magic_preview=magic_preview,
            file_name=file_name,
        )

    def get_material(self):
        """Returns translated material type (plywood/veneer/none)."""
        return ShelfType(self.shelf_type).wooden_materials

    def set_material(self, material: int, with_save: bool = True) -> None:
        """set without storing in db"""
        self.material = material
        self.configurator_params['material'] = material
        if with_save:
            self.save(update_fields=['material'])

    def get_image_by_type(self, image_type):
        """
        Get last image based on image type.
        Using .filter() make additional db hits and ignores prefetched queryset.
        To avoid this we are using list comprehension on a prefetched queryset.
        """
        filtered_images = [
            image for image in self.additional_images.all() if image.type == image_type
        ]
        return filtered_images[-1] if filtered_images else None

    def delete(self, using=None, keep_parents=False, force_delete=False):
        if self.catalogue_entry.exists():
            raise GenericForeignKeyProtectedException
        return super().delete(using, keep_parents, force_delete)

    def clean(self):
        shelf_type = ShelfType(self.shelf_type)
        if self.material not in shelf_type.colors.values():
            raise ValidationError({'material': 'Material does not match Shelf Type.'})

    def get_url_with_region(self, region: RegionLikeObject) -> str:
        if isinstance(region, CachedRegionData):
            country_code = region.country_code.lower()
        else:
            country_code = region.country.code.lower()
        url = self.get_absolute_url().split('/')
        url[1] = f'{url[1]}-{country_code}'
        return '/'.join(url)


class JettyAbstract(FurnitureAbstract):
    # shared configurator types
    configurator_type = models.PositiveSmallIntegerField(
        choices=enums.ConfiguratorTypeEnum.choices(),
        default=enums.ConfiguratorTypeEnum.ROW,
    )
    shelf_type = models.IntegerField(
        default=ShelfType.TYPE01.value,
        help_text='0 - Type 01, 1 - Type 02, 2 - Type 01 Veneer',
        choices=custom_enums.ShelfType.jetty_choices(),
    )
    shelf_category = models.CharField(
        max_length=150,
        choices=FurnitureCategory.jetty_choices(),
        default='',  # Handle remaining null values
        blank=True,
    )
    pattern = models.IntegerField(
        choices=ShelfPatternEnum.choices,
        default=ShelfPatternEnum.SLANT,
    )

    # geometry fields
    doors = models.JSONField(default=list, blank=True)
    backs = models.JSONField(default=list, blank=True)
    drawers = models.JSONField(default=list, blank=True)
    verticals = models.JSONField(default=list, blank=True)
    horizontals = models.JSONField(default=list, blank=True)
    supports = models.JSONField(default=list, blank=True)
    modules = models.JSONField(default=list, blank=True)  # TODO: remove
    rows = models.JSONField(default=list, blank=True)
    joints = models.JSONField(default=list, blank=True)  # TODO: remove
    legs = models.JSONField(default=list, blank=True)
    inserts = models.JSONField(default=list, blank=True)
    plinth = models.JSONField(default=list, blank=True)
    long_legs = models.JSONField(default=list, blank=True)
    cable_management = models.JSONField(default=list, blank=True)
    desk_beams = models.JSONField(default=list, blank=True)
    row_styles = models.JSONField(default=list, blank=True)
    property1 = models.FloatField(blank=True, default=0)

    # configurator-specific fields
    backpanel_styles = models.JSONField(default=list, blank=True, null=True)
    row_amount = models.IntegerField(default=0)
    max_capacity = models.IntegerField(default=0)

    class Meta:
        abstract = True

    def set_shelf_type(self, shelf_type: int, with_save: bool = True) -> None:
        """set without storing in db"""
        self.shelf_type = shelf_type
        if with_save:
            self.save(update_fields=['shelf_type'])


class SampleBoxAbstract(SellableFurnitureAbstract):
    box_variant = models.ForeignKey(
        'warehouse.SampleBoxVariant',
        on_delete=models.PROTECT,
        null=True,
    )
    cost = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name='Cost (PLN)',
    )
    logistic_cost = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name='Logistic Cost (PLN)',
    )
    production_cost = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name='Production Cost (PLN)',
    )

    objects = SellableItemManager()

    class Meta:
        abstract = True

    @property
    def is_material_type(self) -> bool:
        return self.box_variant.is_sofa_sample if self.box_variant else False

    @property
    def is_generating_unreal_image_possible(self) -> bool:
        return False


class SottyAbstract(FurnitureAbstract):
    shelf_category = models.CharField(
        max_length=150,
        choices=FurnitureCategory.sotty_choices(),
        default=FurnitureCategory.TWO_SEATER,
    )

    pattern = None
    materials = models.JSONField(default=list)

    # geometry fields
    armrests = models.JSONField(default=list, blank=True)
    # French version vs American chaise-lounge
    chaise_longues = models.JSONField(default=list, blank=True)
    corners = models.JSONField(default=list, blank=True)
    footrests = models.JSONField(default=list, blank=True)
    seaters = models.JSONField(default=list, blank=True)

    covers_only = models.BooleanField(default=False)
    weight = models.FloatField(null=True, blank=True)

    # Consts
    GEOMETRY_FIELDS: Final[tuple[str, ...]] = (
        'armrests',
        'chaise_longues',
        'corners',
        'footrests',
        'seaters',
    )

    # enums
    class Orientation(models.TextChoices):
        SYMMETRICAL = 'symmetrical'
        LEFT = 'left'
        RIGHT = 'right'

    class Meta:
        abstract = True

    def clean(self):
        shelf_type = ShelfType(self.shelf_type)
        if any(
            material not in shelf_type.colors.values() for material in self.materials
        ):
            raise ValidationError({'material': 'Material does not match Shelf Type.'})

    @property
    def shelf_type(self) -> ShelfType:
        return ShelfType.SOFA_TYPE01

    @property
    def configurator_type(self) -> enums.ConfiguratorTypeEnum:
        return enums.ConfiguratorTypeEnum.SOFA

    def get_configurator_type_display(self) -> str:
        return enums.ConfiguratorTypeEnum.SOFA.name

    @property
    def material(self) -> int:
        if len(self.materials) == 1:
            return self.materials[0]
        elif len(self.materials) > 1:
            return Sofa01Color.MULTICOLOR.value
        else:
            return Sofa01Color.REWOOL2_BROWN.value

    @property
    def has_corduroy_material(self) -> bool:
        corduroy_materials = Sofa01Color.get_corduroy_colors()
        return any(material in corduroy_materials for material in self.materials)

    @property
    def has_leather_material(self) -> bool:
        corduroy_materials = Sofa01Color.get_leather_colors()
        return any(material in corduroy_materials for material in self.materials)

    @property
    def has_wool_material(self) -> bool:
        corduroy_materials = Sofa01Color.get_wool_colors()
        return any(material in corduroy_materials for material in self.materials)

    @property
    def fabric(self) -> Fabric:
        # business rule: sotty can have only one fabric material
        if self.has_corduroy_material:
            return Fabric.CORDUROY
        elif self.has_wool_material:
            return Fabric.WOOL
        elif self.has_leather_material:
            return Fabric.LEATHER
        else:
            # fallback
            return Fabric.WOOL

    @property
    def orientation(self) -> Type[Orientation] | None:
        if self.configurator_params.get('symmetrical'):
            return self.Orientation.SYMMETRICAL
        try:
            return self.Orientation(self.configurator_params.get('direction'))
        except ValueError:
            return None

    @property
    def has_extended_chaise_longue_module(self) -> bool:
        return self._has_module_type(module_type=SottyModuleType.EXTENDED_CHAISE_LONGUE)

    @property
    def has_footrest_module(self) -> bool:
        return self._has_module_type(module_type=SottyModuleType.FOOTREST)

    @property
    def has_seater_module(self) -> bool:
        return self._has_module_type(module_type=SottyModuleType.SEATER)

    def set_material(self, material: int, with_save: bool = True) -> None:
        """only for setting one color"""
        if material == Sofa01Color.MULTICOLOR:
            raise ValueError('Cannot set multicolor material for sotty')

        material_keys = {'material', 'material_cushion', 'material_backrest'}

        def set_material_keys(obj: dict | None) -> None:
            if obj is None:
                obj = {}
            for key in material_keys:
                if key in obj:
                    obj[key] = material

        self.materials = [material]
        set_material_keys(self.configurator_params.get('material'))
        set_material_keys(
            self.configurator_params.get('decoder_params', {}).get('default_material')
        )
        for obj in self.configurator_params.get('layout', []):
            for module in obj.get('modules', []):
                set_material_keys(module)
        for geometry_field in self.GEOMETRY_FIELDS:
            for module in getattr(self, geometry_field, []):
                set_material_keys(module)
        for obj in getattr(self, 'components', []):
            set_material_keys(obj)

        if with_save:
            self.save(update_fields=['materials'])

    def set_shelf_type(self, **kwargs: Any) -> None:
        """mock setting shelf_type"""
        pass

    def _has_module_type(self, module_type: ...) -> bool:
        for obj in self.configurator_params.get('layout', []):
            for module in obj.get('modules', []):
                if module.get('type') == module_type:
                    return True

        return False

    def get_module_types(self) -> tuple[SottyModuleType]:
        return tuple(
            SottyModuleType(module['type'])
            for obj in self.configurator_params.get('layout', [])
            for module in obj.get('modules', [])
            if module.get('type')
        )

    @property
    def is_single_module(self) -> bool:
        return len(self.get_module_types()) == 1

    def get_url_with_region(self, region: RegionLikeObject) -> str:
        if self.shelf_category == FurnitureCategory.COVER:
            if isinstance(region, CachedRegionData):
                country_code = region.country_code.lower()
            else:
                country_code = region.country.code.lower()
            url = self._get_cover_url().split('/')
            url[1] = f'{url[1]}-{country_code}'
            return '/'.join(url)
        return super().get_url_with_region(region)

    @property
    def is_generating_unreal_image_possible(self) -> bool:
        return self.shelf_category != FurnitureCategory.COVER

    def _get_cover_url(self) -> str:
        return reverse_lazy(
            'front-product-sotty-cover',
            args=[
                self.id,
            ],
        )


class WattyAbstract(FurnitureAbstract):
    # shared configurator types
    configurator_type = models.PositiveSmallIntegerField(
        choices=enums.ConfiguratorTypeEnum.choices(),
        default=enums.ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
    )  # TODO: could be removed on behalf on property
    shelf_type = models.PositiveSmallIntegerField(
        default=ShelfType.TYPE03.value,
        choices=ShelfType.watty_choices(),
    )
    shelf_category = models.CharField(
        max_length=150,
        choices=FurnitureCategory.watty_choices(),
        default=FurnitureCategory.WARDROBE,
    )

    # geometry fields
    doors = models.JSONField(default=list, blank=True)
    backs = models.JSONField(default=list, blank=True)
    drawers = models.JSONField(default=list, blank=True)
    walls = models.JSONField(default=list, blank=True)
    slabs = models.JSONField(default=list, blank=True)
    frame = models.JSONField(default=list, blank=True)
    bars = models.JSONField(default=list, blank=True)
    hinges = models.JSONField(default=list, blank=True)
    masking_bars = models.JSONField(default=list, blank=True)
    lighting = models.JSONField(default=list, blank=True)
    cable_management = models.JSONField(default=list, blank=True)
    legs = models.JSONField(default=list, blank=True)

    class Meta:
        abstract = True

    @property
    def pattern(self) -> int:
        """Watty doesn't have a pattern.

        In order not to break stuff, return default value as it was in the db before.
        """

        return 0

    @property
    def is_assembly_service_required(self) -> bool:
        return (
            self.shelf_type == ShelfType.TYPE03
            and self.shelf_category == FurnitureCategory.WARDROBE
        )

    def set_shelf_type(self, shelf_type: int, with_save: bool = True) -> None:
        """set without storing in db"""
        self.shelf_type = shelf_type
        if with_save:
            self.save(update_fields=['shelf_type'])
