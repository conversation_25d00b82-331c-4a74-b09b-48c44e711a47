from rest_framework import serializers

from gallery.enums import FurnitureCategory
from gallery.models import (
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
)


class ImportExportBaseSerializer(serializers.ModelSerializer):
    class Meta:
        abstract = True
        fields = (
            'configurator_params',
            'configurator_type',
            'depth',
            'dna_name',
            'dna_object',
            'height',
            'material',
            'owner',
            'pattern',
            'physical_product_version',
            'shelf_type',
            'width',
        )

    def create(self, data):
        obj = self.Meta.model(**data)
        obj.full_clean()
        obj.save()
        return obj


class JettyImportExportSerializer(ImportExportBaseSerializer):
    verticals = serializers.JSONField()
    horizontals = serializers.JSONField()
    supports = serializers.JSONField(required=False)
    rows = serializers.JSONField(default=[100] * 10, required=False)
    modules = serializers.JSONField(required=False)
    pattern = serializers.IntegerField(default=-1, required=False)
    property1 = serializers.FloatField(default=0, required=False)
    category = serializers.CharField(source='furniture_category', read_only=True)
    pattern_name = serializers.CharField(source='get_pattern_name', read_only=True)
    color_name = serializers.CharField(
        source='translated_material_name', read_only=True
    )
    joints = serializers.JSONField(required=False)
    doors = serializers.JSONField(required=False)
    backs = serializers.JSONField(required=False)
    legs = serializers.JSONField(required=False)
    drawers = serializers.JSONField(required=False)
    desk_beams = serializers.JSONField(required=False)
    row_styles = serializers.JSONField(required=False)
    backpanel_styles = serializers.JSONField(required=False)
    shelf_category = serializers.CharField(allow_null=True)

    class Meta:
        model = Jetty
        fields = ImportExportBaseSerializer.Meta.fields + (
            'backpanel_styles',
            'backs',
            'base_preset',
            'cable_management',
            'category',
            'color_name',
            'components',
            'created_at',
            'created_platform',
            'custom_order',
            'deleted',
            'desk_beams',
            'doors',
            'drawers',
            'furniture_status',
            'grid_preset',
            'horizontals',
            'inserts',
            'joints',
            'legs',
            'long_legs',
            'max_capacity',
            'modules',
            'pattern_name',
            'plinth',
            'preset',
            'preset_initial_state',
            'price',
            'property1',
            'row_amount',
            'row_styles',
            'rows',
            'shelf_category',
            'supports',
            'updated_at',
            'verticals',
        )

    def validate(self, attrs):
        validated_data = super().validate(attrs)
        validated_data['shelf_category'] = attrs.get('shelf_category') or attrs.get(
            'category', FurnitureCategory.BOOKCASE
        )
        return validated_data


class WattyImportExportSerializer(serializers.ModelSerializer):
    class Meta:
        model = Watty
        fields = ImportExportBaseSerializer.Meta.fields + (
            'backs',
            'bars',
            'cable_management',
            'doors',
            'drawers',
            'frame',
            'hinges',
            'masking_bars',
            'slabs',
            'walls',
            'lighting',
            'legs',
        )


class SottyImportExportSerializer(serializers.ModelSerializer):
    class Meta:
        model = Sotty
        fields = ImportExportBaseSerializer.Meta.fields + (
            'materials',
            'armrests',
            'chaise_longues',
            'corners',
            'footrests',
            'seaters',
            'covers_only',
        )
