from rest_framework import serializers

from custom.utils.grid import is_new
from gallery.models import (
    <PERSON>y,
    <PERSON>y,
)
from gallery.models.furniture_abstract import FurnitureAbstract
from gallery.services.prices_for_serializers import get_region_price_with_discount
from gallery.slugs import get_title_for_furniture_influencer
from promotions.models import Promotion
from regions.mixins import RegionCalculationsObject
from regions.types import RegionLikeObject


class BaseFurnitureInfluencerSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    region_price = serializers.SerializerMethodField()
    region_price_with_discount = serializers.SerializerMethodField()
    is_new = serializers.SerializerMethodField()
    size = serializers.SerializerMethodField()
    url = serializers.SerializerMethodField()
    image = serializers.SerializerMethodField()

    class Meta:
        abstract = True
        fields = [
            'id',
            'name',
            'image',
            'region_price',
            'region_price_with_discount',
            'is_new',
            'size',
            'url',
        ]

    @property
    def region(self) -> RegionLikeObject:
        return self.context['region']

    @property
    def promotion(self) -> Promotion | None:
        return self.context['promotion']

    @property
    def rco(self) -> RegionCalculationsObject:
        return self.context['rco']

    @property
    def language(self) -> RegionCalculationsObject:
        return self.context['language']

    def get_name(self, obj: FurnitureAbstract) -> str:
        return get_title_for_furniture_influencer(obj, self.language)

    def get_region_price(self, obj: FurnitureAbstract) -> str:
        return str(
            obj.get_regionalized_price(
                region=self.region,
                region_calculations_object=self.rco,
            )
        )

    def get_region_price_with_discount(self, obj: FurnitureAbstract) -> str:
        return str(
            get_region_price_with_discount(
                obj,
                self.region,
                region_calculations_object=self.rco,
                promotion=self.promotion,
            )
        )

    def get_size(self, obj: FurnitureAbstract) -> str:
        return '{} cm x {} cm {} cm'.format(
            obj.get_width(), obj.get_height(), obj.get_depth()
        )

    def get_url(self, obj: FurnitureAbstract) -> str:
        return obj.get_url_with_region(self.region)

    def get_image(self, obj: FurnitureAbstract) -> str | None:
        if unreal := obj.s4l_unreal_scene:
            return unreal.url
        elif obj.preview:
            return obj.preview.url

    def get_is_new(self, obj: FurnitureAbstract) -> bool:
        return is_new(furniture=obj)


class JettyInfluencerSerializer(BaseFurnitureInfluencerSerializer):
    class Meta(BaseFurnitureInfluencerSerializer.Meta):
        model = Jetty


class WattyInfluencerSerializer(BaseFurnitureInfluencerSerializer):
    class Meta(BaseFurnitureInfluencerSerializer.Meta):
        model = Watty
