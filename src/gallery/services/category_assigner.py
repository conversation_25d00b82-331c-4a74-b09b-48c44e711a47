from typing import TYPE_CHECKING

from gallery.enums import FurnitureCategory

if TYPE_CHECKING:
    from gallery.models import Sotty


def determine_sofa_category(sotty: 'Sotty') -> FurnitureCategory:
    has_corner = len(sotty.corners) > 0
    has_chaise_longue = len(sotty.chaise_longues) > 0

    if sotty.covers_only:
        return FurnitureCategory.COVER

    if (sotty.has_extended_chaise_longue_module and sotty.modules_number == 2) or (
        not sotty.footrests and sotty.modules_number == 1
    ):
        return FurnitureCategory.ARMCHAIR

    if has_corner:
        return FurnitureCategory.CORNER

    if has_chaise_longue or sotty.has_extended_chaise_longue_module:
        return FurnitureCategory.CHAISE_LONGUE

    # No corners neither chaise longue
    seating_width = sotty.get_seating_width()
    if seating_width >= 2_320:
        return FurnitureCategory.FOUR_PLUS_SEATER
    elif seating_width >= 1_740:
        return FurnitureCategory.THREE_SEATER
    elif seating_width >= 1_160:
        return FurnitureCategory.TWO_SEATER

    return FurnitureCategory.FOOTREST_AND_MODULES
