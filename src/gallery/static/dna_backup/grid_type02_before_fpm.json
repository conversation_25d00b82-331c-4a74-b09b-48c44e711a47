{"superior_object_type": "container", "serialization": {"container": {"7": {"setups": {"1": {"configs": [{"parameters": {"config_id": 4, "row_style_table": {"11": {"#fill_b": "O", "#fill_a": "O"}, "13": {"#fill_b": "D", "#fill_a": "D"}, "12": {"#fill_b": "O", "#fill_a": "D"}, "21": {"#fill_b": "O", "#fill_a": "T"}, "22": {"#fill_b": "T", "#fill_a": "D"}, "31": {"#fill_b": "T", "#fill_a": "T"}}, "height": "#row_height", "macros": {}, "mesh_setup": "1", "mesh_id": 523}, "constants": {}}]}}, "params": {"density_mode": "grid", "container_mode": "old_configurator"}}}, "mesh": {"523": {"setups": {"800": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10441, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10440}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10439}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10438}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10437}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10436}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10435}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10434, "division_ratio": "1x"}}], "parameters": {"x_range": [2300, 2590]}}, "400": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10415, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10414}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10413}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10412, "division_ratio": "1x"}}], "parameters": {"x_range": [1150, 2290]}}, "802": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 11026, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11025}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 11024, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11023}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11022}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 11021, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11020}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 11019, "division_ratio": "1x"}}], "parameters": {"x_range": [2600, 4500]}}, "900": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10450, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10449}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10448}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10447, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10446}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10445, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10444}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10443}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10442, "division_ratio": "1x"}}], "parameters": {"x_range": [2560, 2770]}}, "902": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 11035, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11034}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 11033, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11032}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11031}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11030}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 11029, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11028}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 11027, "division_ratio": "1x"}}], "parameters": {"x_range": [2780, 4250]}}, "200": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10408, "division_ratio": "1x"}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10407, "division_ratio": "1x"}}], "parameters": {"x_range": [700, 1150]}}, "1500": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10525, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10524}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10523}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10522}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10521, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10520}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10519}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10518}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10517}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10516}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10515, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10514}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10513}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10512}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10511, "division_ratio": "1x"}}], "parameters": {"x_range": [4250, 4500]}}, "300": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10411, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10410}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10409, "division_ratio": "1x"}}], "parameters": {"x_range": [870, 1720]}}, "600": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10426, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10425}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10424}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10423}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10422}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10421, "division_ratio": "1x"}}], "parameters": {"x_range": [1730, 2570]}}, "1200": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10483, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10482}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10481}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10480, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10479}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10478}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10477}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10476}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10475, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10474}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10473}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10472, "division_ratio": "1x"}}], "parameters": {"x_range": [3410, 4500]}}, "1000": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10460, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10459}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10458}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10457}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10456}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10455}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10454}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10453}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10452}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10451, "division_ratio": "1x"}}], "parameters": {"x_range": [2840, 4500]}}, "1100": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10471, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10470}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10469}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10468, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10467}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10466}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10465}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10464, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10463}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10462}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10461, "division_ratio": "1x"}}], "parameters": {"x_range": [3120, 4400]}}, "500": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10420, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10419}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10418}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10417}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10416, "division_ratio": "1x"}}], "parameters": {"x_range": [1430, 2560]}}, "502": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10997, "division_ratio": "1x"}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10996, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10995}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10994, "division_ratio": "1x"}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10993, "division_ratio": "1x"}}], "parameters": {"x_range": [2570, 2850]}}, "1400": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10510, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10509}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10508}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10507}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10506, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10505}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10504}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10503}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10502}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10501, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10500}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10499}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10498}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10497, "division_ratio": "1x"}}], "parameters": {"x_range": [3990, 4500]}}, "1300": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10496, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10495}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10494}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10493}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10492}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10491}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10490}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10489}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10488}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10487}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10486}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10485}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10484}}], "parameters": {"x_range": [3690, 4390]}}, "602": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 11005, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11004}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 11003, "division_ratio": "1x"}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 11002, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11001}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 11000, "division_ratio": "1x"}}], "parameters": {"x_range": [2580, 2790]}}, "700": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 10433, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10432}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10431}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10430}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10429}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 10428}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 10427, "division_ratio": "1x"}}], "parameters": {"x_range": [2000, 2570]}}, "702": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 11018, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11017}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 11016, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11015}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 11014, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11013}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 11012, "division_ratio": "1x"}}], "parameters": {"x_range": [2580, 3990]}}, "604": {"configs": [{"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 11011, "division_ratio": "1x"}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_b", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 11010, "division_ratio": "1x"}}, {"constants": {"#fill_b": "O", "#type": "#fill_a", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11009}}, {"constants": {"#fill_b": "O", "#type": "#fill_b", "#fill_a": "O"}, "parameters": {"comp_id": 826, "division_ratio": "1x", "config_id": 11008}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 2}, "parameters": {"comp_id": 826, "config_id": 11007, "division_ratio": "1x"}}, {"constants": {"#fill_a": "O", "#fill_b": "O", "#type": "#fill_a", "#back": 3}, "parameters": {"comp_id": 826, "config_id": 11006, "division_ratio": "1x"}}], "parameters": {"x_range": [2800, 3410]}}}, "parameters": {"size_y": [1]}}}, "component": {"826": {"setups": {"1": {"configs": [{"constants": {"#row_height": 200, "#type": "T", "#back": 0}, "subconfigs": [], "parameters": {"e_id": 286, "e_size_y": "#row_height", "fill__split_start": 500, "fill__split": true, "face__s_back": "#back", "type": "#type", "c_config_id": 13388}}]}}, "parameters": {"size_y": [1]}}}}, "serialized_at": "2019-01-14T18:21:01.196358", "superior_object_ids": [7]}