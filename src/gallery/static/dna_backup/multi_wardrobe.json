{"superior_object_type": "mesh", "superior_object_ids": [2321, 2339], "superior_object_line": 3, "serialized_at": "2021-03-01T11:17:00.503382", "serialization": {"mesh": {"2321": {"presets": {"666": {"id": 6236, "image": "/media/mesh_presets/7b502987-6866-400e-beeb-519bf7ffbba0.webp", "geom_id": null, "depth": 630, "height": 2400, "width": 2135, "density": 0, "distortion": 0, "plinth": false, "configurator_custom_params": {"lines": {}, "channels": {"1": {"order": -1, "cables": null, "local_x": null, "door_flip": null, "series_id": 3743, "distortion": null}, "2": {"order": -1, "cables": null, "local_x": null, "door_flip": null, "series_id": 3748, "distortion": null}, "3": {"order": -1, "cables": null, "local_x": "212", "door_flip": 2, "series_id": 3742, "distortion": null}}}, "owner": "katia", "rating": 0, "comment": "", "tags": ""}}, "setups": {"1": {"configs": [{"parameters": {"config_id": 78387, "comp_id": 902, "component": null, "table": 902, "series_pick": 3739, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}], "parameters": {"distortion_mode": "local_x", "plinth__height": "0", "distortion_available": false, "setup_id": 16016, "dim_x": [358, 1018]}}, "2": {"configs": [{"parameters": {"config_id": 77513, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77514, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}], "parameters": {"distortion_mode": "local_x", "plinth__height": "0", "distortion_available": false, "setup_id": 16017, "dim_x": [1019, 1236]}}, "3": {"configs": [{"parameters": {"config_id": 77515, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77516, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}], "parameters": {"distortion_mode": "local_x", "plinth__height": "0", "distortion_available": true, "setup_id": 16018, "dim_x": [1237, 1536]}}, "4": {"configs": [{"parameters": {"config_id": 77517, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77518, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}], "parameters": {"distortion_mode": "local_x", "plinth__height": "0", "distortion_available": false, "setup_id": 16019, "dim_x": [1537, 2036]}}, "5": {"configs": [{"parameters": {"config_id": 77519, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77520, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 3, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77521, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}], "parameters": {"distortion_mode": "local_x", "plinth__height": "0", "distortion_available": true, "setup_id": 16020, "dim_x": [2037, 2554]}}, "6": {"configs": [{"parameters": {"config_id": 77522, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77523, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77524, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}], "parameters": {"distortion_mode": "local_x", "plinth__height": "0", "distortion_available": false, "setup_id": 16021, "dim_x": [2555, 3054]}}, "7": {"configs": [{"parameters": {"config_id": 77525, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77526, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 4, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77527, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 3, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77528, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}], "parameters": {"distortion_mode": "local_x", "plinth__height": "0", "distortion_available": true, "setup_id": 16022, "dim_x": [3055, 3572]}}, "8": {"configs": [{"parameters": {"config_id": 77529, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77530, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77531, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77532, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}], "parameters": {"distortion_mode": "local_x", "plinth__height": "0", "distortion_available": false, "setup_id": 16023, "dim_x": [3573, 4072]}}, "9": {"configs": [{"parameters": {"config_id": 77533, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77534, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 4, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77535, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 5, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77536, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 3, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77537, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}], "parameters": {"distortion_mode": "local_x", "plinth__height": "0", "distortion_available": true, "setup_id": 16024, "dim_x": [4073, 4590]}}, "10": {"configs": [{"parameters": {"config_id": 77538, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77539, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77540, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77541, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77542, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}], "parameters": {"distortion_mode": "local_x", "plinth__height": "0", "distortion_available": false, "setup_id": 16025, "dim_x": [4591, 5090]}}, "11": {"configs": [{"parameters": {"config_id": 77543, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77544, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 4, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77546, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 6, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77545, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 5, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77547, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 3, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77548, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}], "parameters": {"distortion_mode": "local_x", "plinth__height": "0", "distortion_available": true, "setup_id": 16026, "dim_x": [5090, 5508]}}, "12": {"configs": [{"parameters": {"config_id": 77549, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77550, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77552, "comp_id": 902, "component": null, "table": 902, "series_pick": 3738, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77551, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77553, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}, {"parameters": {"config_id": 77554, "comp_id": 902, "component": null, "table": 902, "series_pick": 3741, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x", "plinth__height": "0"}, "constants": {"#object_type": 1, "pattern": 3}}], "parameters": {"distortion_mode": "local_x", "plinth__height": "0", "distortion_available": false, "setup_id": 16027, "dim_x": [5509, 6108]}}}, "parameters": {"distortion_mode": "local_x", "plinth__height": "0", "size_y": [300, 400, 500, 600, 700, 800], "size_x": [400, 6000], "object_type": 1}, "constants": {"#object_type": 1, "pattern": 3}}, "2339": {"presets": {"666": {"id": 6237, "image": "/media/mesh_presets/8b56e779-4786-420b-a9df-6e015430287f.webp", "geom_id": null, "depth": 630, "height": 2400, "width": 2060, "density": 0, "distortion": 0, "plinth": false, "configurator_custom_params": {"lines": {}, "channels": {"1": {"order": -1, "cables": null, "local_x": null, "door_flip": null, "series_id": 3740, "distortion": null}, "2": {"order": -1, "cables": null, "local_x": null, "door_flip": null, "series_id": 3748, "distortion": null}, "3": {"order": -1, "cables": null, "local_x": "212", "door_flip": 2, "series_id": 3742, "distortion": null}}}, "owner": "katia", "rating": 0, "comment": "", "tags": ""}}, "setups": {"1": {"configs": [{"parameters": {"config_id": 78342, "comp_id": 910, "component": null, "table": 910, "series_pick": 3790, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}], "parameters": {"distortion_mode": "local_x", "distortion_available": false, "setup_id": 16184, "dim_x": [358, 1018]}}, "2": {"configs": [{"parameters": {"config_id": 78343, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78344, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}], "parameters": {"distortion_mode": "local_x", "distortion_available": false, "setup_id": 16185, "dim_x": [1019, 1236]}}, "3": {"configs": [{"parameters": {"config_id": 78345, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78346, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}], "parameters": {"distortion_mode": "local_x", "distortion_available": true, "setup_id": 16186, "dim_x": [1237, 1536]}}, "4": {"configs": [{"parameters": {"config_id": 78347, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78348, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}], "parameters": {"distortion_mode": "local_x", "distortion_available": false, "setup_id": 16187, "dim_x": [1537, 2036]}}, "5": {"configs": [{"parameters": {"config_id": 78350, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78351, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 3, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78352, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}], "parameters": {"distortion_mode": "local_x", "distortion_available": true, "setup_id": 16188, "dim_x": [2037, 2554]}}, "6": {"configs": [{"parameters": {"config_id": 78353, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78354, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78355, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}], "parameters": {"distortion_mode": "local_x", "distortion_available": false, "setup_id": 16189, "dim_x": [2555, 3054]}}, "7": {"configs": [{"parameters": {"config_id": 78356, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78359, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 4, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78358, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 3, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78357, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}], "parameters": {"distortion_mode": "local_x", "distortion_available": true, "setup_id": 16190, "dim_x": [3055, 3572]}}, "8": {"configs": [{"parameters": {"config_id": 78361, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78362, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78363, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78364, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}], "parameters": {"distortion_mode": "local_x", "distortion_available": false, "setup_id": 16191, "dim_x": [3573, 4072]}}, "9": {"configs": [{"parameters": {"config_id": 78365, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78368, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 4, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78369, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 5, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78367, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 3, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78366, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}], "parameters": {"distortion_mode": "local_x", "distortion_available": true, "setup_id": 16192, "dim_x": [4073, 4590]}}, "10": {"configs": [{"parameters": {"config_id": 78370, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78371, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78372, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78373, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78374, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}], "parameters": {"distortion_mode": "local_x", "distortion_available": false, "setup_id": 16193, "dim_x": [4591, 5090]}}, "11": {"configs": [{"parameters": {"config_id": 78375, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 1, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78378, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 4, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78380, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 6, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78379, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 5, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78377, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 3, "table_dim_x": "320-840", "division_ratio": "2x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78376, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}], "parameters": {"distortion_mode": "local_x", "distortion_available": true, "setup_id": 16194, "dim_x": [5090, 5508]}}, "12": {"configs": [{"parameters": {"config_id": 78381, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78382, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78383, "comp_id": 910, "component": null, "table": 910, "series_pick": 3781, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78384, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78385, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}, {"parameters": {"config_id": 78386, "comp_id": 910, "component": null, "table": 910, "series_pick": 3782, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "local_x"}, "constants": {"#object_type": 0, "pattern": null}}], "parameters": {"distortion_mode": "local_x", "distortion_available": false, "setup_id": 16195, "dim_x": [5509, 6108]}}}, "parameters": {"distortion_mode": "local_x", "size_y": [2000, 2100, 2200, 2300, 2400, 2500, 2600], "size_x": [1200, 6000], "object_type": 0}, "constants": {"#object_type": 0, "pattern": 1}}}, "component_table": {"902": {"configs": {"slabs_dense": 3749, "rail_xl": 3744, "rail_2x": 3740, "rail_l": 3742, "rail_l_2drawers": 3743, "rail": 3738, "rail_2drawers": 3739, "rail_4drawers": 3741, "slabs": 3745, "slabs_2drawers": 3746, "slabs_4drawers": 3747, "slabs_6drawers": 3748}}, "910": {"configs": {"slabs_dense 2": 3787, "slabs_5drawers 2": 3789, "slabs_3drawers 2": 3788, "slabs 2": 3786, "rail_xl 2": 3785, "rail_m_2drawers 2": 3790, "rail_l 2": 3784, "rail_3drawers 2": 3782, "rail_2x 2": 3783, "rail 2": 3781}}}, "component_series": {"3738": {"setups": {"400": 44500, "300": 44470}, "parameters": {"size_y": [400, 300], "name": "rail", "series_id": 3738}}, "3781": {"setups": {"300": 44500}, "parameters": {"size_y": [300], "name": "rail 2", "series_id": 3781}}, "3739": {"setups": {"400": 44507, "300": 44473}, "parameters": {"size_y": [400, 300], "name": "rail_2drawers", "series_id": 3739}}, "3740": {"setups": {"400": 44502, "300": 44471}, "parameters": {"size_y": [400, 300], "name": "rail_2x", "series_id": 3740}}, "3783": {"setups": {"300": 44502}, "parameters": {"size_y": [300], "name": "rail_2x 2", "series_id": 3783}}, "3782": {"setups": {"300": 44507}, "parameters": {"size_y": [300], "name": "Rail_3drawers 2", "series_id": 3782}}, "3741": {"setups": {"400": 44507, "300": 44474}, "parameters": {"size_y": [400, 300], "name": "rail_4drawers", "series_id": 3741}}, "3742": {"setups": {"400": 44498, "300": 44469}, "parameters": {"size_y": [400, 300], "name": "rail_l", "series_id": 3742}}, "3784": {"setups": {"300": 44498}, "parameters": {"size_y": [300], "name": "rail_l 2", "series_id": 3784}}, "3743": {"setups": {"400": 44504, "300": 44472}, "parameters": {"size_y": [400, 300], "name": "rail_l_2drawers", "series_id": 3743}}, "3790": {"setups": {"300": 44504}, "parameters": {"size_y": [300], "name": "rail_m_2drawers 2", "series_id": 3790}}, "3744": {"setups": {"400": 44497, "300": 44468}, "parameters": {"size_y": [400, 300], "name": "rail_xl", "series_id": 3744}}, "3785": {"setups": {"300": 44497}, "parameters": {"size_y": [300], "name": "rail_xl 2", "series_id": 3785}}, "3745": {"setups": {"400": 44479, "300": 44463}, "parameters": {"size_y": [400, 300], "name": "slabs", "series_id": 3745}}, "3786": {"setups": {"300": 44479}, "parameters": {"size_y": [300], "name": "slabs 2", "series_id": 3786}}, "3746": {"setups": {"400": 44490, "300": 44465}, "parameters": {"size_y": [400, 300], "name": "slabs_2drawers", "series_id": 3746}}, "3788": {"setups": {"300": 44490}, "parameters": {"size_y": [300], "name": "slabs_3drawers 2", "series_id": 3788}}, "3747": {"setups": {"400": 44490, "300": 44466}, "parameters": {"size_y": [400, 300], "name": "slabs_4drawers", "series_id": 3747}}, "3789": {"setups": {"300": 44495}, "parameters": {"size_y": [300], "name": "slabs_5drawers 2", "series_id": 3789}}, "3748": {"setups": {"400": 44495, "300": 44467}, "parameters": {"size_y": [400, 300], "name": "slabs_6drawers", "series_id": 3748}}, "3749": {"setups": {"400": 44482, "300": 44464}, "parameters": {"size_y": [400, 300], "name": "slabs_dense", "series_id": 3749}}, "3787": {"setups": {"300": 44482}, "parameters": {"size_y": [300], "name": "slabs_dense 2", "series_id": 3787}}}, "component": {"44463": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118601, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": "400,800,1200,1546,1929", "e_size_y": 1929, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118599, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118598, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118597, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118596, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118595, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118594, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118600, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44464": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118609, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": "400,706,1012,1318,1624,1929", "e_size_y": 1929, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118607, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118606, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118605, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118604, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118603, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118602, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118608, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44465": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118619, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118618, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118617, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": "389,762", "e_size_y": 1129, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118615, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118614, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118613, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118612, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118611, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118610, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118616, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44466": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118629, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118628, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 800, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118627, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": 364, "e_size_y": 729, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118625, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118624, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118623, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118622, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118621, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118620, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118626, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44467": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118638, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 1200, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118637, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": 364, "e_size_y": 729, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118635, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118634, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118633, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118632, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118631, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118630, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118636, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44468": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118646, "type": "FB", "e_id": 286, "rod__pos_y": "-100", "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 1929, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118644, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118643, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118642, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118641, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118640, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118639, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118645, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44469": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118654, "type": "FB", "e_id": 286, "rod__pos_y": "-100", "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": 400, "e_size_y": 1929, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118652, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118651, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118650, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118649, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118648, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118647, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118653, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44470": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118662, "type": "FB", "e_id": 286, "rod__pos_y": "-100", "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": "400,800", "e_size_y": 1929, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118660, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118659, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118658, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118657, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118656, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118655, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118661, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44471": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118670, "type": "FB", "e_id": 286, "rod__pos_y": "-100,910", "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 1929, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118668, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118667, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118666, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118665, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118664, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118663, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118669, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44472": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118679, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118678, "type": "FB", "e_id": 286, "rod__pos_y": "-100", "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 1529, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118676, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118675, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118674, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118673, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118672, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118671, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118677, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44473": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118689, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118688, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118687, "type": "FB", "e_id": 286, "rod__pos_y": "-100", "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 1129, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118685, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118684, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118683, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118682, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118681, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118680, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118686, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44474": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118698, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 800, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118697, "type": "FB", "e_id": 286, "rod__pos_y": "-100", "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 1129, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118695, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118694, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118693, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118692, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118691, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118690, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118696, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44500": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118932, "type": "FB", "e_id": 286, "rod__pos_y": "-100", "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": "400,800", "e_size_y": 1929, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118928, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118925, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118923, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118921, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118919, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118918, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118930, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44502": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118948, "type": "FB", "e_id": 286, "rod__pos_y": "-100,910", "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 1929, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118943, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118941, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118939, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118937, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118935, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118934, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118945, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44507": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118986, "type": "T", "e_id": 286, "drawer_exterior": true, "drawer_height": "<PERSON>,<PERSON>", "drawer_autofill": true, "e_size_y": 573, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118984, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 227, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118982, "type": "FB", "e_id": 286, "rod__pos_y": "-100", "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 1129, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118978, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118976, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118974, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118972, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118970, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118968, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118980, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44498": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118916, "type": "FB", "e_id": 286, "rod__pos_y": "-100", "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": 400, "e_size_y": 1929, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118912, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118910, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118907, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118905, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118903, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118902, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118914, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44504": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 119014, "type": "T", "e_id": 286, "drawer_exterior": true, "drawer_height": "<PERSON>,<PERSON>", "drawer_autofill": true, "e_size_y": 573, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118964, "type": "FB", "e_id": 286, "rod__pos_y": "-100", "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 1356, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118959, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118957, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118955, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118952, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118951, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118950, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118961, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44497": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118900, "type": "FB", "e_id": 286, "rod__pos_y": "-100", "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 1929, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118896, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118894, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118893, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118891, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118889, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118887, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118898, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44479": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118744, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": "400,800,1200,1546,1929", "e_size_y": 1929, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118740, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118738, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118737, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118735, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118734, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118732, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118741, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44490": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 119019, "type": "T", "e_id": 286, "drawer_exterior": true, "drawer_height": "<PERSON>,<PERSON>", "drawer_autofill": true, "e_size_y": 573, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118852, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 227, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118848, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": "389,762", "e_size_y": 1129, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118839, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118835, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118832, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118829, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118824, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118822, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118842, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44495": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 119021, "type": "T", "e_id": 286, "drawer_exterior": true, "drawer_height": "<PERSON>,<PERSON>", "drawer_autofill": true, "e_size_y": 573, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118884, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 627, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [], "parameters": {"c_config_id": 118883, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": 364, "e_size_y": 729, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118879, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118876, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118872, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118870, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118867, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118863, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118881, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44482": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118774, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": "400,706,1012,1318,1624,1929", "e_size_y": 1929, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"subconfigs": [{"parameters": {"c_config_id": 118765, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "738-1000", "insert__dom_y": 369, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118760, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1000-1200", "insert__dom_y": 469, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118757, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1200-1300", "insert__dom_y": 569, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118754, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1300-1400", "insert__dom_y": 669, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118751, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1400-1500", "insert__dom_y": "369,769", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}, {"parameters": {"c_config_id": 118749, "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "optional_dim_y": "1500-1607", "insert__dom_y": "369,869", "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"c_config_id": 118770, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#pawlacz", "face__s_bottom": 0, "dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#pawlacz": 369}}], "parameters": {"dim_x": "358-1018", "exterior_doors_split_y_start": "0, 2316", "exterior_doors_split_y_value": "0, 1929", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}}}, "configurator_data": {"table": {"902": {"400": [{"series_id": 3748, "component_id": 44495, "series_name": "slabs_6drawers", "series_groups": "od_7,all_4,order_11,eleven_11", "order": 0, "inconsequent": false}, {"series_id": 3747, "component_id": 44490, "series_name": "slabs_4drawers", "series_groups": "order_10,eleven_10", "order": 0, "inconsequent": false}, {"series_id": 3746, "component_id": 44490, "series_name": "slabs_2drawers", "series_groups": "all_4,od_3,eleven_9,order_9", "order": 0, "inconsequent": false}, {"series_id": 3745, "component_id": 44479, "series_name": "slabs", "series_groups": "all_4,od_1,eleven_8,order_8", "order": 0, "inconsequent": false}, {"series_id": 3741, "component_id": 44507, "series_name": "rail_4drawers", "series_groups": "wia_7,wi_9,eleven_7,order_7", "order": 0, "inconsequent": false}, {"series_id": 3739, "component_id": 44507, "series_name": "rail_2drawers", "series_groups": "all_3,order_6,eleven_6", "order": 0, "inconsequent": false}, {"series_id": 3738, "component_id": 44500, "series_name": "rail", "series_groups": "wi_7,all_3,eleven_5,order_5", "order": 0, "inconsequent": false}, {"series_id": 3743, "component_id": 44504, "series_name": "rail_l_2drawers", "series_groups": "all_2,wi_4,wia_4,eleven_3,order_4", "order": 0, "inconsequent": false}, {"series_id": 3742, "component_id": 44498, "series_name": "rail_l", "series_groups": "all_2,eleven_3,wi_3,wia_3,order_3", "order": 0, "inconsequent": false}, {"series_id": 3740, "component_id": 44502, "series_name": "rail_2x", "series_groups": "all_1,eleven_2,wi_2,wia_2,order_2", "order": 0, "inconsequent": false}, {"series_id": 3744, "component_id": 44497, "series_name": "rail_xl", "series_groups": "eleven_1,order_1", "order": 0, "inconsequent": false}, {"series_id": 3749, "component_id": 44482, "series_name": "slabs_dense", "series_groups": "", "order": 0, "inconsequent": false}], "300": [{"series_id": 3748, "component_id": 44467, "series_name": "slabs_6drawers", "series_groups": "od_7,all_4,order_11,eleven_11", "order": 0, "inconsequent": false}, {"series_id": 3747, "component_id": 44466, "series_name": "slabs_4drawers", "series_groups": "order_10,eleven_10", "order": 0, "inconsequent": false}, {"series_id": 3746, "component_id": 44465, "series_name": "slabs_2drawers", "series_groups": "all_4,od_3,eleven_9,order_9", "order": 0, "inconsequent": false}, {"series_id": 3745, "component_id": 44463, "series_name": "slabs", "series_groups": "all_4,od_1,eleven_8,order_8", "order": 0, "inconsequent": false}, {"series_id": 3741, "component_id": 44474, "series_name": "rail_4drawers", "series_groups": "wia_7,wi_9,eleven_7,order_7", "order": 0, "inconsequent": false}, {"series_id": 3739, "component_id": 44473, "series_name": "rail_2drawers", "series_groups": "all_3,order_6,eleven_6", "order": 0, "inconsequent": false}, {"series_id": 3738, "component_id": 44470, "series_name": "rail", "series_groups": "wi_7,all_3,eleven_5,order_5", "order": 0, "inconsequent": false}, {"series_id": 3743, "component_id": 44472, "series_name": "rail_l_2drawers", "series_groups": "all_2,wi_4,wia_4,eleven_3,order_4", "order": 0, "inconsequent": false}, {"series_id": 3742, "component_id": 44469, "series_name": "rail_l", "series_groups": "all_2,eleven_3,wi_3,wia_3,order_3", "order": 0, "inconsequent": false}, {"series_id": 3740, "component_id": 44471, "series_name": "rail_2x", "series_groups": "all_1,eleven_2,wi_2,wia_2,order_2", "order": 0, "inconsequent": false}, {"series_id": 3744, "component_id": 44468, "series_name": "rail_xl", "series_groups": "eleven_1,order_1", "order": 0, "inconsequent": false}, {"series_id": 3749, "component_id": 44464, "series_name": "slabs_dense", "series_groups": "", "order": 0, "inconsequent": false}]}, "910": {"300": [{"series_id": 3787, "component_id": 44482, "series_name": "slabs_dense 2", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3789, "component_id": 44495, "series_name": "slabs_5drawers 2", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3788, "component_id": 44490, "series_name": "slabs_3drawers 2", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3786, "component_id": 44479, "series_name": "slabs 2", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3785, "component_id": 44497, "series_name": "rail_xl 2", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3790, "component_id": 44504, "series_name": "rail_m_2drawers 2", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3784, "component_id": 44498, "series_name": "rail_l 2", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3782, "component_id": 44507, "series_name": "Rail_3drawers 2", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3783, "component_id": 44502, "series_name": "rail_2x 2", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3781, "component_id": 44500, "series_name": "rail 2", "series_groups": "", "order": 0, "inconsequent": false}]}}, "component": {"44495": {"id": 44495, "name": "slabs_6drawers - SLABS_5DRAWERS"}, "44467": {"id": 44467, "name": "slabs_6drawers - oowwwwww 1"}, "44490": {"id": 44490, "name": "slabs_4drawers - SLABS_3DRAWERS"}, "44466": {"id": 44466, "name": "slabs_4drawers - oowwwwo1"}, "44465": {"id": 44465, "name": "slabs_2drawers - ooowwo 1"}, "44479": {"id": 44479, "name": "slabs - SLABS"}, "44463": {"id": 44463, "name": "slabs - ooooo1"}, "44507": {"id": 44507, "name": "rail_4drawers - RAIL_3DRAWERS"}, "44474": {"id": 44474, "name": "rail_4drawers - WIWWwW1"}, "44473": {"id": 44473, "name": "rail_2drawers - wiwwo1"}, "44500": {"id": 44500, "name": "rail - RAIL"}, "44470": {"id": 44470, "name": "rail - wioo1"}, "44504": {"id": 44504, "name": "rail_l_2drawers - RAIL_M_2DRAWERS"}, "44472": {"id": 44472, "name": "rail_l_2drawers - wiww1"}, "44498": {"id": 44498, "name": "rail_l - RAIL_L"}, "44469": {"id": 44469, "name": "rail_l - wio1"}, "44502": {"id": 44502, "name": "rail_2x - RAIL_2X"}, "44471": {"id": 44471, "name": "rail_2x - wiwi"}, "44497": {"id": 44497, "name": "rail_xl - RAIL_XL"}, "44468": {"id": 44468, "name": "rail_xl - wi"}, "44482": {"id": 44482, "name": "slabs_dense - SLABS_DENSE"}, "44464": {"id": 44464, "name": "slabs_dense - OOOOOO1"}}}, "superior_object_collection": "<PERSON><PERSON>"}