import datetime
import math

from typing import Final
from unittest import mock

from django.contrib.auth import get_user_model

import pytest

from freezegun import freeze_time
from pytest_cases import parametrize_with_cases

from gallery.constants import SEATING_DEPTH_OFFSET
from gallery.enums import (
    ConfiguratorTypeEnum,
    FurnitureCategory,
    FurnitureStatusEnum,
    SottySeatingDepthCases,
)
from gallery.exceptions import (
    BadCategoryConfigurationError,
    BagConnectionError,
)
from gallery.services.bag import fetch_custom_dna_from_bag
from gallery.services.category_configuration_validation import (
    validate_category_configuration,
)
from gallery.services.copy_furniture import copy_furniture
from gallery.services.sotty_seating_depth_handler import SottySeatingDepthHandler
from gallery.services.wishlist_cohort_stats import (
    _format_with_spaces,
    _send_s4l_summary_to_slack,
    s4l_cohort_counter,
    send_s4l_summary_to_slack,
)
from orders.enums import OrderStatus

User = get_user_model()


JETTY_GEOMETRY_FIELDS: Final[tuple[str, ...]] = (
    'doors',
    'backs',
    'drawers',
    'verticals',
    'horizontals',
    'supports',
    'rows',
    'legs',
    'inserts',
    'plinth',
    'long_legs',
    'cable_management',
    'desk_beams',
    'row_styles',
    'property1',
)
SOTTY_GEOMETRY_FIELDS: Final[tuple[str, ...]] = (
    'materials',
    'armrests',
    'chaise_longues',
    'corners',
    'footrests',
    'seaters',
    'covers_only',
)
WATTY_GEOMETRY_FIELDS: Final[tuple[str, ...]] = (
    'doors',
    'backs',
    'drawers',
    'walls',
    'slabs',
    'frame',
    'bars',
    'hinges',
    'masking_bars',
    'lighting',
    'cable_management',
    'legs',
)


@pytest.mark.django_db
@pytest.mark.parametrize(
    ('factory', 'status', 'geometry_fields'),
    [
        ('jetty_factory', FurnitureStatusEnum.DRAFT, JETTY_GEOMETRY_FIELDS),
        ('sotty_factory', FurnitureStatusEnum.SAVED, SOTTY_GEOMETRY_FIELDS),
        ('watty_factory', FurnitureStatusEnum.ORDERED, WATTY_GEOMETRY_FIELDS),
    ],
)
def test_copy_furniture(user, request, factory, status, geometry_fields):
    old_furniture = request.getfixturevalue(factory)()

    new_furniture = copy_furniture(
        old_furniture=old_furniture, owner=user, new_furniture_status=status
    )

    assert new_furniture.pk != old_furniture.pk
    assert new_furniture.owner == user
    assert new_furniture.furniture_status == status
    assert new_furniture.preset is False
    assert new_furniture.base_preset == old_furniture.id
    assert new_furniture.components == old_furniture.components
    assert new_furniture.configurator_params == old_furniture.configurator_params
    assert new_furniture.shelf_type == old_furniture.shelf_type
    assert new_furniture.shelf_category == old_furniture.shelf_category
    assert new_furniture.material == old_furniture.material
    assert new_furniture.width == old_furniture.width
    assert new_furniture.depth == old_furniture.depth
    assert new_furniture.height == old_furniture.height
    for geometry_field in geometry_fields:
        assert getattr(new_furniture, geometry_field) == getattr(
            old_furniture, geometry_field
        )


@pytest.mark.django_db
class TestFetchCustomDna:
    def test_sends_request_to_default_bag_if_not_overridden(
        self,
        settings,
        mocker,
    ):
        settings.BAG_URL = 'http://some-bag.url'
        settings.BAG_TOKEN = 'token'
        mock = mocker.patch('gallery.services.bag.requests.get')
        mock.return_value.json.return_value = {'dna': {}}
        fetch_custom_dna_from_bag(
            bag_object_id=1,
        )
        mock.assert_called_once_with(
            'http://some-bag.url/dna/multi/1',
            headers={
                'Content-Type': 'application/json',
                'Authorization': f'Token {settings.BAG_TOKEN}',
            },
        )

    def test_bag_url_can_be_overridden(self, mocker, settings):
        settings.BAG_TOKEN = 'token'
        mock = mocker.patch('gallery.services.bag.requests.get')
        mock.return_value.json.return_value = {'dna': {}}
        fetch_custom_dna_from_bag(
            bag_object_id=1,
            bag_url='http://bag.bag',
        )
        mock.assert_called_once_with(
            'http://bag.bag/dna/multi/1',
            headers={
                'Content-Type': 'application/json',
                'Authorization': f'Token {settings.BAG_TOKEN}',
            },
        )

    def test_raises_bag_connection_error_if_bag_fails(
        self,
        settings,
        requests_mock,
    ):
        settings.BAG_URL = 'http://some-bag.url'
        requests_mock.get(
            'http://some-bag.url/dna/multi/1',
            status_code=400,
        )
        with pytest.raises(BagConnectionError):
            fetch_custom_dna_from_bag(
                bag_object_id=1,
            )


class TestValidateCategoryConfiguration:
    @pytest.mark.parametrize(
        'furniture_category, configurator_type, raise_error',
        [
            (FurnitureCategory.BEDSIDE_TABLE, ConfiguratorTypeEnum.COLUMN, False),
            (FurnitureCategory.BEDSIDE_TABLE, ConfiguratorTypeEnum.ROW, True),
            (FurnitureCategory.SHOERACK, ConfiguratorTypeEnum.COLUMN, False),
            (FurnitureCategory.SHOERACK, ConfiguratorTypeEnum.ROW, False),
            (FurnitureCategory.SHOERACK, ConfiguratorTypeEnum.MIXED_ROW_COLUMN, True),
        ],
    )
    def test_validate_category_configuration_jetty(
        self,
        jetty,
        furniture_category,
        configurator_type,
        raise_error,
    ):
        jetty.shelf_category = furniture_category
        jetty.configurator_type = configurator_type
        if raise_error:
            with pytest.raises(BadCategoryConfigurationError):
                validate_category_configuration(jetty)
        else:
            validate_category_configuration(jetty)

    @pytest.mark.parametrize(
        'configurator_type, raise_error',
        [
            (ConfiguratorTypeEnum.COLUMN, True),
            (ConfiguratorTypeEnum.ROW, True),
            (ConfiguratorTypeEnum.MIXED_ROW_COLUMN, False),
        ],
    )
    def test_validate_category_configuration_watty(
        self,
        watty,
        configurator_type,
        raise_error,
    ):
        watty.configurator_type = configurator_type
        if raise_error:
            with pytest.raises(BadCategoryConfigurationError):
                validate_category_configuration(watty)
        else:
            validate_category_configuration(watty)


class TestCategoryAssignerCases:
    def case_cover_category(self, sotty_factory):
        sotty = sotty_factory(covers_only=True)
        return sotty, FurnitureCategory.COVER

    @pytest.mark.parametrize('module', ('chaise_longues', 'corners', 'seaters'))
    def case_single_module_category(self, sotty_factory, module):
        kwargs = {
            'chaise_longues': [],
            'corners': [],
            'footrests': [],
            'seaters': [],
        } | {module: [{'desc': 'simple sotty with single module'}]}
        sotty = sotty_factory(**kwargs)
        return sotty, FurnitureCategory.ARMCHAIR

    def case_corner_category(self, sotty_factory):
        sotty = sotty_factory(corner=True)
        return sotty, FurnitureCategory.CORNER

    def case_chaise_longue_category(self, sotty_factory):
        sotty = sotty_factory()
        return sotty, FurnitureCategory.CHAISE_LONGUE

    @pytest.mark.parametrize(
        ('single_seater_width', 'total_seaters'), [(1_000, 5), (773.5, 3)]
    )
    def case_four_plus_seater_category(
        self, sotty_factory, single_seater_width, total_seaters
    ):
        sotty = sotty_factory(
            chaise_longues=[],
            seaters=[{'width': single_seater_width} for _ in range(total_seaters)],
        )
        return sotty, FurnitureCategory.FOUR_PLUS_SEATER

    @pytest.mark.parametrize(
        ('single_seater_width', 'total_seaters'), [(870, 2), (773, 3)]
    )
    def case_three_seater_category(
        self, sotty_factory, single_seater_width, total_seaters
    ):
        sotty = sotty_factory(
            chaise_longues=[],
            seaters=[{'width': single_seater_width} for _ in range(total_seaters)],
        )
        return sotty, FurnitureCategory.THREE_SEATER

    @pytest.mark.parametrize(
        ('single_seater_width', 'total_seaters'), [(580, 2), (869.5, 2)]
    )
    def case_two_seater_category(
        self, sotty_factory, single_seater_width, total_seaters
    ):
        sotty = sotty_factory(
            chaise_longues=[],
            seaters=[{'width': single_seater_width} for _ in range(total_seaters)],
        )
        return sotty, FurnitureCategory.TWO_SEATER

    def case_footrest_and_modules_category(self, sotty_factory):
        sotty = sotty_factory(corners=[], chaise_longues=[], seaters=[{'width': 1_159}])
        return sotty, FurnitureCategory.FOOTREST_AND_MODULES


@pytest.mark.django_db
class TestCategoryAssigner:
    @parametrize_with_cases(
        ('sotty', 'expected_category'),
        cases=TestCategoryAssignerCases,
    )
    def test_determine_sofa_category(self, sotty, expected_category):
        assert FurnitureCategory(sotty.shelf_category) == expected_category


@pytest.mark.django_db
class TestWishlistCohortStats:
    def test_format_with_spaces(self):
        assert _format_with_spaces(1000) == '1 000'
        assert _format_with_spaces(1000000) == '1 000 000'
        assert _format_with_spaces(0) == '0'
        assert _format_with_spaces(123) == '123'

    @mock.patch('gallery.services.wishlist_cohort_stats.requests.post')
    def test_send_s4l_summary_to_slack(self, mock_post, settings):
        # Test when webhook is not set
        settings.SLACK_WEBHOOK = None
        settings.IS_PRODUCTION = True
        _send_s4l_summary_to_slack('2023-01-01 - 2023-03-01', 100, 50)
        mock_post.assert_not_called()

        # Test when not in production
        settings.SLACK_WEBHOOK = 'https://hooks.slack.com/services/xxx/yyy/zzz'
        settings.IS_PRODUCTION = False
        _send_s4l_summary_to_slack('2023-01-01 - 2023-03-01', 100, 50)
        mock_post.assert_not_called()

        # Test successful call
        settings.IS_PRODUCTION = True
        _send_s4l_summary_to_slack('2023-01-01 - 2023-03-01', 100, 50)
        mock_post.assert_called_once_with(
            settings.SLACK_WEBHOOK,
            json={
                'text': 'In date range 2023-01-01 - 2023-03-01 100 users did S4L\nand 50 of them converted to orders.',
                'channel': 's4l-stats',
                'username': 'Save4Later Stats',
                'icon_emoji': ':hand_with_index_finger_and_thumb_crossed:',
            },
            timeout=10,
        )

    def test_getting_saved_furniture_is_right(
        self, jetty_factory, watty_factory, sotty_factory
    ):
        with freeze_time('2023-03-01'):
            jetty_factory(furniture_status=FurnitureStatusEnum.SAVED)
            jetty_factory(furniture_status=FurnitureStatusEnum.DRAFT)
            watty_factory(furniture_status=FurnitureStatusEnum.SAVED)
            sotty_factory(furniture_status=FurnitureStatusEnum.SAVED)
        assert s4l_cohort_counter(
            datetime.date(2023, 2, 2),
            datetime.date(2023, 4, 2),
        ) == (3, 0)

    def test_getting_orders_is_right(
        self,
        jetty_factory,
        order_factory,
    ):
        with freeze_time('2023-03-01'):
            j = jetty_factory(furniture_status=FurnitureStatusEnum.SAVED)
            order_factory(owner=j.owner, status=OrderStatus.DELIVERED)
            order_factory(status=OrderStatus.DELIVERED)
        assert s4l_cohort_counter(
            datetime.date(2023, 2, 2),
            datetime.date(2023, 4, 2),
        ) == (1, 1)

    @mock.patch('gallery.services.wishlist_cohort_stats._send_s4l_summary_to_slack')
    @mock.patch('gallery.services.wishlist_cohort_stats.s4l_cohort_counter')
    @freeze_time('2023-04-05')  # Wednesday
    def test_send_s4l_summary_to_slack_dates(self, mock_counter, mock_send):
        # Setup
        mock_counter.return_value = (100, 50)

        # Call the function
        send_s4l_summary_to_slack()

        # Check that s4l_cohort_counter was called with correct dates
        # April 5, 2023 is a Wednesday, so last Sunday was April 2, 2023
        # Two months before that is February 2, 2023
        mock_counter.assert_called_once_with(
            datetime.date(2023, 2, 2),
            datetime.date(2023, 4, 2),
        )

        # Check that _send_s4l_summary_to_slack was called with correct parameters
        mock_send.assert_called_once_with(
            '2023-02-02 - 2023-04-02',
            100,
            50,
        )


class SeatingDepthCases:
    def case_single_corner(self):
        corner_depth = 1_000  # in mm
        return (
            dict(
                corners=[{'depth': corner_depth}],
                chaise_longues=[],
                seaters=[],
                armrests=[],
                footrests=[],
            ),
            SottySeatingDepthCases.CORNER,
            math.ceil(corner_depth - SEATING_DEPTH_OFFSET),
        )

    def case_single_footrests(self):
        footrest_depth = 900  # in mm
        return (
            dict(
                corners=[],
                chaise_longues=[],
                seaters=[],
                armrests=[],
                footrests=[{'depth': footrest_depth}],
            ),
            SottySeatingDepthCases.SINGLE_FOOTRESTS,
            math.ceil(footrest_depth - SEATING_DEPTH_OFFSET),
        )

    def case_extended_chaise_longue(self):
        footrest_depth = 700  # in mm
        seater_depth = 800  # in mm
        return (
            dict(
                footrests=[{'depth': footrest_depth}],
                seaters=[{'depth': seater_depth}],
                single_extended_chaise_longue=True,
            ),
            SottySeatingDepthCases.SINGLE_EXTENDED_CHAISE_LONGUE,
            math.ceil(footrest_depth + seater_depth - SEATING_DEPTH_OFFSET),
        )

    def case_single_chaise_longue(self):
        chaise_longue_depth = 1_100  # in mm
        return (
            dict(
                chaise_longues=[{'depth': chaise_longue_depth}],
                corners=[],
                seaters=[],
                armrests=[],
                footrests=[],
            ),
            SottySeatingDepthCases.CHAISE_LONGUE,
            math.ceil(chaise_longue_depth - SEATING_DEPTH_OFFSET),
        )

    def case_seaters_multiple_depths(self):
        seater_depth = 1_050  # in mm
        return (
            dict(seaters=[{'depth': seater_depth}]),
            SottySeatingDepthCases.SEATERS,
            math.ceil(seater_depth - SEATING_DEPTH_OFFSET),
        )

    def case_no_components(self):
        return (
            dict(
                corners=[],
                chaise_longues=[],
                seaters=[],
                armrests=[],
                footrests=[],
            ),
            SottySeatingDepthCases.SEATERS,
            None,
        )


class TestSeatingDepthHandler:
    @parametrize_with_cases(
        'sotty_data, expected_case, expected_depth', cases=SeatingDepthCases
    )
    def test_get_seating_depth(
        self, sotty_data, expected_case, expected_depth, sotty_factory
    ):
        expected_depth = math.ceil(expected_depth / 10.0) if expected_depth else None
        sotty = sotty_factory(**sotty_data)
        handler = SottySeatingDepthHandler(sotty)
        assert handler._get_sotty_case() == expected_case
        assert handler.get_seating_depth() == expected_depth
