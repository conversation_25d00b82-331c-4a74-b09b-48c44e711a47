from django.utils import translation

import pytest

from pytest_cases import (
    case,
    parametrize_with_cases,
)

from custom.enums import LanguageEnum
from custom.enums.colors import Sofa01Color
from gallery.enums import FurnitureCategory
from gallery.models import Sotty
from gallery.slugs import (
    generate_title_based_on_filter_data,
    get_slug_for_furniture,
    get_title_for_grid,
)


@pytest.mark.parametrize(
    'query_param,expected_result',
    [
        (
            {
                'category': 'wardrobe',
                'regionName': 'germany',
                'colors': 'white',
                'width': 'lt150',
            },
            'Wardrobes in White',
        ),
        (
            {
                'category': 'wardrobe',
                'regionName': 'germany',
                'colors': 'white,green',
            },
            'Wardrobes',
        ),
        (
            {
                'category': 'sideboard',
                'regionName': 'germany',
            },
            'Sideboards',
        ),
        (
            {
                'category': 'wardrobe',
                'regionName': 'germany',
                'colors': 'pink',
                'materials': 'plywood',
            },
            'Wardrobes in Pink - Plywood',
        ),
        (
            {
                'category': 'wardrobe',
                'regionName': 'germany',
                'colors': 'pink',
                'materials': 'plywood',
                'additional': 'sale',
            },
            'Wardrobes in Pink - Plywood - Sale',
        ),
        (
            {
                'category': 'wardrobe',
                'regionName': 'germany',
                'features': 'doors,cableManagement',
                'colors': 'pink',
                'materials': 'plywood',
                'additional': 'sale',
            },
            'Wardrobes in Pink with doors and cable management - Plywood - Sale',
        ),
        (
            {
                'category': 'wardrobe',
                'regionName': 'germany',
                'features': 'externalDrawers,internalDrawers',
                'colors': 'pink',
                'materials': 'plywood',
                'additional': 'sale',
            },
            'Wardrobes in Pink with internal and external drawers - Plywood - Sale',
        ),
    ],
)
@pytest.mark.django_db
def test_generate_title_for_plp_filter(query_param, expected_result):
    with translation.override(LanguageEnum.EN):
        result = generate_title_based_on_filter_data(query_param)
    assert expected_result == result


class TestSottySlugParserCases:
    def sotty_data_without_save(self) -> Sotty:
        return Sotty(
            materials=[Sofa01Color.REWOOL2_BROWN],
            height=123,
            width=123,
            depth=123,
        )

    @case(tags=['test_making_names'])
    @pytest.mark.parametrize(
        ('language', 'name', 'seo_slug'),
        [
            (LanguageEnum.EN, '', ''),
            (LanguageEnum.DE, '', ''),
        ],
    )
    def case_chaise_longue_with_features(self, language, name, seo_slug):
        sotty = self.sotty_data_without_save()
        sotty.shelf_category = FurnitureCategory.CHAISE_LONGUE
        sotty.chaise_longues = [{'geometry': 123}]
        sotty.footrests = [{'geometry': 123}]
        return sotty, language, name, seo_slug

    @case(tags=['test_making_names'])
    @pytest.mark.parametrize(
        ('language', 'name', 'seo_slug'),
        [
            (LanguageEnum.EN, '', ''),
            (LanguageEnum.DE, '', ''),
        ],
    )
    def case_corner_with_features(self, language, name, seo_slug):
        sotty = self.sotty_data_without_save()
        sotty.shelf_category = FurnitureCategory.CORNER
        sotty.chaise_longues = [{'geometry': 123}]
        sotty.footrests = [{'geometry': 123}]
        return sotty, language, name, seo_slug

    @case(tags=['test_making_names'])
    @pytest.mark.parametrize(
        ('sofa_category', 'name', 'seo_slug'),
        [
            (FurnitureCategory.TWO_SEATER, '', ''),
            (FurnitureCategory.THREE_SEATER, '', ''),
            (FurnitureCategory.FOUR_PLUS_SEATER, '', ''),
            (FurnitureCategory.CORNER, '', ''),
            (FurnitureCategory.CHAISE_LONGUE, '', ''),
            (FurnitureCategory.ARMCHAIR, '', ''),
            (FurnitureCategory.FOOTREST_AND_MODULES, '', ''),
            (FurnitureCategory.COVER, '', ''),
        ],
    )
    def case_different_sofa_category(self, sofa_category, name, seo_slug):
        sotty = self.sotty_data_without_save()
        sotty.shelf_category = sofa_category
        return sotty, LanguageEnum.EN, name, seo_slug

    @case(tags=['test_making_names'])
    @pytest.mark.parametrize(
        ('material', 'name', 'seo_slug'),
        [
            (Sofa01Color.REWOOL2_BROWN, '', ''),
            (Sofa01Color.REWOOL2_OLIVE_GREEN, '', ''),
            (Sofa01Color.REWOOL2_LIGHT_GRAY, '', ''),
            (Sofa01Color.REWOOL2_BUTTER_YELLOW, '', ''),
            (Sofa01Color.REWOOL2_SHADOW_PINK, '', ''),
            (Sofa01Color.REWOOL2_GREEN, '', ''),
            (Sofa01Color.REWOOL2_BABY_BLUE, '', ''),
            (Sofa01Color.CORDUROY_ECRU, '', ''),
            (Sofa01Color.CORDUROY_ROCK, '', ''),
            (Sofa01Color.CORDUROY_DARK_BROWN, '', ''),
            (Sofa01Color.CORDUROY_STEEL, '', ''),
            (Sofa01Color.CORDUROY_TOBACCO, '', ''),
            (Sofa01Color.CORDUROY_PINK, '', ''),
            (Sofa01Color.CORDUROY_CAMOUFLAGE, '', ''),
            (Sofa01Color.CORDUROY_BLUE_KLEIN, '', ''),
        ],
    )
    def case_different_color(self, material, name, seo_slug):
        sotty = self.sotty_data_without_save()
        sotty.materials = [material]
        return sotty, LanguageEnum.EN, name, seo_slug


@pytest.mark.skip(reason='Waiting for translations')
@pytest.mark.django_db
class TestSottySlugParser:
    @parametrize_with_cases(
        ('sotty', 'language', 'expected_title', 'expected_seo_slug'),
        cases=TestSottySlugParserCases,
        has_tag='test_making_names',
    )
    def test_making_names(self, sotty, language, expected_title, expected_seo_slug):
        title_for_grid = get_title_for_grid(furniture=sotty, language=language)
        assert title_for_grid == expected_title

        slug_for_furniture = get_slug_for_furniture(furniture=sotty, language=language)
        assert slug_for_furniture == expected_seo_slug
