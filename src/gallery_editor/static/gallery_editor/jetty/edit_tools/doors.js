import { thickness } from "../../constants.js";
import { minDoorWidth} from "../constants.js";


const restoreRemovedMatchingDoors = (compartment, doorSplit) => {
  doorSplit = doorSplit === null ? compartment.x1 : doorSplit;
  let matchingDoors = compartment.doors.filter(
    door => (
      (door.x1 === doorSplit && door.x2 === compartment.x2) ||
      (door.x2 === doorSplit && door.x1 === compartment.x1)
    )
  );
  if (matchingDoors.length) {
    matchingDoors.forEach(door => {
      door.removed = false;
    });
    compartment.doors.filter(door => (
      matchingDoors.indexOf(door) === -1
    )).forEach(door => {
      door.removed = true;
    });
  }
  return !!matchingDoors.length;
};

export const addDoorToCompartment = (compartment) => {
  if (restoreRemovedMatchingDoors(compartment, null)) return;
  let newDoors = {
    x1: compartment.x1,
    x2: compartment.x2,
    y1: compartment.y1,
    y2: compartment.y2,
    z1: compartment.z1,
    z2: compartment.z2,
    flip: 0,
    handle: 0,
    direction: 2,
  }
  jetty.doors.push(newDoors);
  compartment.doors.push(newDoors);
};

const findDoorSplitAxis = (compartment, jetty) => {
  let splitX = (compartment.x1 + compartment.x2) / 2;
  // find neighbouring vertical with right edge closest to compartment's middle
  let minDistance;
  let closestVertical = jetty.verticals.filter(vertical => (
    vertical.y1 === compartment.y2 + thickness ||
    vertical.y2 === compartment.y1 - thickness
  )).reduce(
    (closestVertical, vertical) => {
      let distance = Math.abs(
        (vertical.x1 + vertical.x2 + thickness) / 2 - splitX
      )
      if (minDistance === undefined || distance < minDistance) {
        minDistance = distance;
        return vertical;
      }
      return closestVertical;
    },
    null, // in case of a single row filter will return an empty array
  );
  // check if after splitting the door at the right edge of the nearest vertical
  // resulting doors leaf widths are over the minimum
  let splitXAdjustment = (compartment.x2 - compartment.x1) / 2 - minDoorWidth
  if (minDistance <= splitXAdjustment)
    splitX = (closestVertical.x1 + closestVertical.x2 + thickness) / 2
  return splitX;
};

export const addDoubleDoorToCompartment = (compartment, jetty) => {
  let splitX = findDoorSplitAxis(compartment, jetty);
  if (restoreRemovedMatchingDoors(compartment, splitX)) return;
  let newDoors = []
  newDoors.push({
    x1: compartment.x1,
    x2: splitX,
    y1: compartment.y1,
    y2: compartment.y2,
    z1: compartment.z1,
    z2: compartment.z2,
    flip: 1,
    handle: 1,
    direction: 1,
    parity: 'double',
  });
  newDoors.push({
    x1: splitX,
    x2: compartment.x2,
    y1: compartment.y1,
    y2: compartment.y2,
    z1: compartment.z1,
    z2: compartment.z2,
    flip: 0,
    handle: 0,
    direction: 2,
    parity: 'double',
  });
  jetty.doors.push(...newDoors);
  compartment.doors.push(...newDoors);
};
