import { initDrawing, updateDrawingSize } from "../render/common.js";
import { makeCompartments, remakeCompartments } from './compartments.js';
import { geometryFields, insertsDepthOffset, supportWidth } from "./constants.js";
import { shelfTypeNames } from "../constants.js";
import {
  addBacksToCompartment,
  addGrommetToCompartment,
  addHorizontalInsertToCompartment,
  addSupportToCompartment,
  adjustHorizontalInserts,
  changeDepth,
  clearCompartment,
  clearGrommets,
  clearHorizontalInserts,
  setCompartmentInsertsDepth,
} from './edit_tools/compartment_fills.js';
import { addDoorToCompartment, addDoubleDoorToCompartment, } from "./edit_tools/doors.js";
import {
  addDoubleDrawerInsertToCompartment,
  addDoubleDrawerToCompartment,
  addDrawerToCompartment,
} from "./edit_tools/drawers.js";
import { createLegs, createLongLegs, removeLegs, removeLongLegs, } from "./edit_tools/legs.js";
import { mirrorShelf } from './edit_tools/mirror.js';
import { addHorizontal, addVertical, changeRowHeight, moveVertical, removeVertical, } from "./edit_tools/structural.js";
import { getFinalGeometry } from '../geometry_cleanup.js';
import { renderJetty, } from '../render/render_jetty.js';
import {
  addHorizontalButton,
  addVerticalButton,
  depthRange,
  fillSelect,
  grommetSelect,
  horizontalInsertsSelect,
  insertsDepthButton,
  insertsDepthSelect,
  jettyInput,
  mirrorShelfButton,
  moveVerticalButton,
  orientationSelect,
  removeLeftVerticalButton,
  removeRightVerticalButton,
  rowHeightSelect,
  swapLegsButton,
  updateDepthRange,
  updateUi,
} from './ui.js';
import { renderCompartments } from "../render/compartments.js";


export let selectedCompartment = null;
const drawing = initDrawing('drawing', jetty);

const onCompartmentClick = (compartment) => {
  selectedCompartment = compartment;
  renderCompartments(
    jetty,
    drawing,
    compartments,
    selectedCompartment,
    onCompartmentClick,
  );
  updateUi(selectedCompartment);
}

const onRowHeightSelectChange = () => {
  const value = parseInt(rowHeightSelect.value);
  let rowHeightChange = value - (selectedCompartment.y2 - selectedCompartment.y1);
  changeRowHeight(rowHeightChange, selectedCompartment, compartments);
  jetty.height += rowHeightChange;
  if (rowHeightChange > 0) {
    updateDrawingSize(drawing, jetty);
    updateInfo(jetty);
  }
  compartments = remakeCompartments(compartments, jetty);
  selectedCompartment = null;
  updateRender();
  updateUi(selectedCompartment, true);
  jettyInput.value = getFinalGeometry(jetty, geometryFields);
}

const onFillSelectChange = () => {
  const value = fillSelect.value;
  clearCompartment(selectedCompartment);
  if (value === 'none') clearGrommets(selectedCompartment);
  if (value === 'door') addDoorToCompartment(selectedCompartment);
  if (value === 'doubleDoor') addDoubleDoorToCompartment(selectedCompartment, jetty);
  if (value === 'drawer') {
    addDrawerToCompartment(selectedCompartment);
    // there can be no inserts in a compartment with a drawer
    clearHorizontalInserts(selectedCompartment);
  }
  if (value === 'doubleDrawer') {
    addDoubleDrawerToCompartment(selectedCompartment);
    // leave only insert between drawers
    clearHorizontalInserts(selectedCompartment);
    addDoubleDrawerInsertToCompartment(selectedCompartment);
  }
  if (value === 'support') {
    addSupportToCompartment(selectedCompartment);
    clearGrommets(selectedCompartment);
  }
  if (
    value === 'drawer' ||
    value === 'doubleDrawer' ||
    value === 'door' ||
    value === 'doubleDoor' ||
    value === 'back'
  ) {
    addGrommetToCompartment(selectedCompartment);
    addBacksToCompartment(selectedCompartment);
  }
  if (selectedCompartment.horizontalInserts.length > 0)
    adjustHorizontalInserts(selectedCompartment);
  updateRender();
  updateUi(selectedCompartment, true);
  jettyInput.value = getFinalGeometry(jetty, geometryFields);
}

const onOrientationSelectChange = () => {
  const value = orientationSelect.value;
  let flipValue = value === 'left' ? 1 : 0;
  let directionValue = value === 'left' ? 1 : 2;
  selectedCompartment.doors.forEach(door => {
    if (selectedCompartment.doors.filter(d => !d.removed).length === 1) {
      door.direction = directionValue;
      door.flip = flipValue;
    } else {
      delete door.parity;  // otherwise PS will overwrite the change
      // NOTE: renderer does not support flipped double doors,
      //  it will display two handles
      door.handle = door.flip === flipValue ? 0 : 1;
    }
  })
  selectedCompartment.drawers.forEach(drawer => {
    drawer.direction = directionValue;
    drawer.flip = flipValue;
  })
  selectedCompartment.supports.forEach(support => {
    if (value === 'left') {
      support.x1 = selectedCompartment.x1
      support.x2 = selectedCompartment.x1 + supportWidth;
    } else {
      support.x1 = selectedCompartment.x2 - supportWidth
      support.x2 = selectedCompartment.x2;
    }
  })
  updateRender();
  updateUi(selectedCompartment, true);
  jettyInput.value = getFinalGeometry(jetty, geometryFields);
}

const onGrommetSelectChange = () => {
  clearGrommets(selectedCompartment);
  [...grommetSelect.options].forEach(option => {
    if (option.selected)
      addGrommetToCompartment(selectedCompartment, parseInt(option.value));
  })
  updateRender();
  updateUi(selectedCompartment, true);
  jettyInput.value = getFinalGeometry(jetty, geometryFields);
}

const onHorizontalsInsertsChange = () => {
  clearHorizontalInserts(selectedCompartment);
  setCompartmentInsertsDepth(selectedCompartment, insertsDepthSelect.value, jetty);
  [...horizontalInsertsSelect.options].forEach(option => {
    if (option.selected) {
      addHorizontalInsertToCompartment(selectedCompartment, parseInt(option.value));
      addBacksToCompartment(selectedCompartment);
    }
  })
  // if there are drawers in the compartment, adjust inserts subtype
  adjustHorizontalInserts(selectedCompartment);
  updateRender();
  updateUi(selectedCompartment, true);
  jettyInput.value = getFinalGeometry(jetty, geometryFields);
}

const onInsertsDepthChange = () => {
  // adjust inserts subtype and front offset
  setCompartmentInsertsDepth(selectedCompartment, insertsDepthSelect.value, jetty);

  adjustHorizontalInserts(selectedCompartment);
  updateRender();
  updateUi(selectedCompartment, true);
  jettyInput.value = getFinalGeometry(jetty, geometryFields);
}

const onUnifyInsertsDepth = () => {
  // adjust inserts subtype and front offset in all compartments
  let insertsDepth = insertsDepthOffset[insertsDepthSelect.value];

  compartments.forEach(compartment => {
    compartment.insertsDepth = insertsDepth;
    adjustHorizontalInserts(compartment);
  });

  updateRender();
  updateUi(selectedCompartment, true);
  jettyInput.value = getFinalGeometry(jetty, geometryFields);
}

const onShelfMirror = () => {
  jetty = mirrorShelf(jetty);
  compartments = remakeCompartments(compartments, jetty);
  selectedCompartment = null;
  updateRender();
  updateUi(selectedCompartment, true);
  jettyInput.value = getFinalGeometry(jetty, geometryFields);
}

const onSwapLegs = () => {
  if (swapLegsButton.innerText === 'Remove long legs') {
    removeLongLegs(jetty);
    createLegs(jetty);
  } else {
    removeLegs(jetty);
    createLongLegs(jetty);
  }
  updateRender();
  updateUi(selectedCompartment, true);
  jettyInput.value = getFinalGeometry(jetty, geometryFields);
}

const onDepthChange = () => {
  let depthChange = depthRange.value * 10 - jetty.depth;
  changeDepth(depthChange, compartments);
  jetty.depth += depthChange;
  updateInfo(jetty);
  updateRender();
  updateUi(selectedCompartment, true);
  jettyInput.value = getFinalGeometry(jetty, geometryFields);
}

const updateRender = () => {
  renderJetty(drawing, jetty);
  renderCompartments(
    jetty,
    drawing,
    compartments,
    selectedCompartment,
    onCompartmentClick,
  );
}

const updateInfo = (jetty) => {
  let shelfType = shelfTypeNames[jetty['shelf_type']];
  let productInfo = productID > 0 ? `&nbsp; Product ID: ${productID}` : '';
  document.getElementById('shelf_info').innerHTML =  `
    ${shelfType} <br>
    Gallery ID: ${jetty.id} ${productInfo}<br>
    Dimensions: ${jetty.height}x${jetty.width}x${jetty.depth} [mm]
  `;
  updateDepthRange(depthRange, jetty.depth / 10);
}

const onAddHorizontal = () => {
  let splitHeight = parseInt(addHorizontalButton.value)
  addHorizontal(splitHeight, selectedCompartment, jetty);
  compartments = remakeCompartments(compartments, jetty);
  selectedCompartment = null;
  updateRender();
  updateUi(selectedCompartment, true);
  jettyInput.value = getFinalGeometry(jetty, geometryFields);
}

const onMoveVertical = () => {
  let distance = parseInt(moveVerticalButton.value);
  let side = moveVerticalButton.side;
  moveVertical(side, distance, selectedCompartment, jetty);
  compartments = remakeCompartments(compartments, jetty);
  selectedCompartment = null;
  updateRender();
  updateUi(selectedCompartment, true);
  jettyInput.value = getFinalGeometry(jetty, geometryFields);
}

const onAddVertical = () => {
  addVertical(selectedCompartment, jetty);
  compartments = remakeCompartments(compartments, jetty);
  selectedCompartment = null;
  updateRender();
  updateUi(selectedCompartment, true);
  jettyInput.value = getFinalGeometry(jetty, geometryFields);
}

const onRemoveVertical = (side) => {
  removeVertical(side, selectedCompartment, jetty);
  compartments = remakeCompartments(compartments, jetty);
  selectedCompartment = null;
  updateRender();
  updateUi(null, true);
  jettyInput.value = getFinalGeometry(jetty, geometryFields);
}

jettyInput.value = getFinalGeometry(jetty, geometryFields);
let compartments = makeCompartments(jetty)

updateRender();
updateInfo(jetty);
updateUi();
rowHeightSelect.onchange = onRowHeightSelectChange;
fillSelect.onchange = onFillSelectChange;
orientationSelect.onchange = onOrientationSelectChange;
grommetSelect.onchange = onGrommetSelectChange;
horizontalInsertsSelect.onchange = onHorizontalsInsertsChange;
mirrorShelfButton.onclick = onShelfMirror;
swapLegsButton.onclick = onSwapLegs;
addHorizontalButton.onclick = onAddHorizontal;
moveVerticalButton.onclick = onMoveVertical;
depthRange.onchange = onDepthChange;
insertsDepthSelect.onchange = onInsertsDepthChange;
insertsDepthButton.onclick = onUnifyInsertsDepth;
addVerticalButton.onclick = onAddVertical;
removeLeftVerticalButton.onclick = () => onRemoveVertical('left');
removeRightVerticalButton.onclick = () => onRemoveVertical('right');
