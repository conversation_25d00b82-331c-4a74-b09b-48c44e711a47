import { initDrawing } from "../render/common.js";
import { compareJetty } from "./compare.js";
import { shelfTypeNames } from "../constants.js";
import { renderJetty } from '../render/render_jetty.js';
import { getChangedDimensions, getChangedElementsList } from "../common_compare_utils.js";
import { geometryFields } from "./constants.js";

const editedDrawing = initDrawing('editedDrawing', editedGallery);
const targetDrawing = initDrawing('targetDrawing', targetGallery);

const updateRender = () => {
  editedDrawing.css({width: '90%'});
  targetDrawing.css({width: '90%'});
  renderJetty(editedDrawing, editedGallery);
  renderJetty(targetDrawing, targetGallery);
}

const updateInfo = (jetty, productID, infoBoxID) => {
  let shelfType = shelfTypeNames[jetty['shelf_type']];
  let productInfo = productID > 0 ? `&nbsp; Product ID: ${productID}` : '';
  document.getElementById(infoBoxID).innerHTML =  `
    ${shelfType} <br>
    Gallery ID: ${jetty.id} ${productInfo}<br>
    Dimensions: ${jetty.height}x${jetty.width}x${jetty.depth} [mm]
  `;
}

const updateChangesInfo = (editedGallery, targetGallery) => {
  let changesInfo = document.getElementById('changesList');
  changesInfo.prepend(getChangedDimensions(editedGallery, targetGallery));
  changesInfo.appendChild(getChangedElementsList(editedGallery, targetGallery, geometryFields));
}

compareJetty(editedGallery, targetGallery);
updateRender();
updateInfo(editedGallery, editedProductID, 'draftShelfInfo');
updateInfo(targetGallery, targetProductID, 'targetShelfInfo');
updateChangesInfo(editedGallery, targetGallery);
