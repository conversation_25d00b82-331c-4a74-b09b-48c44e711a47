import { thickness } from "../constants.js";

export const geometryFields = [
  'doors',
  'backs',
  'drawers',
  'walls',
  'slabs',
  'frame',
  'bars',
  'hinges',
  'masking_bars',
  'lighting',
  'legs',
  'cable_management',
];

export const elementTypeCodes = {
  slabs: 'S',
  bars: 'R',
  drawers: 'T',
  cable_management: 'G',
};
export const elementFieldsByCodes = {
  S: 'slabs',
  R: 'bars',
  T: 'drawers',
  G: 'cable_management',
};
export const doorHandle = {
  primary: 0,
  secondary: 1,
}
export const maxInternalDrawerDepth = 697;
export const externalDrawerDepths = [352, 402, 452, 502, 552, 602];
export const minDepthWithDrawersType13 = 360;
export const externalDrawerDepthsType13 = {
  360: 288,
  450: 368,
  600: 518,
};
export const externalDrawerDepthsVeneer13 = {
  360: 278,
  450: 358,
  600: 508,
};
export const internalDrawerDepthsType13 = {
  360: 282,
  450: 362,
  600: 512,
}
export const externalDrawerDepthsType23 = {
  360: 308,
  460: 408,
  560: 508,
};

export const externalDrawerMinimumOffset = 36;
export const t03crossbarDepthThresholds = {
  lighting:
    {
      400: 300,
      450: 340,
      510: 413,
    },
  default:
    {
      400: 340,
      480: 413,
    }
};
export const t13crossbarDepthThresholds = {
  270: 190,
  360: 280,
  450: 370,
}
export const crossBarZAxisOffset = {
  lighting: -14.5,
  default: 9,
}
export const maxDepthForCrossbar = 520;
export const minWidthForDoubleBar = 900;
export const standardBarYOffsetBelowSlab = 91;
export const t03wallToFrameDistance = 15;
export const standardBarZRadius = 10;
export const standardBarYRadius = 20;
export const crossBarXRadius = 6;
export const crossBarHeight = 85;
export const wallTopFinHeight = 18;
export const externalDrawersZOffsetType13 = 17;
export const externalDrawersZOffsetVeneer13 = 15;
export const doorYOffset = 4;
export const doorZOffsetType13 = 17;
export const doorZOffsetVeneer13 = 15;
export const doorThicknessType13 = 18;
export const maxSingleDoorWidth = 590;
export const t13StandardWardrobeHeightSlabY2 = 1939;
export const t13SlabStandardBackZOffset = 18;
export const t13BackZOffset = 6;
export const t13BackThickness = 12;
export const t24BackZOffset = 16;
export const t24BackThickness = 18;
export const t13SlabTopBackZOffset = t13BackZOffset;
export const t13slabZOffsetDependingOnDoors = {
  true: 38,
  false: 19,
}
export const t13CrossbarZOffsetDependingOnDoors = {
  true: 38 + 12,
  false: 19 + 22,
}
export const t13CrossbarToBarAdjustOffset = 26;
export const doubleCrossbarWallOffset = 263;

export const t23frontZOffset = 4;
export const t23slabFrontZOffset = t23frontZOffset + thickness;
export const t23exteriorWallsBottomOffset = 4;
export const t24exteriorWallsBottomOffset = 5;
export const t24middleWallsBackOffset = 10;
export const t24cableManagementSlabsZOffset = 20;
export const t23legZOffset = 35;
export const t23legXOffset = 58;
export const t23legHeight = 24;
export const t25legHeight = 100;
export const t25legRadius = 30;
export const horizontalGrommetZOffset = 97;
export const hingeSettingsT13 = {
  height: 41,
  width: 39,
  depth: 64,
  yOffsetBottom: 70,
  yOffsetTop: 70,
  zOffset: 35,
  excludeSlabSubtypes: [],
}
export const hingeSettingsV13 = {
  height: 41,
  width: 39,
  depth: 64,
  yOffsetBottom: 70,
  yOffsetTop: 70,
  zOffset: 33,
  excludeSlabSubtypes: [],
}
export const hingeSettingsT23 = {
  height: 41,
  width: 39,
  depth: 64,
  yOffsetBottom: 70 - 21 - t23exteriorWallsBottomOffset,
  yOffsetTop: 50 - 21,
  zOffset: thickness,
  excludeSlabSubtypes: ['b', 'f', 't'],
}
