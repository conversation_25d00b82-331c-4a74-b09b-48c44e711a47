import {
  externalDrawerDepths,
  externalDrawerMinimumOffset,
  maxInternalDrawerDepth,
} from "../constants.js";

const createDrawers = (selectedColumn, elements) => {
  let drawers = []
  elements.forEach(element => {
    if (element.type !== 'drawers') return;
    let z2, depth;
    let subtype = element.doors ? 'i' : 'e';  // internal or external drawer
    z2 = subtype === 'e' ? watty.depth - 12 : selectedColumn.z2 - 15;
    if (subtype === 'e') {
      // external drawers depth is fixed within brackets
      depth = z2 - externalDrawerMinimumOffset;
      depth = externalDrawerDepths.reduce((currentMax, currentDepth) => (
        currentDepth <= depth ? currentDepth : currentMax
      ));
    } else {
      // internal drawers depth changes with column depth up to a maximum
      depth = (z2 - 18 < maxInternalDrawerDepth) ? z2 - 18 : maxInternalDrawerDepth;
    }
    if (subtype === 'e' && element.y1 < 100) subtype = 'b';  // drawer bottom
    drawers.push({
      x1: selectedColumn.x1,
      x2: selectedColumn.x2,
      y1: element.y1,
      y2: element.y2,
      z1: z2 - depth,
      z2,
      subtype,
    });
  });
  watty.drawers.push(...drawers);
  selectedColumn.drawers.push(...drawers);
}

const createSlabs = (selectedColumn, elements) => {
  let slabs = []
  let slabElements = elements.filter(element => element.type === 'slabs')
  slabElements[slabElements.length - 1].subtype = 'b' // slab base
  slabElements[0].subtype = 't'; // slab top

  slabElements.forEach(element => {
    // slab standard
    let subtype = element.subtype === undefined ? 's' : element.subtype;
    let drawerAbove = selectedColumn.drawers.find(drawer => (
      drawer.y1 >= element.y2 && drawer.y1 < elements.y2 + 50
    ));
    // slab drawer
    if (drawerAbove) drawerAbove.subtype = 'd';
    // todo: slab push over 237 cm at 200 cm height
    slabs.push({
      x1: selectedColumn.x1,
      x2: selectedColumn.x2,
      y1: element.y1,
      y2: element.y2,
      z1: selectedColumn.z1 + 18,  // todo: slab drawer
      z2: selectedColumn.z2 - 15,
      subtype,
    });
  });
  watty.slabs.push(...slabs);
  selectedColumn.slabs.push(...slabs);
}

const adjustWalls = () => {
  watty.walls.forEach(wall => {
    // find the highest slab bordering the wall on either side
    let highestSlab = watty.slabs.reduce((highestSlab, slab) => (
      (
        slab.x2 === wall.x1 ||
        slab.x1 === wall.x2
      ) && !slab.removed && slab.y1 > highestSlab.y1 ? slab : highestSlab
    ));
    wall.y2 = highestSlab.y2;
  })
};

export const buildType03Column = (selectedColumn, parsedElements, parsedDoors) => {
  // add new elements to column, set subtypes for slabs, bars and drawers
  createDrawers(selectedColumn, parsedElements);
  createSlabs(selectedColumn, parsedElements);
  // add new doors to column
  // distribute hinges
  // distribute backs based on column height
  // make walls
  adjustWalls();
}
