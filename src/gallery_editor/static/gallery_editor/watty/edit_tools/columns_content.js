import { shelfTypeValues, thickness } from "../../constants.js";
import { doorYOffset, elementFieldsByCodes, geometryFields, } from "../constants.js";
import { buildType03Column } from "./build_column_type03.js";
import { buildType13Column, buildVeneer13Column } from "./build_column_type13.js";
import { buildType23Column, buildType24Column, buildType25Column } from "./build_column_matty.js";
import { describeColumnContent } from "./describe_column_content.js";

export const changeColumnWidth = (selectedColumn, width) => {
  let columnWidth = selectedColumn.x2 - selectedColumn.x1;
  let widthChange = width - columnWidth;
  let columnAxis = selectedColumn.x1 + columnWidth / 2;
  watty.width += widthChange;
  // split doors, multiply crossbars when over threshold width
  geometryFields.forEach(field => {
    let geometry = watty[field];
    geometry.forEach(element => {
      if (field === "bars" && element.subtype === "z") {
        if (element.x1 < columnAxis && element.x2 > columnAxis) return;
      }
      if (element.x1 < columnAxis) element.x1 -= widthChange / 2;
      if (element.x2 < columnAxis) element.x2 -= widthChange / 2;
      if (element.x1 > columnAxis) element.x1 += widthChange / 2;
      if (element.x2 > columnAxis) element.x2 += widthChange / 2;
    });
  });
  selectedColumn.x1 -= widthChange / 2;
  selectedColumn.x2 += widthChange / 2;
  updateColumnContent(selectedColumn);
}

export const duplicateColumn = (selectedColumn) => {
  let columnWidth = selectedColumn.x2 - selectedColumn.x1 + thickness;
  let duplicatedGeometry = {};
  geometryFields.forEach(field => {
    let geometry = watty[field];
    duplicatedGeometry[field] = [];
    // push stuff around selected column
    geometry.forEach(element => {
      // push anything to the left of the column by half column width
      if (element.x2 <= selectedColumn.x1) {
        element.x1 -= columnWidth / 2;
        element.x2 -= columnWidth / 2;
        return;
      }
      // copy geometry inside selected column, moving it to the left
      if (
        element.x1 >= selectedColumn.x1 - thickness / 2 &&
        element.x2 <= selectedColumn.x2 + thickness
      ) duplicatedGeometry[field].push({
        ...element,
        x1: element.x1 - columnWidth / 2,
        x2: element.x2 - columnWidth / 2,
        drawing: null,
        drawingFromAbove: null,
      });
      // push anything else (including selected column content) to the right
      element.x1 += columnWidth / 2;
      element.x2 += columnWidth / 2;
    });
  });
  // update watty with duplicated geometry and new width
  geometryFields.forEach(field => {
    watty[field].push(...duplicatedGeometry[field]);
  });
  watty.width += columnWidth;
  // move column as well, to rebuild it correctly
  selectedColumn.x1 += columnWidth / 2;
  selectedColumn.x2 += columnWidth / 2;
  updateColumnContent(selectedColumn);
}

export const removeColumn = (selectedColumn, columns) => {
  let columnWidth = selectedColumn.x2 - selectedColumn.x1 + thickness;
  geometryFields.forEach(field => {
    let geometry = watty[field];
    // pull stuff around selected column
    geometry.forEach(element => {
      if (element.x2 <= selectedColumn.x1) {
        // pull anything to the left of the column by half column width
        element.x1 += columnWidth / 2;
        element.x2 += columnWidth / 2;
      } else if (
        // remove geometry inside selected column
        element.x1 >= selectedColumn.x1 - thickness / 2 &&
        element.x2 <= selectedColumn.x2 + thickness
      ) {
        element.removed = true;
      } else {
        // pull anything else to the right
        element.x1 -= columnWidth / 2;
        element.x2 -= columnWidth / 2;
      }
    });
  });
  watty.width -= columnWidth;
}

export const changeColumnDepth = (selectedColumn, depth) => {
  selectedColumn.z2 = depth;
  updateColumnContent(selectedColumn);
}

export const changeColumnBack = (selectedColumn, back) => {
  selectedColumn.z1 = back;
  updateColumnContent(selectedColumn);
}


const parseElements = content => {
  let elements = [];
  let lines = content.split("\n");
  lines.forEach(line => {
    if (line === "position,element,door" || line === "") return;
    let [position, type, doors] = line.split(",");
    let height = thickness;
    let merge = [];
    if (type.includes("T")) {
      // retrieve merge info appended to drawer type
      if (type.includes("r")) merge.push("right");
      if (type.includes("l")) merge.push("left");
      // retrieve height appended to drawer type
      let matches = type.match(/\d+/);
      height = matches ? parseInt(matches[0], 10) : 150;
      type = "T";
    }
    let y1 = parseInt(position) - thickness / 2;
    elements.push({
      type: elementFieldsByCodes[type],
      y1: y1,
      y2: y1 + height,
      doors,
      merge,
    });
  });
  elements.sort((a, b) => b.y2 - a.y2);
  return elements;
};

const reconstructDoors = elements => {
  let currentDoor = {y1: null, y2: null}
  let doors = [];
  elements.forEach(element => {
    if (element.doors) {
      currentDoor.y2 = currentDoor.y2 || element.y2;
      currentDoor.y1 = element.y1;
    } else {
      if (currentDoor.y1 !== null) {
        doors.push(currentDoor);
        currentDoor = {y1: null, y2: null};
      }
    }
  });
  if (currentDoor.y1 !== null) doors.push(currentDoor);

  // NOTE: External drawers usually cover the slab above them.
  //  If there are doors that would end at this slab, they are
  //  22 mm shorter instead (to keep the 4 mm offset to the drawer).
  //  To correctly reconstruct these doors, we need to adjust their y1
  const slabBehindDrawerToDoorOffset = thickness + doorYOffset;
  doors.forEach(door => {
    let externalDrawerDirectlyBelow = elements.find(element => (
      element.y2 === door.y1 - slabBehindDrawerToDoorOffset &&
      element.type === "drawers"
    ));
    if (externalDrawerDirectlyBelow) {
      door.y1 += slabBehindDrawerToDoorOffset;
    }
  })
  return doors;
};

const removeColumnContent = selectedColumn => {
  geometryFields.forEach(field => {
    if (field === "walls") return;
    let geometry = watty[field];
    geometry.forEach(element => {
      if (
        element.x2 < selectedColumn.x1 ||
        element.x1 > selectedColumn.x2
      ) return;
      element.removed = true;
    });
  });
};

export const changeColumnContent = (selectedColumn, content) => {
  let parsedElements = parseElements(content);
  let parsedDoors = reconstructDoors(parsedElements);
  removeColumnContent(selectedColumn);
  if (watty.shelf_type === shelfTypeValues.TYPE_13)
    buildType13Column(selectedColumn, parsedElements, parsedDoors);
  else if (watty.shelf_type === shelfTypeValues.VENEER_13)
    buildVeneer13Column(selectedColumn, parsedElements, parsedDoors);
  else if (watty.shelf_type === shelfTypeValues.TYPE_23)
    buildType23Column(selectedColumn, parsedElements, parsedDoors);
  else if (watty.shelf_type === shelfTypeValues.TYPE_24)
    buildType24Column(selectedColumn, parsedElements, parsedDoors);
  else if (watty.shelf_type === shelfTypeValues.TYPE_25)
    buildType25Column(selectedColumn, parsedElements, parsedDoors);
  else
    buildType03Column(selectedColumn, parsedElements, parsedDoors);
}

export const updateColumnContent = (selectedColumn) => {
  let content = describeColumnContent(selectedColumn);
  changeColumnContent(selectedColumn, content);
}
