{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Editing Jetty {{ gallery_object.id }}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css"
          integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N" crossorigin="anonymous">
    <link rel="icon" type="image/png" sizes="32x32" href={% static "gallery_editor/jetty/favicon.png" %}>
</head>
<body>
<div class="container">
    <div class="row">
        <div id="compartment_box" class="col">
            <div class="alert alert-secondary">
                <h3>Compartment:</h3>
                <p id="compartment_info"></p>
            </div>
            <div class="input-group mb-3">
                <label class="input-group-text" for="rowHeight">Row height:</label>
                <select class="custom-select" name="rowHeight" id="rowHeight">
                    <option value="0">Double</option>
                    <option value="182">A</option>
                    <option value="282">B</option>
                    <option value="382">C</option>
                    <option value="482">D</option>
                    <option value="582">E</option>
                    <option value="682">F</option>
                    <option value="782">G</option>
                </select>
            </div>
            <div class="input-group mb-3">
                <label class="input-group-text" for="addHorizontalHeight">Split vertically:</label>
                <select class="custom-select" name="addHorizontalHeight" id="addHorizontalHeight">
                    <option value="" disabled selected>Choose height</option>
                    <option value="A">A</option>
                    <option value="B">B</option>
                    <option value="C">C</option>
                    <option value="D">D</option>
                    <option value="E">E</option>
                </select>
                <button class="btn btn-secondary mr-5" name="addHorizontal" id="addHorizontal">Add a horizontal</button>
            </div>
            <div class="input-group mb-3">
                <label class="input-group-text" for="removeLeftVertical">Edit verticals:</label>
                <div class="btn-group" role="group">
                    <button class="btn btn-secondary" name="removeLeftVertical" id="removeLeftVertical">Remove left
                    </button>
                    <button class="btn btn-secondary" name="addVertical" id="addVertical">Add a vertical</button>
                    <button class="btn btn-secondary" name="removeRightVertical" id="removeRightVertical">Remove right
                    </button>
                </div>
            </div>
            <div class="input-group mb-3">
                <label class="input-group-text" for="moveVertical">Local edge:</label>
                <select class="custom-select" name="moveVerticalChoice" id="moveVerticalChoice">
                    <option value="" disabled selected>Choose vertical</option>
                    <option value="left">left vertical</option>
                    <option value="right">right vertical</option>
                </select>
                <input type="number" class="form-control" id="moveVerticalRange" min="-100" max="100" disabled>
                <div class="input-group-append">
                    <span class="input-group-text" id="moveVerticalAppend">mm</span>
                    <button class="btn btn-secondary mr-5" name="moveVertical" id="moveVertical" disabled>Move
                        vertical
                    </button>
                </div>
            </div>
            <div class="input-group mb-3">
                <label class="input-group-text" for="fill">Fill type:</label>
                <select class="custom-select" name="fill" id="fill">
                    <option value="none">None</option>
                    <option value="door">Doors</option>
                    <option value="doubleDoor">Double doors</option>
                    <option value="drawer">Drawer</option>
                    <option value="doubleDrawer">Double drawer</option>
                    <option value="back">Back</option>
                    <option value="support">Support</option>
                </select>
            </div>
            <div class="input-group mb-3">
                <label class="input-group-text" for="orientation">Orientation:</label>
                <select class="custom-select" name="orientation" id="orientation">
                    <option value="left">Left (handle on the right)</option>
                    <option value="right">Right (handle on the left)</option>
                </select>
            </div>
            <div class="input-group mb-3">
                <label class="input-group-text" for="grommet">Back grommet:</label>
                <select class="custom-select" name="grommet" id="grommet" multiple>
                    <option value="50">Height A/2</option>
                    <option value="150">Height A</option>
                    <option value="250">Height B</option>
                    <option value="350">Height C</option>
                    <option value="450">Height D</option>
                    <option value="550">Height E</option>
                    <option value="650">Height F</option>
                </select>
            </div>
            <div class="input-group mb-3">
                <label class="input-group-text" for="horizontalInserts">Horizontal inserts:</label>
                <select class="custom-select" name="horizontalInserts" id="horizontalInserts" multiple>
                    <option value="100">Height A/2</option>
                    <option value="200">Height A</option>
                    <option value="300">Height B</option>
                    <option value="400">Height C</option>
                    <option value="500">Height D</option>
                    <option value="600">Height E</option>
                    <option value="700">Height F</option>
                </select>
                <label class="input-group-text" for="insertsDepth">Depth:</label>
                <div class="input-group-append" style="flex-direction: column; justify-content: space-around;">
                    <select class="custom-select" name="insertsDepth" id="insertsDepth">
                        <option value="standard" title="Retracted by 3 cm">Standard</option>
                        <option value="full-depth" title="Mosaic-style flush with front">Full-depth</option>
                    </select>
                    <button class="btn btn-secondary" name="unifyInsertsDepth" id="unifyInsertsDepth" disabled>Set for
                        all
                    </button>
                </div>
            </div>
        </div>
        <div id="shelf_box" class="col">
            <div
                {% if is_draft is True %}
                    class="alert alert-secondary"
                {% elif product_id is -1 %}
                    class="alert alert-info"
                {% else %}
                    class="alert alert-warning"
                {% endif %}
                    role="alert"
            >
                <h3>Shelf Info:</h3>
                <img src={% static "gallery_editor/pan_szafka.svg" %} width="150px" class="float-right">
                <p id="shelf_info"></p>
                <p><em>{{ description }}</em></p>
                <p>
                    Original Price: {{ original_price }} PLN<br>
                    Current Price: {{ price }} PLN<br>
                    <b>Price Change: {{ price_change }} PLN</b>
                </p>
            </div>
            <div class="input-group pb-3">
                <button class="btn btn-info mr-5" name="mirror" id="mirrorShelf">Mirror</button>
                <button class="btn btn-info mr-5" name="legs" id="swapLegs">Swap legs type</button>
            </div>
            <div class="input-group pb-3" {% if is_production is True %} style="display: none" {% endif %}>
                <label for="depthRange" class="input-group-text mr-2" id="depthRangeLabel">Depth: [cm]</label>
                <input type="range" style="width: 50%" id="depthRange" min="20" max="60">
            </div>
            {% if errors %}
                <div class="alert alert-danger" role="alert">
                    <h4>Errors:</h4>
                    <ul>
                        {% for error in errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                </div>
            {% else %}
                <div class="alert alert-success" role="alert">
                    <p>No errors</p>
                </div>
            {% endif %}
            <div class="text-right pb-3">
                <form id="jettyForm" method="post">
                    {% csrf_token %}
                    <input name="changed_geometry" id="jettyInput" type="hidden" value="{}">
                    <input name="product_id" id="productId" type="hidden" value="{{ product_id|safe }}">
                    <input name="original_price" type="hidden" value="{{ original_price }}">

                    <button class="btn btn-light" name="recalculate_price" id="recalculatePrice">Check price &
                        validate
                    </button>
                    <button
                        {% if errors|length_is:0 %}
                            class="btn btn-primary"
                        {% else %}
                            class="btn btn-danger" title="Save on your own responsibility."
                        {% endif %}
                            name="save_draft_object" id="saveDraftObject">Save Draft
                    </button>
                    <button
                        {% if errors|length_is:0 %}
                            class="btn btn-success"
                        {% else %}
                            class="btn btn-danger" title="Save on your own responsibility."
                        {% endif %}
                            type="submit" name="save_original_object" id="saveOriginalObject">Save Shelf
                    </button>
                    <br/>
                    <br/>
                    <button class="btn btn-info" name="get_front_view" id="getFrontView" type="button">
                        <span>Get front view</span>
                        <span class="spinner-border spinner-border-sm" style="display: none"
                              id="frontViewSpinner"></span>
                    </button>

                    <button class="btn btn-info" name="get_image" id="getImage" type="button">Get image</button>
                </form>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col" id="drawing">
        </div>
    </div>
    <script type="text/javascript">
        let jetty = {{ gallery_object|safe }};
        let productID = {{ product_id|safe }};
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/svg.js/3.1.2/svg.min.js" type="text/javascript"></script>
    <script src="{% static "gallery_editor/jetty/editor.js" %}" type="module"></script>
</div>
</body>
</html>
