import json
import logging

from functools import partial
from typing import (
    TYPE_CHECKING,
    Any,
    Type,
)

from django.contrib import (
    admin,
    messages,
)
from django.contrib.admin.helpers import ACTION_CHECKBOX_NAME
from django.core.paginator import Paginator
from django.db.models import QuerySet
from django.http import HttpResponseRedirect
from django.shortcuts import (
    redirect,
    render,
)
from django.utils.html import escape
from django.utils.safestring import mark_safe

from django_object_actions import action as django_object_action

from custom.admin_action import admin_action_with_form
from custom.admin_permissions import group_member_required
from custom.enums.enums import AdminGroup
from customer_service.correction_request_strategies import (
    CorrectionRequestAmountStrategy,
    CorrectionRequestNeutralizationStrategy,
)
from customer_service.models import CSCorrectionRequest
from customer_service.utils import create_neutralize_invoice_correction_request
from invoice.choices import InvoiceStatus
from invoice.forms import (
    InvoiceCachedToDictForm,
    InvoiceCorrectionAmountForm,
    InvoiceNeutralizationForm,
    SalesReportForm,
    SellAtInvoiceForm,
)
from invoice.models import (
    Invoice,
    InvoiceDomestic,
)
from invoice.symfonia import (
    export_to_fka,
    revert_storno_adjustments,
    symfonia_queryset,
)
from invoice.tasks import (
    export_whole_invoices,
    generate_sales_report_and_send,
)

if TYPE_CHECKING:
    from django.contrib.admin import ModelAdmin
    from django.forms import forms
    from django.http import (
        HttpRequest,
        HttpResponse,
    )


logger = logging.getLogger('invoice')


@mark_safe
@admin.display(description='Invoice pdf')
def pdf_link(obj: Invoice) -> str:
    if obj.pdf:
        return "<a href='{}'> link</a>".format(obj.pdf.url)
    return 'Not yet generated, generate!'


@mark_safe
@admin.display(description='Order info')
def order_summary(obj: Invoice) -> str:
    if obj.order is None:
        return 'No order'
    if obj.sent_invoice_at is None:
        return (
            '<a href="/admin/orders/order/{}/">order</a><br/>'
            '<span style="color:red">Invoice not yet sent to customer,<br/> '
            'Type: {} <br/>{}/{} vat</span>'
        ).format(
            obj.order.id,
            obj.order.get_order_type_display(),
            escape(obj.order.vat),
            escape(obj.order.invoice_vat),
        )
    return (
        '<a href="/admin/orders/order/{}/">order</a><br/>'
        'Type: {} <br/>Notes: {} <br/>Assembly: {}, vat: {}'
    ).format(
        obj.order.id,
        obj.order.get_order_type_display(),
        escape(obj.order.order_notes),
        obj.order.assembly,
        escape(obj.order.vat),
    )


@mark_safe
@admin.display(description='Order info')
def invoice_items_summary(obj: Invoice) -> str:
    return escape(','.join([x.item_name for x in obj.invoice_items.all()]))


@admin.action(description='Create PDF')
def create_pdf(
    modeladmin: 'ModelAdmin',
    request: 'HttpRequest',
    queryset: QuerySet['Invoice'],
):
    for page in Paginator(queryset, 100):
        for invoice in page.object_list:
            if invoice.status in {
                InvoiceStatus.PROFORMA,
                InvoiceStatus.CORRECTING_PROFORMA,
            }:
                invoice.cached_to_dict = {}
                invoice.cached_delivery_address_country = ''
                invoice.save(
                    update_fields=['cached_to_dict', 'cached_delivery_address_country']
                )
            invoice.create_pdf()


def return_response_with_file(
    modeladmin: 'ModelAdmin',
    request: 'HttpRequest',
    queryset: QuerySet['Invoice'],
    form: 'forms.Form',
) -> 'HttpResponse':
    adjustments = partial(
        revert_storno_adjustments,
        reporting_period=form.cleaned_data['reporting_period'],
    )
    invoices = symfonia_queryset(queryset)
    response_with_file = export_to_fka(modeladmin, invoices, request, adjustments)
    return response_with_file


@admin.action(description='Export WHOLE invoices for accounting')
def export_whole_invoices_by_lines(
    modeladmin: 'ModelAdmin',
    request: 'HttpRequest',
    queryset: QuerySet['Invoice'],
):
    invoice_ids = queryset.exclude(
        status__in=[InvoiceStatus.PROFORMA, InvoiceStatus.CORRECTING]
    ).values_list('pk', flat=True)
    export_whole_invoices.delay(
        user_email=request.user.email,
        queryset_pks=list(invoice_ids),
    )
    messages.info(request, f'The report will soon be sent to {request.user.email}')


@admin.action(description='Export WHOLE invoices for accouting - ONLY CORRECTIONS')
def export_whole_invoices_by_lines_correcting(
    modeladmin: 'ModelAdmin',
    request: 'HttpRequest',
    queryset: QuerySet['Invoice'],
):
    invoice_correction_ids = queryset.filter(
        status__in=[InvoiceStatus.CORRECTING]
    ).values_list('pk', flat=True)
    export_whole_invoices.delay(
        user_email=request.user.email,
        queryset_pks=list(invoice_correction_ids),
    )
    messages.info(request, f'The report will soon be sent to {request.user.email}')


@admin.action(
    description='Create normal based on proforma invoice in sell month, without adding '
    'to production'
)
def create_normal_from_proforma_in_sell_month(
    modeladmin: 'ModelAdmin',
    request: 'HttpRequest',
    queryset: QuerySet['Invoice'],
):
    for proforma_invoice in queryset.order_by('issued_at'):
        invoice = Invoice.objects.create_normal_from_proforma(
            proforma_invoice,
            issued_at=Invoice.objects.generate_issued_at(proforma_invoice),
        )
        if invoice.order.earliest_invoice_domestic_version_supported():
            invoice_domestic = (
                InvoiceDomestic.objects.create_domestic_from_normal_or_proforma(invoice)
            )
            invoice_domestic.create_pdf()

    messages.info(
        request,
        f'Created normal based on proforma invoices: {queryset.count()}',
    )


def _create_and_send_normal_invoice_from_proforma(
    modeladmin: 'ModelAdmin',
    request: 'HttpRequest',
    queryset: QuerySet['Invoice'],
    form: Type['SellAtInvoiceForm'],
    **kwargs: Any,
):
    for proforma_invoice in queryset.filter(status=InvoiceStatus.PROFORMA).order_by(
        'issued_at'
    ):
        invoice = Invoice.objects.create_normal_from_proforma(
            proforma_invoice,
            sell_at=form.cleaned_data['sell_at'],
        )
        in_production = (
            invoice.order.update_to_paid_after_normal_invoice_from_proforma_created(
                sell_date=form.cleaned_data['sell_at']
            )
        )
        if in_production:
            modeladmin.message_user(request, in_production)

        invoice.send_invoice_to_user()
        if invoice.order.earliest_invoice_domestic_version_supported():
            invoice_domestic = (
                InvoiceDomestic.objects.create_domestic_from_normal_or_proforma(invoice)
            )
            invoice_domestic.create_pdf()
            invoice_domestic.send_invoice_to_user()

    modeladmin.message_user(
        request, f'Created normal invoices from proforma: {queryset.count()}'
    )
    return HttpResponseRedirect(request.get_full_path())


@admin.action(description='Create normal invoice based on proforma after it was paid')
def create_normal_invoice_from_proforma(
    modeladmin: 'ModelAdmin',
    request: 'HttpRequest',
    queryset: QuerySet['Invoice'],
):
    return admin_action_with_form(
        modeladmin=modeladmin,
        request=request,
        queryset=queryset,
        form_class=SellAtInvoiceForm,
        success_function=_create_and_send_normal_invoice_from_proforma,
        success_function_kwargs={},
    )


@admin.action(description='Create invoice correction with amount')
def create_invoice_correction_for_amount(
    modeladmin: 'ModelAdmin',
    request: 'HttpRequest',
    queryset: QuerySet['Invoice'],
):
    form = None
    if 'apply' in request.POST:
        form = InvoiceCorrectionAmountForm(request.POST, invoice=queryset[0])
        if form.is_valid() and queryset.count() == 1:
            invoice = queryset[0]
            if invoice.pending_correction_requests.exists():
                messages.add_message(
                    request,
                    messages.ERROR,
                    f'{invoice} pending request already exists',
                )
                return HttpResponseRedirect(request.get_full_path())

            correction_request = CSCorrectionRequest(
                issuer=request.user,
                invoice=invoice,
                correction_amount_gross=form.cleaned_data['amount'],
                tag=form.cleaned_data['tag'],
            )
            strategy = CorrectionRequestAmountStrategy(correction_request)
            strategy.prepare_correction_request(
                issued_at=form.cleaned_data['issued_at'],
            )
            correction_invoice = strategy.accept(
                user=request.user,
                issued_at=correction_request.correction_invoice.issued_at,
            )

            if correction_invoice.order.earliest_invoice_domestic_version_supported():
                InvoiceDomestic.objects.create_domestic_from_correction(
                    correction_invoice
                )
            modeladmin.message_user(request, 'Created invoice correction ')
            return HttpResponseRedirect(request.get_full_path())
    if not form:
        form = InvoiceCorrectionAmountForm(
            initial={'_selected_action': request.POST.getlist(ACTION_CHECKBOX_NAME)}
        )
    opts = modeladmin.model._meta
    app_label = opts.app_label
    return render(
        request,
        'admin/invoice_correction_amount.html',
        {
            'queryset': queryset,
            'form': form,
            'opts': opts,
            'app_label': app_label,
        },
    )


def create_neutralization_correction_request_and_accept(
    modeladmin: 'ModelAdmin',
    request: 'HttpRequest',
    queryset: QuerySet['Invoice'],
    form: Type['InvoiceNeutralizationForm'],
    **kwargs: Any,
):
    tag = form.cleaned_data['tag']
    issued_at = form.cleaned_data['issued_at']

    for invoice in queryset:
        if invoice.pending_correction_requests.exists():
            modeladmin.message_user(
                request,
                f'{invoice} pending request already exists',
                level=messages.ERROR,
            )
            return HttpResponseRedirect(request.get_full_path())

        correction_request = create_neutralize_invoice_correction_request(
            invoice,
            request.user,
            correction_context='Admin action',
            tag=tag,
            issued_at=issued_at,
        )
        strategy = CorrectionRequestNeutralizationStrategy(correction_request)
        try:
            correction_invoice = strategy.accept(
                user=request.user,
                issued_at=correction_request.correction_invoice.issued_at,
            )
        except ValueError as e:
            modeladmin.message_user(request, e, level=messages.ERROR)
        else:
            if correction_invoice.order.earliest_invoice_domestic_version_supported():
                InvoiceDomestic.objects.create_domestic_from_correction(
                    correction_invoice
                )

        modeladmin.message_user(request, 'Created invoice correction')
    return HttpResponseRedirect(request.get_full_path())


@admin.action(description='Create invoice neutralization')
def create_invoice_correction_neutralization(
    modeladmin: 'ModelAdmin',
    request: 'HttpRequest',
    queryset: QuerySet['Invoice'],
):
    return admin_action_with_form(
        modeladmin=modeladmin,
        request=request,
        queryset=queryset,
        form_class=InvoiceNeutralizationForm,
        success_function=create_neutralization_correction_request_and_accept,
        success_function_kwargs={},
    )


@admin.action(description='Recalculate invoice items')
def recalculate_invoice_items(
    modeladmin: 'ModelAdmin',
    request: 'HttpRequest',
    queryset: QuerySet['Invoice'],
):
    for invoice in queryset:
        invoice.recreate_items()


def generate_sales_report(
    modeladmin: 'ModelAdmin',
    request: 'HttpRequest',
    queryset: QuerySet['Invoice'],
    form: Type['SalesReportForm'],
    **kwargs: Any,
):
    email = request.user.username
    invoice_filter_kwargs = {
        'status__in': (
            InvoiceStatus.CORRECTING,
            InvoiceStatus.ENABLED,
            InvoiceStatus.ENABLED_VAT_REGION_CORRECTION,
        )
    }
    for field_string in ('sell_at', 'issued_at'):
        start = getattr(form.cleaned_data[field_string], 'start', None)
        stop = getattr(form.cleaned_data[field_string], 'stop', None)
        if start:
            invoice_filter_kwargs[field_string + '__gte'] = start
        if stop:
            invoice_filter_kwargs[field_string + '__lte'] = stop
    generate_sales_report_and_send.delay(invoice_filter_kwargs, email)
    modeladmin.message_user(request, f'Email with report will be sent to {email}')
    return HttpResponseRedirect(request.get_full_path())


@admin.action
def accounting_sales_report(
    modeladmin: 'ModelAdmin',
    request: 'HttpRequest',
    queryset: QuerySet['Invoice'],
):
    return admin_action_with_form(
        modeladmin=modeladmin,
        request=request,
        queryset=queryset,
        form_class=SalesReportForm,
        success_function=generate_sales_report,
        success_function_kwargs={},
    )


def save_invoice_and_redirect(
    modeladmin: 'ModelAdmin',
    request: 'HttpRequest',
    queryset: QuerySet['Invoice'],
    form: Type['InvoiceCachedToDictForm'],
    object_id: int,
) -> 'HttpResponse':
    form.save()
    return redirect('admin:invoice_invoice_change', object_id=object_id)


class InvoiceAdminActionsMixin:
    @django_object_action(
        description='Edit PDF',
        label='Edit PDF',
        attrs={'style': '--object-tools-bg: orangered'},
    )
    @group_member_required(AdminGroup.ACCOUNTING)
    def edit_cached_to_dict_and_render_pdf(
        self,
        request: 'HttpRequest',
        obj: Invoice,
    ) -> 'HttpResponse':
        return admin_action_with_form(
            modeladmin=self,
            request=request,
            instance=obj,
            queryset=None,
            form_class=InvoiceCachedToDictForm,
            form_initial={
                'cached_to_dict': json.loads(obj.cached_to_dict),
            },
            success_function=save_invoice_and_redirect,
            success_function_kwargs={'object_id': obj.pk},
        )
