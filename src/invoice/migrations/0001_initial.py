# Generated by Django 1.11.24 on 2020-02-17 21:54
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('regions', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Invoice',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('pretty_id', models.CharField(blank=True, max_length=32)),
                ('receipt_id', models.CharField(blank=True, max_length=64)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                (
                    'pdf',
                    models.FileField(blank=True, null=True, upload_to='invoice_pdfs'),
                ),
                ('sent', models.BooleanField(default=False)),
                (
                    'status',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, 'Normal'),
                            (7, 'Normal - region after correction'),
                            (1, 'Disabled'),
                            (2, 'Pro forma'),
                            (3, 'Cancelled'),
                            (4, 'Correcting'),
                            (5, 'Blank'),
                            (6, 'External ES(sumup,cash)'),
                            (8, 'Correcting draft'),
                        ],
                        db_index=True,
                        default=0,
                    ),
                ),
                ('currency_symbol', models.CharField(default='€', max_length=10)),
                ('force_outside_eu', models.BooleanField(default=False)),
                ('issued_at', models.DateTimeField(blank=True, null=True)),
                ('corrected_issued_at', models.DateTimeField(blank=True, null=True)),
                ('sell_at', models.DateTimeField(blank=True, null=True)),
                ('corrected_sell_at', models.DateTimeField(blank=True, null=True)),
                ('due_date_at', models.DateTimeField(blank=True, null=True)),
                ('delivery_date_at', models.DateTimeField(blank=True, null=True)),
                ('receipt_date_at', models.DateTimeField(blank=True, null=True)),
                ('receipt_date_change', models.DateTimeField(blank=True, null=True)),
                ('sent_invoice_at', models.DateTimeField(blank=True, null=True)),
                ('payable_booking_date', models.DateTimeField(blank=True, null=True)),
                ('exchange_date', models.DateField(blank=True, null=True)),
                (
                    'exchange_rate',
                    models.DecimalField(
                        blank=True, decimal_places=4, max_digits=16, null=True
                    ),
                ),
                (
                    'vat_amount_in_pln',
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=16, null=True
                    ),
                ),
                ('additional_top', models.TextField(blank=True, null=True)),
                ('show_both_address', models.BooleanField(default=False)),
                ('additional_address_1', models.TextField(blank=True, null=True)),
                ('additional_address_2', models.TextField(blank=True, null=True)),
                ('use_polish_account_in_pln', models.BooleanField(default=False)),
                (
                    'additional_total_text',
                    models.CharField(blank=True, max_length=120, null=True),
                ),
                (
                    'additional_total_value',
                    models.CharField(blank=True, max_length=120, null=True),
                ),
                ('corrected_notes', models.TextField(blank=True, null=True)),
                (
                    'force_net',
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    'force_vat',
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    'force_gross',
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    'force_net_in_pln',
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    'force_vat_in_pln',
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    'force_gross_in_pln',
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    'force_exchange_rate',
                    models.DecimalField(
                        blank=True, decimal_places=4, max_digits=8, null=True
                    ),
                ),
                ('force_exchange_date', models.DateField(blank=True, null=True)),
                (
                    'exported_to_symfonia',
                    models.DateTimeField(blank=True, default=None, null=True),
                ),
                (
                    'exported_to_symfonia_f',
                    models.DateTimeField(blank=True, default=None, null=True),
                ),
            ],
        ),
        migrations.CreateModel(
            name='InvoiceCorrectionChange',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'correction_type',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (3, 'Customer VAT ID'),
                            (1, 'Exchange'),
                            (2, 'Exchange date'),
                            (0, 'Other'),
                            (4, 'Change issue date'),
                            (5, 'Change invoice number'),
                        ]
                    ),
                ),
                ('name', models.CharField(max_length=128)),
                ('previous_state', models.CharField(max_length=512)),
                ('current_state', models.CharField(max_length=512)),
            ],
        ),
        migrations.CreateModel(
            name='InvoiceHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('invoice_raw_id', models.IntegerField()),
                (
                    'generated_from_order',
                    django.contrib.postgres.fields.jsonb.JSONField(null=True),
                ),
                (
                    'invoice_history',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, null=True
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='InvoiceItem',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'item_type',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, 'Item'),
                            (1, 'Delivery'),
                            (2, 'Additional service'),
                        ]
                    ),
                ),
                ('item_name', models.CharField(max_length=128)),
                (
                    'item_material',
                    models.CharField(blank=True, max_length=128, null=True),
                ),
                (
                    'item_dimensions',
                    models.CharField(blank=True, max_length=128, null=True),
                ),
                ('quantity', models.PositiveSmallIntegerField()),
                (
                    'vat_rate',
                    models.DecimalField(decimal_places=2, default=0.23, max_digits=4),
                ),
                ('net_price', models.DecimalField(decimal_places=2, max_digits=16)),
                (
                    'discount_value',
                    models.DecimalField(decimal_places=2, default=0, max_digits=16),
                ),
                ('net_value', models.DecimalField(decimal_places=2, max_digits=16)),
                ('vat_amount', models.DecimalField(decimal_places=2, max_digits=16)),
                ('gross_price', models.DecimalField(decimal_places=2, max_digits=16)),
                (
                    'vat_status',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, 'Normal VAT'),
                            (1, 'VAT 0% - WDT'),
                            (2, 'Switzerland VAT'),
                        ],
                        default=0,
                    ),
                ),
                (
                    'net_weight',
                    models.DecimalField(decimal_places=2, default=0, max_digits=8),
                ),
                (
                    'gross_weight',
                    models.DecimalField(decimal_places=2, default=0, max_digits=8),
                ),
                (
                    'corrected_invoice_item',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='correcting_items',
                        to='invoice.InvoiceItem',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='InvoiceSequence',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'numeration_type',
                    models.IntegerField(choices=[(0, 'Normal'), (1, 'RV'), (2, 'VC')]),
                ),
                (
                    'invoice_type',
                    models.IntegerField(
                        choices=[
                            (0, 'Normal'),
                            (7, 'Normal - region after correction'),
                            (1, 'Disabled'),
                            (2, 'Pro forma'),
                            (3, 'Cancelled'),
                            (4, 'Correcting'),
                            (5, 'Blank'),
                            (6, 'External ES(sumup,cash)'),
                            (8, 'Correcting draft'),
                        ]
                    ),
                ),
                ('pretty_id_template', models.CharField(max_length=100)),
                (
                    'sequence_prefix',
                    models.TextField(blank=True, default=None, null=True),
                ),
                (
                    'country',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='regions.Country',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='SymfoniaExportLog',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'status',
                    models.IntegerField(
                        choices=[(10, 'Cofnięte'), (1, 'Ok')], default=1
                    ),
                ),
                ('exported_from_date', models.DateTimeField(null=True)),
                ('exported_to_date', models.DateTimeField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Symfonia export log',
                'verbose_name_plural': 'Symfonia export log',
            },
        ),
        migrations.CreateModel(
            name='SymfoniaFConfiguration',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('document_type', models.CharField(max_length=128)),
                ('account', models.CharField(max_length=128)),
                ('vat_account', models.CharField(max_length=128)),
                ('transaction_type', models.CharField(max_length=128)),
            ],
        ),
        migrations.CreateModel(
            name='SymfoniaImportLog',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('pdf_files_name', django.contrib.postgres.fields.jsonb.JSONField()),
                ('text_files_body', django.contrib.postgres.fields.jsonb.JSONField()),
                (
                    'status',
                    models.IntegerField(
                        choices=[(1, 'Init'), (2, 'Ok'), (3, 'Partial'), (100, 'Error')]
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Symfonia import log',
                'verbose_name_plural': 'Symfonia import log',
            },
        ),
        migrations.CreateModel(
            name='LDFInvoice',
            fields=[
                (
                    'invoice_ptr',
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to='invoice.Invoice',
                    ),
                ),
            ],
            bases=('invoice.invoice',),
        ),
        migrations.AddField(
            model_name='symfoniaimportlog',
            name='invoice',
            field=models.ManyToManyField(to='invoice.Invoice'),
        ),
        migrations.AddField(
            model_name='symfoniaimportlog',
            name='user',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name='symfoniaexportlog',
            name='exported_from',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='symfonia_export_from',
                to='invoice.Invoice',
            ),
        ),
        migrations.AddField(
            model_name='symfoniaexportlog',
            name='exported_to',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='symfonia_export_to',
                to='invoice.Invoice',
            ),
        ),
        migrations.AddField(
            model_name='symfoniaexportlog',
            name='user',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name='invoiceitem',
            name='invoice',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='invoice_items',
                to='invoice.Invoice',
            ),
        ),
        migrations.AddField(
            model_name='invoicecorrectionchange',
            name='correcting_invoice',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='correction_changes',
                to='invoice.Invoice',
            ),
        ),
        migrations.AddField(
            model_name='invoice',
            name='corrected_invoice',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='corrections',
                to='invoice.Invoice',
            ),
        ),
    ]
