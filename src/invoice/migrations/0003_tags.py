# Generated by Django 1.11.29 on 2020-12-03 09:44
from __future__ import unicode_literals

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0006_remove_folds'),
        ('invoice', '0002_dependant_foreign_keys'),
    ]

    operations = [
        migrations.AddField(
            model_name='invoiceitem',
            name='order_item',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='orders.OrderItem',
            ),
        ),
        migrations.AddField(
            model_name='invoiceitem',
            name='tag',
            field=models.IntegerField(
                blank=True,
                choices=[
                    (1, 'DISCOUNT QUALITY DISSATISFACTION'),
                    (2, 'DISCOUNT REFERRAL'),
                    (3, 'DISCOUNT PRICE ADJUSTMENT DUE TO ANY OTHER REASON'),
                    (4, 'CANCELLATION'),
                    (5, 'FREE RETURN'),
                    (6, 'CHANGE OF ADDRESS'),
                    (7, 'WRONG VAT'),
                    (8, 'PRICE INCREASE'),
                    (9, 'WRONG NAME'),
                    (10, 'OTHER INVOICE ADJUSTMENT'),
                    (11, 'ADDITIONAL SERVICE'),
                ],
                default=None,
                null=True,
            ),
        ),
    ]
