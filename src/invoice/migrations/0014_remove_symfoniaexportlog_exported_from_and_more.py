# Generated by Django 4.1.9 on 2023-09-07 14:39

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('invoice', '0013_invoiceitem_discount_tag'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='symfoniaexportlog',
            name='exported_from',
        ),
        migrations.RemoveField(
            model_name='symfoniaexportlog',
            name='exported_to',
        ),
        migrations.RemoveField(
            model_name='symfoniaexportlog',
            name='user',
        ),
        migrations.RemoveField(
            model_name='symfoniaimportlog',
            name='invoice',
        ),
        migrations.RemoveField(
            model_name='symfoniaimportlog',
            name='user',
        ),
        migrations.DeleteModel(
            name='LDFInvoice',
        ),
        migrations.DeleteModel(
            name='SymfoniaExportLog',
        ),
        migrations.DeleteModel(
            name='SymfoniaImportLog',
        ),
    ]
