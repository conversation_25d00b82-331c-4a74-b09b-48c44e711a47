# Generated by Django 4.1.9 on 2024-04-03 11:37

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('invoice', '0016_remove_invoice_exported_to_big_query'),
    ]

    operations = [
        migrations.DeleteModel(
            name='InvoiceItemForCashRegister',
        ),
        migrations.CreateModel(
            name='InvoiceDomestic',
            fields=[],
            options={
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('invoice.invoice',),
        ),
        migrations.AddField(
            model_name='invoice',
            name='is_domestic',
            field=models.BooleanField(default=False),
        ),
    ]
