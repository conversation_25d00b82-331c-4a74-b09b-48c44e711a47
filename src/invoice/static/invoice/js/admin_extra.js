(function ($) {
    $(document).ready(function(){
        $(".submit-row").append($('<input id="add_buttons" type="button" value="add recalculate fields"/>'));
        $("#add_buttons").click(function(){
                $(".field-vat_status").append(
                    $('<input class="recalculate_button_discount" type="button" value="discount from net"/>' +
                        '<input class="recalculate_button_from_gross" type="button" value="vat/net from gross"/>')
                );

                $(".recalculate_button_from_gross").click(function(){
                    var parent_invoice_item = $(this).parent().parent();
                    var net_price = parseFloat($(".field-net_price input", parent_invoice_item).val());
                    var vat_rate = parseFloat($(".field-vat_rate input", parent_invoice_item).val());
                    var gross_price = parseFloat($(".field-gross_price input", parent_invoice_item).val());
                    var quantity = parseFloat($(".field-quantity input", parent_invoice_item).val());
                    var new_vat_amount = vat_rate > 0 ? ((gross_price/(1+vat_rate)) * vat_rate).toFixed(2) : 0;
                    var new_net_value = (gross_price - parseFloat(new_vat_amount)).toFixed(2);
                    var new_discount_value = (parseFloat((net_price*quantity).toFixed(2)) - new_net_value).toFixed(2);

                    $(".field-net_value input", parent_invoice_item).val(new_net_value);
                    $(".field-vat_amount input", parent_invoice_item).val(new_vat_amount);
                    $(".field-discount_value input", parent_invoice_item).val(new_discount_value);
                });
                $(".recalculate_button_discount").click(function(){
                    var parent_invoice_item = $(this).parent().parent();
                    var net_price = parseFloat($(".field-net_price input", parent_invoice_item).val());
                    var net_value = parseFloat($(".field-net_value input", parent_invoice_item).val());
                    var quantity = parseFloat($(".field-quantity input", parent_invoice_item).val());
                    var discount_value = (parseFloat((net_price*quantity).toFixed(2)) - net_value).toFixed(2);

                    $(".field-discount_value input", parent_invoice_item).val(discount_value);
                });
            });

    })
})(django.jQuery);