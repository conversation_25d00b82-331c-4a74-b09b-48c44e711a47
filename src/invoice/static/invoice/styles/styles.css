* {
    box-sizing: border-box;
    -moz-box-sizing: border-box;
}

h5 {
    font-weight: normal;
    font-size: 14pt;
}

p {
    font-size: 12pt;
}

div {
    font-size: 10pt;
}

thead {
    font-size: 10pt;
}

tbody {
    font-size: 8pt;
}


h5, p {
    margin: 4pt 0;
}


body {
    margin: 0;
    margin: 52pt 41pt;

    font-family: 'LetteraTextPro';
}

table {
    width: 100%;
    border-collapse: collapse;
}


strong {
    color: #EC008B;
    font-style: normal;
    font-weight: normal;
}

em {
    font-style: normal;
    font-family: 'PxGrotesk';
    font-weight: lighter;
}

.inverted-fonts {
    font-family: 'PxGrotesk';
}

.inverted-fonts em {
    font-family: 'LetteraTextPro';
}

.nowrap {
    white-space: nowrap;
}

#main-details {
    margin-bottom: 41pt;
}

#main-details tr:first-child td {
    padding-bottom: 20pt;
}

#main-details tr:first-child td {
    vertical-align: top;
}

#main-details tr:last-child td {
    vertical-align: bottom;
}

#main-details td:last-child {
    text-align: right;
}

#company-details img {
    width: 56pt;
}

#company-details hr {
    display: block;
    width: 100pt;
    height: 1px;
    border: 0;
    background: #231F20;
    margin-left: auto;
    margin-right: 0;
}

#order-details article {
    border: 1px solid #E8E7E7;
    display: inline-block;
    text-align: left;
    padding: 10pt 20pt;
    width: 220pt;
}

#order-items-details td {
    padding: 10pt;
}

#order-items-details td:first-child {
    padding-left: 0pt;
}

#order-items-details td:last-child {
    padding-right: 0pt;
}

#order-items-details > thead > td {
}

#order-items-details > thead > tr > td,
#order-items-details > tbody > tr > td {

    border-bottom: 1px solid #231F20;
    text-align: center;
}

#order-items-details > thead > tr > td:first-child,
#order-items-details > tbody > tr > td:first-child {

    text-align: left;
}

#order-summary {
    float: right;
    margin-top: 41pt;
    width: 240pt;
}

#order-summary tfoot td {
    border-top: 1px solid #231F20;
}

#order-summary td:last-child {
    text-align: right;
}

.correction #order-summary  {
    float: right;
    margin-top: 41pt;
    width: 220pt;
}

.correction #order-summary td {
    text-align: right;
}
.correction #order-summary td:first-child {
    text-align: left;
}

.correction #order-changes-in-data {
    width: 100%;
    margin-bottom: 5%;
}
.correction #order-changes-in-data td {
    padding: 10pt;
}

.correction #order-changes-in-data td:first-child {
    padding-left: 0pt;
}

.correction #order-changes-in-data td:last-child {
    padding-right: 0pt;
}

.correction #order-changes-in-data > thead > td {
}

.correction #order-changes-in-data > thead > tr > td,
.correction #order-changes-in-data > tbody > tr > td {
    text-align: center;
}
.correction #order-changes-in-data > thead > tr > td:first-child,
.correction #order-changes-in-data > tbody > tr > td:first-child {

    text-align: left;
}

.correction table#order-changes-reason {
    width: 90%;
}

#vat-change-summary {
    float: left;
    border: 1px solid #E8E7E7;
    display: inline-block;
    text-align: center;
    margin-top: 41pt;
    width: 300pt;
    font-size: 7pt;
    /*padding: 10pt 20pt;*/
}
#vat-change-summary > tbody > tr > td {
    width: 80pt;
}

#vat-change-summary > thead > tr > td:first-child,
#vat-change-summary > tbody > tr > td:first-child {
    width: 90pt;
}

.no-page-break {
    page-break-inside: avoid;
}
