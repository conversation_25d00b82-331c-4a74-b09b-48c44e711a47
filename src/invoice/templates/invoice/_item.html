{% load invoice_tags %}
<tr {% if invoice_object.order.country == 'france' and invoice_item.recycle_tax_value %}class="no-border-bottom"{% endif %}>
    <td>
        {{ invoice_item.item_name }}
        {% if invoice_item.item_type == 0 %}
            {% if not is_complaint_proforma %}
                <br/>{{ invoice_item.item_material|default_if_none:"" }}
            {% endif %}
            <br/>{{ invoice_item.item_dimensions|default_if_none:"" }}
        {% endif %}
    </td>
    {% if invoice.delivery_address.country == 'united_kingdom' %}
        <td class="nowrap">
            <em>{{ invoice_item.hts_code }}</em>
        </td>
    {% endif %}
    <td class="nowrap">
        <em>{{ invoice_item.quantity }}</em>
    </td>
    <td class="nowrap">
        <em>{{ invoice_item.net_price }}{{ invoice.currency_symbol }}</em>
    </td>
    {% if invoice_object.get_promo_amount_number > 0 or invoice_object.is_correction %}
        <td class="nowrap">
            <em>{{ invoice_item.discount_value }}{{ invoice.currency_symbol }}</em>
        </td>
    {% endif %}
    <td class="nowrap">
        <em>{{ invoice_item.net_value }}{{ invoice.currency_symbol }}</em>
    </td>
    <td class="nowrap">
        <em>{% as_percentage invoice_item.vat_rate %}%</em>
    </td>
    <td class="nowrap">
        <em>{{ invoice_item.vat_amount }}{{ invoice.currency_symbol }}</em>
    </td>
    {% if invoice.delivery_address.outside_eu %}
        <td>{{ invoice_item.net_weight }} kg</td>
        <td>{{ invoice_item.gross_weight }} kg</td>
    {% endif %}
    <td class="nowrap">
        <em>{{ invoice_item.gross_price }}{{ invoice.currency_symbol }}</em>
    </td>
</tr>

{% if invoice_object.order.country == 'france' and invoice_item.recycle_tax_value %}
    <tr class="no-border-top">
        <td>Eco-fee included</td>
        <td class="nowrap"></td>
        <td class="nowrap"></td>
        {% if invoice_object.get_promo_amount_number > 0 or invoice_object.is_correction %}
            <td class="nowrap"></td>
        {% endif %}
        <td class="nowrap"></td>
        <td class="nowrap"></td>
        <td class="nowrap"></td>
        {% if invoice.delivery_address.outside_eu %}
            <td></td>
            <td></td>
        {% endif %}
        <td class="nowrap">
            <em>{{ invoice_item.recycle_tax_value }}{{ invoice.currency_symbol }}</em>
        </td>
    </tr>
{% endif %}
