from decimal import Decimal
from unittest.mock import patch

import pytest

from invoice.serializers import ForLogisticInvoiceItem


@pytest.mark.django_db
class TestForLogisticInvoiceItem:
    @pytest.fixture
    def invoice_item(
        self,
        invoice_item_factory,
        regions,
        invoice_factory,
        order_factory,
    ):
        order = order_factory(country='poland')
        invoice = invoice_factory(order=order)
        return invoice_item_factory(
            invoice=invoice,
            net_price=Decimal('100.00'),
            net_value=Decimal('100.00'),
            gross_price=Decimal('123.00'),
        )

    @pytest.fixture
    def cached_invoice_items(self, invoice_item):
        return [
            {
                'id': invoice_item.id,
                'net_price': Decimal('200.00'),
                'net_value': Decimal('200.00'),
                'gross_price': Decimal('246.00'),
            }
        ]

    @patch('orders.models.Order.is_to_be_shipped_complaint', return_value=False)
    def test_get_net_price_when_not_to_be_shipped_complaint(
        self,
        mock_is_to_be_shipped_complaint,
        invoice_item,
    ):
        serializer = ForLogisticInvoiceItem(
            invoice_item,
        )
        assert Decimal(serializer.data['net_price']) == invoice_item.net_price

    @patch('orders.models.Order.is_to_be_shipped_complaint', return_value=False)
    def test_get_net_value_when_not_to_be_shipped_complaint(
        self,
        mock_is_to_be_shipped_complaint,
        invoice_item,
    ):
        serializer = ForLogisticInvoiceItem(invoice_item)
        assert Decimal(serializer.data['net_value']) == invoice_item.net_value

    @patch('orders.models.Order.is_to_be_shipped_complaint', return_value=False)
    def test_get_gross_price_when_not_to_be_shipped_complaint(
        self,
        mock_is_to_be_shipped_complaint,
        invoice_item,
    ):
        serializer = ForLogisticInvoiceItem(invoice_item)
        assert Decimal(serializer.data['gross_price']) == invoice_item.gross_price

    @patch('orders.models.Order.is_to_be_shipped_complaint', return_value=True)
    @patch('invoice.models.InvoiceItem._get_cached_invoice_item')
    def test_get_net_price_when_to_be_shipped_complaint(
        self,
        mock_get_cached_invoice_item,
        mock_is_to_be_shipped_complaint,
        invoice_item,
        cached_invoice_items,
    ):
        mock_get_cached_invoice_item.return_value = cached_invoice_items[0]
        serializer = ForLogisticInvoiceItem(invoice_item)
        assert (
            Decimal(serializer.data['net_price'])
            == cached_invoice_items[0]['net_price']
        )

    @patch('orders.models.Order.is_to_be_shipped_complaint', return_value=True)
    @patch('invoice.models.InvoiceItem._get_cached_invoice_item')
    def test_get_net_value_when_to_be_shipped_complaint(
        self,
        mock_get_cached_invoice_item,
        mock_is_to_be_shipped_complaint,
        invoice_item,
        cached_invoice_items,
    ):
        mock_get_cached_invoice_item.return_value = cached_invoice_items[0]
        serializer = ForLogisticInvoiceItem(invoice_item)
        assert (
            Decimal(serializer.data['net_value'])
            == cached_invoice_items[0]['net_value']
        )

    @patch('orders.models.Order.is_to_be_shipped_complaint', return_value=True)
    @patch('invoice.models.InvoiceItem._get_cached_invoice_item')
    def test_get_gross_price_when_to_be_shipped_complaint(
        self,
        mock_get_cached_invoice_item,
        mock_is_to_be_shipped_complaint,
        invoice_item,
        cached_invoice_items,
    ):
        mock_get_cached_invoice_item.return_value = cached_invoice_items[0]
        serializer = ForLogisticInvoiceItem(invoice_item)
        assert (
            Decimal(serializer.data['gross_price'])
            == cached_invoice_items[0]['gross_price']
        )
