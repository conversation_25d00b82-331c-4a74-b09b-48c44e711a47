{% extends 'mails/_base_templates/base_flow_admin.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% endblock %}

{% block preheader %}
    {% blocktrans %}Sales report{% endblocktrans %}
{% endblock %}

{% block content %}
    <p style="margin: 30px 60px 0; font-size: 14px; color:#7c7d81; font-family: Helvetica, Arial, sans-serif; line-height: 24px;">
        Different amount of Furniture Order Items and Products
    </p>
    <table width="100%" style="font-size: 11px; width: 600px; background-color: #edf0f0; margin: 10px 60px 0;"
           cellpadding="0" cellspacing="0">
        <thead width="100%">
            <tr>
                <th style="min-width: 100px;">LogisticOrder</th>
                <th style="min-width: 100px;">Order</th>
                <th>Product</th>
            </tr>
        </thead>
        <tbody>
        {% for r in data %}
            <tr>
                <td style="border-top: 1px dotted; padding: 3px;">
                    LogisticOrder {{ r.order.logistic_info.last.id }}<a
                        href="https://{{ site }}/admin/logistic/logisticorder/?order_id={{ r.order.id }}"><img
                        src="https://tylko.com{% static 'mails/icon-external-link.png' %}"/></a>
                    </br>CS Panel<a href="https://{{ site }}/cs/user_overview/{{ r.order.owner.id }}/"><img
                        src="https://tylko.com{% static 'mails/icon-external-link.png' %}"/></a>
                </td>
                <td style="border-top: 1px dotted; padding: 3px;">
                    Order {{ r.order.id }}: {{ r.order.get_status_display }}<a
                        href="https://{{ site }}/admin/accounting/order_info/{{ r.order.id }}/"><img
                        src="https://tylko.com{% static 'mails/icon-external-link.png' %}"/></a>
                    </br>Items Count {{ r.items }}
                </td>
                <td style="border-top: 1px dotted; padding: 3px;">
                    {% for p in r.order.product_set.all %}
                        {% if p.status != 0 %}Product {{ p.id }}: {{ p.get_status_display }}
                            <a href="https://{{ site }}/admin/producers/product/{{ p.id }}/"><img
                                    src="https://tylko.com{% static 'mails/icon-external-link.png' %}"/></a><br/>
                        {% endif %}
                    {% endfor %}
                    Product Count: {{ r.production_items }}
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
{% endblock %}
