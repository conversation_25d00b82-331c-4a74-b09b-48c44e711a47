{% extends 'mails/_base_templates/base_flow.html' %}
{% load i18n static mailing_tags %}

{% block preheader %}
    {% trans 'mail_correction_after_free_return_normal_preheader' %}
{% endblock %}


{% block content %}
    {# Translation Keys Variables #}
    {% blocktrans  asvar header_1 with user=user%}mail_correction_after_free_return_normal_welcome_{{ user }}{% endblocktrans %}
    {% trans 'mail_transaction_new_invoice_attached_header_2_1' as header_2 %}

    {% include 'mails/_components/transaction-header.html' with utm='utm_campaign=transaction_correction_invoice' header=header_1 paragraph=header_2 %}

    {% mail_paragraph '' join='lower' margin_bottom=0 font_size=17 font_height=24 paragraph_color='#7c7d81' paragraph_align='left' extra_style='padding: 0 50px 0 0;' %}
        {% blocktrans with order=order order_items_id=order_items_id %}mail_correction_after_free_return_normal_paragraph_1_0_{{ order }}_{{ order_items_id }}{% endblocktrans %}
        <br><br>
        {% trans 'mail_correction_after_free_return_normal_paragraph_2_0' %}
        <br><br>
        {% trans 'mail_correction_after_free_return_normal_paragraph_3_0' %}
        <br><br>
        {% trans 'mail_correction_after_free_return_normal_signature' %}
    {% mail_paragraph '' join='upper' margin_bottom=0 %}

{% endblock %}

