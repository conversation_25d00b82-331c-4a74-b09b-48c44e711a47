{% extends 'mails/_base_templates/base_flow.html' %}
{% load i18n pf_i18n_tags defaulttags static galleries mailing_tags %}

{% block preheader %}
    {% trans 'mail_transaction_new_order_placed_preheader_1' %}
{% endblock %}

{% block content %}
    {# Translation Keys Variables #}
    {% trans 'mail_transaction_new_order_placed_header_1_1' as header_1 %}
    {% trans 'mail_transaction_new_order_placed_header_2_1' as header_2 %}
    {% trans 'mail_transaction_new_order_placed_header_3_2' as header_3_2 %}
    {% trans 'common_shipping_address' as shipping_address %}
    {% trans 'common_billing_address' as common_billing_address %}
    {% trans 'common_deliver_comments' as common_deliver_comments %}
    {% trans 'mail_transaction_new_order_placed_buton_1' as button %}

    {% include 'mails/_components/transaction-header.html' with utm='utm_campaign=transaction_order_placed' header=header_1 paragraph=header_2 %}

    {% mail_paragraph '' join='lower' margin_bottom=0 font_size=17 font_height=24 paragraph_color='#7c7d81' paragraph_align='left'%}
        {% trans 'mail_transaction_new_order_placed_header_3_1' %}
        {% if order.first_name %}{{ order.first_name }}{% elif order.owner.first_name %}{{ order.owner.first_name }}{% elif order.invoice_first_name %}{{ order.invoice_first_name }}{% elif order.invoice_company_name %}{{ order.invoice_company_name }}{% endif %},
        <br><br>
        {% trans 'mail_transaction_new_order_placed_text_1_1' %} {{ order.order_pretty_id }}{% trans 'mail_transaction_new_order_placed_text_1_2' %}
    {% mail_paragraph '' join='upper' %}

    {% mail_paragraph header_3_2 font_size=21 font_height=24 margin_bottom=10 paragraph_color='#000' paragraph_align='left' extra_style='padding-top: 30px;' %}
    {% mail_separator_line line_length='100%' line_height=2 %}

    {% for item in order.items.all %}
        {% mail_paragraph '' join='lower' font_size=15 font_height=20 paragraph_color='#7c7d81' paragraph_align='left'%}
            <table border="0" cellpadding="0" cellspacing="0" width="100%">
                <tr style="padding: 0 0 40px 0">
                    <td width="275" valign="top">
                        <img src="{{ item.order_item.preview | file_url }}" alt="" width="275" height="206">
                    </td>
                    <td width="285">
                        <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%">
                            <tr valign="top" width="285">
                                <td>
                                    {{ item.order_item.get_title }}
                                    <br>

                                    {% if item.order_item.custom_order %}
                                        {% item_custom_product_name item.order_item %}
                                    {% else %}
                                        {% for dimension in item.order_item.get_dimensions %}{{ dimension.label }}{{ dimension.value|floatformat }}cm, {% if not forloop.last %} {% endif %}{% endfor %}
                                        <br>
                                        {% include "tag_cart_table_materials.html" with material=item.order_item.get_material_description styled=False item=item only %}
                                    {% endif %}
                                </td>
                                <td style="float: right;">
                                    {{ item.get_price }}
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        {% mail_paragraph '' join='upper' %}
    {% endfor %}

    {% mail_paragraph '' join='lower' margin_bottom=20 font_size=15 font_height=20 paragraph_color='#7c7d81' paragraph_align='left'%}
        <table border="0" cellpadding="0" cellspacing="0" width="100%">
            <tr>
                <td style="width: 275px; font-size: 15px; line-height: 20px; padding: 0; margin: 0;">{% trans "common_items" %}</td>
                <td style="width: 285px; font-size: 15px; line-height: 20px; text-align: right; padding: 0; margin: 0;">{{ order.get_shelf_price }}</td>
            </tr>
            <tr>
                <td style="width: 275px; font-size: 15px; line-height: 20px; padding: 0; margin: 0;">{% trans "common_ship" %}</td>
                <td style="width: 285px; font-size: 15px; line-height: 20px; text-align: right; padding: 0; margin: 0;">{% trans "common_free" %}</td>
            </tr>
            {% if order.assembly %}
                <tr>
                    <td style="width: 275px; font-size: 15px; line-height: 20px; padding: 0; margin: 0;">{% trans "basic_pdp_product_specs_section_3_header_1" %}</td>
                    <td style="width: 285px; font-size: 15px; line-height: 20px; text-align: right; padding: 0; margin: 0;">{{ order.get_assembly_price_display }}</td>
                </tr>
            {% endif %}

            {%  if order.region_promo_amount > 0 %}
                <tr>
                    <td style="width: 275px; font-size: 15px; line-height: 20px; padding: 0; margin: 0;">{% trans "common_discount" %}</td>
                    <td style="width: 285px; font-size: 15px; line-height: 20px; text-align: right; padding: 0; margin: 0;">{{ order.get_promo_amount }}</td>
                </tr>
            {% endif %}
        </table>
    {% mail_paragraph '' join='upper' %}

    {% mail_paragraph '' join='lower' margin_bottom=20 font_size=15 font_height=20 paragraph_color='#000' paragraph_align='left'%}
        <table border="0" cellpadding="0" cellspacing="0" width="100%">
            <tr>
                <td width="275">{% trans "common_total_price" %}</td>
                <td width="285" style="text-align: right;">{{ order.get_total_price }}</td>
            </tr>
            <tr>
                <td width="275" style="margin-top:2px;">
                    {% if order.country == "switzerland" %}
                        {% trans "common_switzerland_info_1_1" %}
                    {% elif order.country == "norway" %}
                        {% trans "common_norway_info_1_1" %}
                    {% else %}
                        {% trans "common_vat_included" %}
                    {% endif %}
                </td>
            </tr>
        </table>
    {% mail_paragraph '' join='upper' %}


    <!-- address -->
    {% mail_paragraph shipping_address margin_bottom=10 font_size=21 font_height=24 paragraph_color='#000' paragraph_align='left' extra_style="margin-top: 65px;"%}

    {% mail_paragraph '' join='lower' margin_bottom=0 font_size=15 font_height=20 paragraph_color='#7c7d81' paragraph_align='left'%}
        {{ order.first_name }} {{ order.last_name }}
        <br>
        {{ order.street_address_1 }}
        {% if order.street_address_2 %}
           {{ order.street_address_2 }}
        {% endif %}

        <br>
        {{ order.postal_code }} {{ order.city }}
        <br>
        {% country_trans order.country %}
        <br>
        {{ order.email }}
        <br>
        {{ order.phone }}
        {% if order.company_name %}
            <br>{{ order.company_name }}
        {% endif %}
        {% if order.vat %}
            <br>{{ order.vat }}
        {% endif %}
    {% mail_paragraph '' join='upper'%}

    {% mail_separator_line line_length='100%' line_height=2 %}

    {% mail_paragraph common_billing_address margin_bottom=10 font_size=21 font_height=24 paragraph_color='#000' paragraph_align='left' extra_style="margin-top: 30px;"%}

    {% mail_paragraph '' join='lower' margin_bottom=0 font_size=15 font_height=20 paragraph_color='#7c7d81' paragraph_align='left'%}
        {% if order.is_different_address %}
            {{ order.invoice_first_name }} {{ order.invoice_last_name }}
            <br>
            {{ order.invoice_street_address_1 }}
            <br>
            {{ order.invoice_postal_code }} {{ order.invoice_city }}
            <br>
            {{ order.invoice_postal_code }} {{ order.invoice_city }}
            <br>
            {% country_trans order.invoice_country %}
            <br>
            {{ order.invoice_email }}
            <br>
            {{ order.invoice_phone }}
            <br>
            {{ order.invoice_company_name }}
            <br>
            {{ order.invoice_vat }}
        {% else %}
            {% trans "mail_transaction_order_placed_sameas" %}
        {% endif %}
    {% mail_paragraph '' join='upper' %}

    {% if order.notes %}

        {% mail_separator_line line_length='100%' line_height=2 %}
        {% mail_paragraph common_deliver_comments margin_bottom=10 font_size=21 font_height=24 paragraph_color='#000' paragraph_align='left'%}
        {% mail_paragraph '' join='lower' margin_bottom=0 font_size=15 font_height=20 paragraph_color='#7c7d81' paragraph_align='left'%}
            {{ order.notes }}
        {% mail_paragraph '' join='upper' %}
    {% endif %}

    {% mail_button button 'front-contact' order_status_page=True pk=order.id postal_code=order.sanitised_postal_code utm='utm_campaign=transaction_order_placed' login_access_token=login_access_token %}

    {% mail_paragraph '' join='lower' margin_bottom=0 font_size=17 font_height=24 paragraph_color='#7c7d81' paragraph_align='left'%}
        {% trans 'mail_transaction_new_order_placed_text_2_1' %}
        <br><br>
        {% trans 'mail_transaction_new_order_placed_text_3_1' %}
        <a href="tel:{% trans "mail_transaction_new_order_placed_buton_2" %}" style="color:#ff3c00; white-space: nowrap;" >{% trans "mail_transaction_new_order_placed_buton_2" %}</a>
        {% trans 'mail_transaction_new_order_placed_text_3_1_1' %} <a href="tel:{% trans "mail_transaction_new_order_placed_buton_3" %}" style="color:#ff3c00; white-space: nowrap;" >{% trans "mail_transaction_new_order_placed_buton_3" %}</a>
        {% trans 'mail_transaction_new_order_placed_text_3_2' %}
        <br><br>
        {% trans 'mail_transaction_new_order_placed_text_4_1' %}
    {% mail_paragraph '' join='upper' %}

{% endblock %}

{% block unsubscribe %}
{% endblock %}

