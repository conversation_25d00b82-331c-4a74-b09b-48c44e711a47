{% extends 'mails/_base_templates/base_flow.html' %}
{% load i18n static mailing_tags %}

{% block preheader %}
    {% if order.assembly %}
        {% trans 'mail_transaction_payment_confirmation_assembly_service_preheader' %}
    {% else %}
	    {% trans 'mail_transaction_new_payment_confirmation_preheader_1' %}
    {% endif %}
{% endblock %}

{% block content %}
    {# Translation Keys Variables #}
    {% trans 'mail_transaction_new_payment_confirmation_header_1_1' as header_1 %}
    {% trans 'mail_transaction_payment_confirmation_assembly_service_header_2_sample' as header_2 %}

    {% include 'mails/_components/transaction-header.html' with utm='utm_campaign=transaction_payment_authorised' header=header_1 paragraph=header_2 %}

    {% mail_paragraph '' join='lower' margin_bottom=0 font_size=17 font_height=24 paragraph_color='#7c7d81' paragraph_align='left' %}
        {% trans 'mail_transaction_new_payment_confirmation_header_3_1' %}
        {% if order.first_name %}{{ order.first_name }}{% elif order.owner.first_name %}{{ order.owner.first_name }}{% elif order.invoice_first_name %}{{ order.invoice_first_name }}{% elif order.invoice_company_name %}{{ order.invoice_company_name }}{% endif %},
        <br><br>

        {% blocktrans with order=order.order_pretty_id %}mail_transaction_new_payment_confirmation_text_1_with_{{ order }}{% endblocktrans %}
        {% blocktrans with date=delivery_date|date:"DATE_FORMAT" %}mail_transaction_new_payment_confirmation_sample_set_text_1_2_{{ date }}{% endblocktrans %} {% trans 'mail_transaction_new_payment_confirmation_sample_set_text_1_3' %}

    {% mail_paragraph '' join='upper' %}

    {% mail_paragraph '' join='lower' margin_bottom=0 font_size=17 font_height=24 paragraph_color='#7c7d81' paragraph_align='left' %}
        {% trans 'mail_transaction_new_payment_confirmation_text_5_1' %}
    {% mail_paragraph '' join='upper' %}

{% endblock %}
{% block unsubscribe %}
    {% include 'mails/_base_templates/_base_includes/_unsubscribe.html' %}
{% endblock %}
