{% extends 'mails/_base_templates/base_flow.html' %}
{% load i18n static mailing_tags %}

{% block preheader %}
    {% trans 'mail_transaction_proforma_invoice_preheader_1' %}
{% endblock %}

{% block content %}
    {# Translation Keys Variables #}
    {% trans 'mail_transaction_proforma_invoice_header_1_1' as header_1 %}
    {% trans 'mail_transaction_proforma_invoice_header_2_1' as header_2 %}

    {% include 'mails/_components/transaction-header.html' with utm='utm_campaign=transaction_payment_confirmation' header=header_1 paragraph=header_2 %}

    {% mail_paragraph '' join='lower' margin_bottom=0 font_size=17 font_height=24 paragraph_color='#7c7d81' paragraph_align='left' extra_style='padding: 0 50px 0 0;' %}
    {% trans 'mail_transaction_proforma_invoice_header_3_1' %}
    {% if order.first_name %}{{ order.first_name }}{% elif order.owner.first_name %}
        {{ order.owner.first_name }}{% elif order.invoice_first_name %}
        {{ order.invoice_first_name }}{% elif order.invoice_company_name %}{{ order.invoice_company_name }}{% endif %},
    <br><br>
    {% trans 'mail_transaction_proforma_invoice_text_1_1' %}
    <span style="white-space: nowrap">{{ order.order_pretty_id }} </span><br>
    {% trans 'mail_transaction_proforma_invoice_text_1_2' %}
    <br><br>
    {% trans 'mail_transaction_proforma_invoice_text_2_1' %}
    <br><br>
    {% trans 'mail_transaction_proforma_invoice_text_3_1' %}
    {% mail_paragraph '' join='upper' margin_bottom=0 %}

{% endblock %}
