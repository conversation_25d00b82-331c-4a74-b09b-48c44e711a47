import pytest

from mailing.enums import CustomerTypeChoices
from mailing.models import Customer
from orders.enums import OrderStatus


@pytest.mark.django_db(transaction=True)
class TestCustomer:
    def test_can_be_cs_customer_return_true_when_customer_exists(
        self, customer_factory, order_factory
    ):
        email = email_owner = '<EMAIL>'
        order = order_factory(email=email)
        customer = customer_factory(
            email=email,
            email_owner=email_owner,
            customer_type=CustomerTypeChoices.CS_CUSTOMER,
        )
        assert customer.can_be_cs_customer(order)

    def test_can_be_cs_customer_return_true_when_customer_has_at_least_two_orders(
        self,
        order_factory,
    ):
        email = '<EMAIL>'
        orders = order_factory.create_batch(
            size=3,
            email=email,
            status=OrderStatus.SHIPPED,
        )
        assert Customer.can_be_cs_customer(orders[0])

    def test_can_be_cs_customer_return_false_when_customer_has_zero_orders(
        self, order_factory
    ):
        email = '<EMAIL>'
        order = order_factory(email=email)
        can_be_cs_customer = Customer.can_be_cs_customer(order)
        assert not can_be_cs_customer
