# Generated by Django 4.1.13 on 2024-12-02 14:39

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('orders', '0056_order_completed_used_promos_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='orderitem',
            name='content_type',
            field=models.ForeignKey(
                limit_choices_to=models.Q(
                    models.Q(('app_label', 'gallery'), ('model', 'jetty')),
                    models.Q(('app_label', 'gallery'), ('model', 'samplebox')),
                    models.Q(('app_label', 'gallery'), ('model', 'watty')),
                    models.Q(('app_label', 'gallery'), ('model', 'sotty')),
                    _connector='OR',
                ),
                on_delete=django.db.models.deletion.CASCADE,
                to='contenttypes.contenttype',
            ),
        ),
    ]
