# Generated by Django 4.1.13 on 2025-02-06 14:53

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0058_orderitem_updated_at_orderitem_with_assembly_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrderToProductionInvalid',
            fields=[],
            options={
                'verbose_name_plural': 'Orders to Production Invalid',
                'ordering': ('-id',),
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('orders.ordertoproduction',),
        ),
        migrations.AddField(
            model_name='ordertoproduction',
            name='errors',
            field=models.JSONField(blank=True, default=list),
        ),
    ]
