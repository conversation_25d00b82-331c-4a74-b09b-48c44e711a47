{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_list producers_extra %}

{% block extrastyle %}
  {{ block.super }}
  <link rel="stylesheet" type="text/css" href="{% static "admin/css/changelists.css" %}" />
  {% if cl.formset %}
    <link rel="stylesheet" type="text/css" href="{% static "admin/css/forms.css" %}" />
  {% endif %}
  {% if cl.formset or action_form %}
    <script type="text/javascript" src="{% url 'admin:jsi18n' %}"></script>
  {% endif %}
  {{ media.css }}
  {% if not actions_on_top and not actions_on_bottom %}
    <style>
      #changelist table thead th:first-child {width: inherit}
    </style>
  {% endif %}
    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet" />
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css' %}" rel="stylesheet" type="text/css">
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    {{ media.js }}
    <script type="text/javascript" src="{% static 'js/bootstrap.js' %}"></script>
{% endblock %}

{% block bodyclass %}{{ block.super }} app-{{ opts.app_label }} model-{{ opts.model_name }} change-list{% endblock %}

{% if not is_popup %}
{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label=cl.opts.app_label %}">{{ cl.opts.app_config.verbose_name }}</a>
&rsaquo; {{ cl.opts.verbose_name_plural|capfirst }}
</div>
{% endblock %}
{% endif %}

{% block coltype %}flex{% endblock %}

{% block content %}
  <div class="module">
    <div class="col-lg-6 col-md6">
        <div class="panel panel-default">
            <div class="panel-heading">Voucher stats</div>
            <div class="table-responsive">
                <table class="table">
                    <thead><tr>
                        <th>Code</th>
                        <th>Type</th>
                        <th>Value</th>
                        <th>Origin</th>
                        <th>Times used</th>
                        <th>Total amount</th>
                        <th>Avg</th>
                        <th></th>
                    </tr></thead>
                    <tbody>{% for vc in vouchers_stats %}
                    <tr>
                        <td><a href="/admin/vouchers/voucher/{{ vc.main_voucher }}" target="_blank">{{ vc.main_voucher__code }}</a></td>
                        <td>{% if vc.main_voucher__kind_of == 0 %}Absolute{% else %}Percentage{% endif %}</td>
                        <td>{{ vc.main_voucher__value|floatformat:'-2' }}</td>
                        <td>{% if vc.main_voucher__origin == 0 %}Manual
                        {% elif vc.main_voucher__origin == 1 %}Invites
                        {% elif vc.main_voucher__origin == 2 %}Gift card
                        {% elif vc.main_voucher__origin == 3 %}Popup
                        {% elif vc.main_voucher__origin == 4 %}Discount
                        {% elif vc.main_voucher__origin == 5 %}Mailing
                        {% elif vc.main_voucher__origin == 6 %}Influencers{% endif %}</td>
                        <td>{{ vc.num_v }}</td>
                        <td>{{ vc.sum_v|floatformat:'-2' }}</td>
                        <td>{% widthratio vc.sum_v vc.num_v 1 %}</td>
                        <td><a href="/admin/vouchers/voucher/{{ vc.main_voucher }}" target="_blank"><i class="fa fa-arrow-circle-right"></i></a></td>
                    </tr>
                    {% endfor %}</tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md6">
        <div class="panel panel-default">
            <div class="panel-heading">Voucher origin stats</div>
            <div class="table-responsive">
                <table class="table">
                    <thead><tr>
                        <th>Origin</th>
                        <th>Times used</th>
                        <th>Total amount</th>
                        <th>Avg</th>
                    </tr></thead>
                    <tbody>{% for vc in voucher_origin_stats %}
                    <tr>
                        <td>{% if vc.main_voucher__origin == 0 %}Manual
                        {% elif vc.main_voucher__origin == 1 %}Invites
                        {% elif vc.main_voucher__origin == 2 %}Gift card
                        {% elif vc.main_voucher__origin == 3 %}Popup
                        {% elif vc.main_voucher__origin == 4 %}Discount
                        {% elif vc.main_voucher__origin == 5 %}Mailing
                        {% elif vc.main_voucher__origin == 6 %}Influencers{% endif %}</td>
                        <td>{{ vc.num_v }}</td>
                        <td>{{ vc.sum_v|floatformat:'-2' }}</td>
                        <td>{% widthratio vc.sum_v vc.num_v 1 %}</td>
                    </tr>
                    {% endfor %}</tbody>
                </table>
            </div>
        </div>
    </div>
<div class="clearfix"></div>
    <div class="col-lg-6 col-md6">
        <div class="panel panel-default">
            <div class="panel-heading">Voucher stats</div>
            <div class="table-responsive">
                <table class="table">
                    <thead><tr>
                        <th>Code</th>
                        <th>Type</th>
                        <th>Value</th>
                        <th>Origin</th>
                        <th>Times used</th>
                        <th>Total amount</th>
                        <th>Avg</th>
                        <th></th>
                    </tr></thead>
                    <tbody>{% for vc in vouchers_stats_3m %}
                    <tr>
                        <td><a href="/admin/vouchers/voucher/{{ vc.main_voucher }}" target="_blank">{{ vc.main_voucher__code }}</a></td>
                        <td>{% if vc.main_voucher__kind_of == 0 %}Absolute{% else %}Percentage{% endif %}</td>
                        <td>{{ vc.main_voucher__value|floatformat:'-2' }}</td>
                        <td>{% if vc.main_voucher__origin == 0 %}Manual
                        {% elif vc.main_voucher__origin == 1 %}Invites
                        {% elif vc.main_voucher__origin == 2 %}Gift card
                        {% elif vc.main_voucher__origin == 3 %}Popup
                        {% elif vc.main_voucher__origin == 4 %}Discount
                        {% elif vc.main_voucher__origin == 5 %}Mailing
                        {% elif vc.main_voucher__origin == 6 %}Influencers{% endif %}</td>
                        <td>{{ vc.num_v }}</td>
                        <td>{{ vc.sum_v|floatformat:'-2' }}</td>
                        <td>{% widthratio vc.sum_v vc.num_v 1 %}</td>
                        <td><a href="/admin/vouchers/voucher/{{ vc.main_voucher }}" target="_blank"><i class="fa fa-arrow-circle-right"></i></a></td>
                    </tr>
                    {% endfor %}</tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md6">
        <div class="panel panel-default">
            <div class="panel-heading">Voucher origin stats</div>
            <div class="table-responsive">
                <table class="table">
                    <thead><tr>
                        <th>Origin</th>
                        <th>Times used</th>
                        <th>Total amount</th>
                        <th>Avg</th>
                    </tr></thead>
                    <tbody>{% for vc in voucher_origin_stats_3m %}
                    <tr>
                        <td>{% if vc.main_voucher__origin == 0 %}Manual
                        {% elif vc.main_voucher__origin == 1 %}Invites
                        {% elif vc.main_voucher__origin == 2 %}Gift card
                        {% elif vc.main_voucher__origin == 3 %}Popup
                        {% elif vc.main_voucher__origin == 4 %}Discount
                        {% elif vc.main_voucher__origin == 5 %}Mailing
                        {% elif vc.main_voucher__origin == 6 %}Influencers{% endif %}</td>
                        <td>{{ vc.num_v }}</td>
                        <td>{{ vc.sum_v|floatformat:'-2' }}</td>
                        <td>{% widthratio vc.sum_v vc.num_v 1 %}</td>
                    </tr>
                    {% endfor %}</tbody>
                </table>
            </div>
        </div>
    </div>
<div class="clearfix"></div>
</div>
  <div id="content-main">
    {% block object-tools %}
        <ul class="object-tools">
          {% block object-tools-items %}
            {% if has_add_permission %}
            <li>
              {% url cl.opts|admin_urlname:'add' as add_url %}
              <a href="{% add_preserved_filters add_url is_popup to_field %}" class="addlink">
                {% blocktrans with cl.opts.verbose_name as name %}Add {{ name }}{% endblocktrans %}
              </a>
            </li>
            {% endif %}
          {% endblock %}
        </ul>
    {% endblock %}
    {% if cl.formset.errors %}
        <p class="errornote">
        {% if cl.formset.total_error_count == 1 %}{% trans "Please correct the error below." %}{% else %}{% trans "Please correct the errors below." %}{% endif %}
        </p>
        {{ cl.formset.non_form_errors }}
    {% endif %}
    <div class="module{% if cl.has_filters %} filtered{% endif %}" id="changelist">
      <div class="changelist-form-container">
        {% block search %}{% search_form cl %}{% endblock %}
        {% block date_hierarchy %}{% date_hierarchy cl %}{% endblock %}

          <form id="changelist-form" method="post"{% if cl.formset.is_multipart %} enctype="multipart/form-data"{% endif %} novalidate>{% csrf_token %}
          {% if cl.formset %}
            <div>{{ cl.formset.management_form }}</div>
          {% endif %}

          {% block result_list %}
              {% if action_form and actions_on_top and cl.show_admin_actions %}{% admin_actions %}{% endif %}
              {% result_list cl %}
              {% if action_form and actions_on_bottom and cl.show_admin_actions %}{% admin_actions %}{% endif %}
          {% endblock %}
          {% block pagination %}{% pagination cl %}{% endblock %}
          </form>
      </div>

      {% block filters %}
        {% if cl.has_filters %}
          <div id="changelist-filter">
            <h2>{% trans 'Filter' %}</h2>
            {% for spec in cl.filter_specs %}{% admin_list_filter cl spec %}{% endfor %}
          </div>
        {% endif %}
      {% endblock %}
    </div>
  </div>
{% endblock %}
