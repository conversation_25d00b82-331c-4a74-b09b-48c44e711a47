import pytest

from checkout.tests.conftest import (  # noqa
    klarna_correct_order,
    klarna_correct_transaction,
    mock_success_adyen_capture_url,
)


@pytest.fixture()
def order_with_products(order, order_item_factory, product_factory, manufactor_factory):
    manufactor_a = manufactor_factory(name='A')
    manufactor_b = manufactor_factory(name='B')

    order_item = order_item_factory(order=order)
    product_factory(manufactor=manufactor_a, order_item=order_item)
    product_factory(manufactor=manufactor_b, order_item=order_item)
    return order


@pytest.fixture()
def region_germany_with_rates(
    region_factory,
    currency_rate_factory,
    region_rate_factory,
):
    region = region_factory(germany=True, currency__rates=[])
    currency_rate_factory(currency=region.currency, rate=1.5)
    region_rate_factory(region=region, rate=2)
    return region


@pytest.fixture()
def region_germany_neutral(
    region_factory,
    currency_rate_factory,
    region_rate_factory,
):
    region = region_factory(germany=True, currency__rates=[])
    currency_rate_factory(currency=region.currency, rate=1.0)
    region_rate_factory(region=region, rate=1)
    return region


@pytest.fixture()
def region_united_kingdom_with_rates(
    region_factory,
    currency_rate_factory,
    region_rate_factory,
):
    region = region_factory(united_kingdom=True, currency__rates=[])
    currency_rate_factory(currency=region.currency, rate=1.5)
    region_rate_factory(region=region, rate=2)
    return region
