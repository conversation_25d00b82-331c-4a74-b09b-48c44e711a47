from datetime import timedelta

from django.utils import timezone

import pytest

from orders.enums import OrderStatus


@pytest.mark.django_db
def test_estimated_delivery_time(
    order_factory,
    order_item_factory,
    jetty,
    mocker,
):
    order = order_factory(
        items=None,
        status=OrderStatus.TO_BE_SHIPPED,
    )
    mocker.patch(
        'gallery.models.models.Jetty.get_delivery_time_days',
        return_value=10,
    )
    order_item_factory(
        order=order,
        order_item=jetty,
        is_jetty=True,
    )
    order_item_factory(
        order=order,
        is_sample_box=True,
    )
    expected_delivery_time = timezone.now() + timedelta(days=10)
    order_delivery_time = order.get_estimated_delivery_time()
    assert expected_delivery_time.date() == order_delivery_time.date()


@pytest.mark.django_db
def test_estimated_delivery_time__without_items(order_factory):
    order = order_factory(
        items=None,
        status=OrderStatus.TO_BE_SHIPPED,
    )
    assert order.get_estimated_delivery_time() is None
