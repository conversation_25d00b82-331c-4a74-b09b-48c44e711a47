import typing

from decimal import Decimal

from django.db.models import (
    F,
    Sum,
)

from orders.exceptions import (
    OrderRegionPriceDiffThanPriceForEuroOrCHF,
    OrderRegionPromoDiffThanSumOrderItemRegionPromo,
    OrderRegionTotalDiffThanItems,
)

if typing.TYPE_CHECKING:
    from orders.models import Order


def is_decimal_int(value: Decimal) -> bool:
    return value == value.to_integral_value()


class OrderToProductionValidator:
    def __init__(self, order: 'Order'):
        self.order = order

    def __call__(self):
        (
            is_valid,
            exception_message,
        ) = self.is_order_region_promo_eq_sum_items_region_promo()
        if not is_valid:
            raise OrderRegionPromoDiffThanSumOrderItemRegionPromo(exception_message)

        is_valid, exception_message = self.is_order_region_total_price_eq_items()
        if not is_valid:
            raise OrderRegionTotalDiffThanItems(exception_message)

        if self.order.currency and self.order.currency.code in ['EUR', 'CHF']:
            (
                is_valid,
                exception_message,
            ) = self.are_region_price_eq_price_when_euro_or_chf()
            if not is_valid:
                raise OrderRegionPriceDiffThanPriceForEuroOrCHF(exception_message)

    def is_order_region_promo_eq_sum_items_region_promo(self) -> (bool, str):
        region_promo_value_from_items = self.order.items.aggregate(
            region_promo_value_total=Sum(F('region_promo_value') * F('quantity'))
        )['region_promo_value_total']
        region_delivery_promo_value_from_items = self.order.items.aggregate(
            region_delivery_promo_value_total=Sum(
                F('region_delivery_promo_value') * F('quantity')
            )
        )['region_delivery_promo_value_total']
        items_and_delivery_promo = (
            region_promo_value_from_items + region_delivery_promo_value_from_items
        )

        is_valid = self.order.region_promo_amount == items_and_delivery_promo

        exception_message = ''
        if not is_valid:
            exception_message = (
                f'OrderID: {self.order.id}; '
                f'Order.region_promo_amount: {self.order.region_promo_amount}; '
                f'OrderItem[].region_promo_value: {items_and_delivery_promo}'
            )

        return is_valid, exception_message

    def is_order_region_total_price_eq_items(self) -> (bool, str):
        region_price_from_items = self.order.items.aggregate(
            region_price_total=Sum(F('region_price') * F('quantity'))
        )['region_price_total']

        region_delivery_price_from_items = self.order.items.aggregate(
            region_delivery_price_total=Sum(F('region_delivery_price') * F('quantity'))
        )['region_delivery_price_total']

        region_assembly_price_from_items = Decimal('0.0')
        if self.order.assembly:
            region_assembly_price_from_items = self.order.items.aggregate(
                region_assembly_price_total=Sum(
                    F('region_assembly_price') * F('quantity')
                )
            )['region_assembly_price_total']

        region_promo_value_from_items = self.order.items.aggregate(
            region_promo_value_total=Sum(F('region_promo_value') * F('quantity'))
        )['region_promo_value_total']
        region_delivery_promo_value_from_items = self.order.items.aggregate(
            region_delivery_promo_value_total=Sum(
                F('region_delivery_promo_value') * F('quantity')
            )
        )['region_delivery_promo_value_total']
        items_and_delivery_promo = (
            region_promo_value_from_items + region_delivery_promo_value_from_items
        )

        region_total_price_from_items = (
            region_price_from_items
            + region_assembly_price_from_items
            + region_delivery_price_from_items
            - items_and_delivery_promo
        )

        is_valid = self.order.region_total_price == region_total_price_from_items

        exception_message = ''
        if not is_valid:
            exception_message = (
                f'OrderID: {self.order.id}; '
                f'Order.region_total_price: {self.order.region_total_price}; '
                f'Region Total Price from Items: {region_total_price_from_items}'
            )

        return is_valid, exception_message

    def are_region_price_eq_price_when_euro_or_chf(self) -> (bool, str):
        exception_message = ''
        is_order_valid = all(
            [
                self.order.total_price == self.order.region_total_price,
                self.order.total_price_net == self.order.region_total_price_net,
                self.order.promo_amount == self.order.region_promo_amount,
                self.order.promo_amount_net == self.order.region_promo_amount_net,
            ]
        )
        if not is_order_valid:
            exception_message = (
                f'OrderID: {self.order.id}; '
                f'Order.region_total_price: {self.order.region_total_price}; '
                f'Order.total_price: {self.order.total_price}; '
                f'Order.region_total_price_net: {self.order.region_total_price_net}; '
                f'Order.total_price_net: {self.order.total_price_net}; '
                f'Order.region_promo_amount: {self.order.region_promo_amount}; '
                f'Order.promo_amount: {self.order.promo_amount}; '
                f'Order.region_promo_amount_net: {self.order.region_promo_amount_net}; '
                f'Order.promo_amount_net: {self.order.promo_amount_net};'
            )

        order_items_checks = []
        for order_item in self.order.items.all():
            order_items_checks.extend(
                [
                    order_item.price == order_item.region_price,
                    order_item.price_net == order_item.region_price_net,
                    order_item.assembly_price == order_item.region_assembly_price,
                ]
            )

        are_order_items_valid = all(order_items_checks)
        if not are_order_items_valid:
            exception_message += (
                f'OrderID: {self.order.id}; '
                f'There are differences at OrderItem level: '
                f'OrderItem[].region_price/price; '
                f'OrderItem[].region_price_net/price_net; '
                f'OrderItem[].region_assembly_price/assembly_price;'
            )
        is_valid = is_order_valid and are_order_items_valid

        return is_valid, exception_message
