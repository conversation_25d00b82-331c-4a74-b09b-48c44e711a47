from rest_framework.permissions import BasePermission

from payments.primer import PrimerClient


class PrimerWebhookPermission(BasePermission):
    """
    This permission is used to validate the signature of the webhook request
    """

    message = 'Invalid signature'

    def has_permission(self, request, view):
        return PrimerClient.validate_webhook_signature(
            request.data, request.headers['x-signature-primary']
        )
