from time import sleep

from django.conf import settings

from celery import shared_task
from celery.utils.log import get_task_logger

from checkout.klarna_capture import handle_capture_notification
from custom.metrics import task_metrics
from payments.models import (
    Notification,
    Transaction,
)

task_logger = get_task_logger('celery_task')


@shared_task
@task_metrics
def accept_notification(notification_data):
    if not settings.IS_TESTING:
        sleep(6)  # FIXME: WHY?

    notification = Notification()
    notification.amount_currency = notification_data.get('currency', '')
    notification.amount_value = notification_data.get('value', None)
    notification.event_date = notification_data.get('eventDate', None)
    notification.reason = notification_data.get('reason', '')
    notification.code = notification_data.get('eventCode', '')
    notification.live = notification_data.get('live', 'false') == 'true'
    notification.payment_method = notification_data.get('paymentMethod', '')
    notification.success = notification_data.get('success', 'false') == 'true'
    notification.operations = notification_data.get('operations', '')
    notification.psp_reference = notification_data.get('pspReference', '')
    notification.merchant_reference = notification_data.get('merchantReference', '')
    notification.merchant_account_code = notification_data.get(
        'merchantAccountCode',
        '',
    )
    notification.original_reference = notification_data.get('originalReference', '')

    if notification.code == 'CAPTURE':
        handle_capture_notification(notification)
    else:
        handle_transaction_notification(notification, notification_data)
    notification.save()


def handle_transaction_notification(notification, notification_data):
    try:
        transaction = Transaction.objects.get(reference=notification.psp_reference)
        notification.transaction = transaction
        transaction.update_after_notification(notification)
    except Transaction.DoesNotExist:
        try:
            transaction = Transaction.objects.get(
                merchant_reference=notification.merchant_reference
            )
            notification.transaction = transaction
            transaction.update_after_notification(notification)
        except Transaction.MultipleObjectsReturned:
            # ok, something went wrong, but maybe it can be fixed,
            # let's try to find empty transactions and put data there
            transaction_candidates = Transaction.objects.filter(
                merchant_reference=notification.merchant_reference
            )
            handle_multiple_transaction_candidates(
                transaction_candidates,
                notification,
                notification_data,
            )
        except Transaction.DoesNotExist:
            pass
    except Transaction.MultipleObjectsReturned:
        task_logger.error(
            'Multiple transactions found for pspReference=%s',
            notification.psp_reference,
        )
        transaction_candidates = Transaction.objects.filter(
            reference=notification.psp_reference
        )
        handle_multiple_transaction_candidates(
            transaction_candidates,
            notification,
            notification_data,
        )

    notification.save()


def handle_multiple_transaction_candidates(
    transaction_candidates,
    notification,
    notification_data,
):
    updated = False
    for transaction in transaction_candidates:
        notification.transaction = transaction
        updated = notification.transaction.update_after_notification(
            notification,
        )
        if updated:
            break
    if not updated:
        notification.transaction = transaction_candidates.first()
