from decimal import Decimal
from typing import Optional

elements_coefs = {
    # armrest
    'S01_AR_D07_W02_CORDUROY': Decimal('328'),
    'S01_AR_D07_W02_REWOOL': Decimal('440'),
    'S01_AR_D07_W02_REWOOL_FIREPROOF': Decimal('445'),
    'S01_AR_D07_W02_CORDUROY_COVER': <PERSON><PERSON><PERSON>('99'),
    'S01_AR_D07_W02_REWOOL_COVER': Decimal('199'),
    # chaise lounge
    'S01_CL_D13_W07_CORDUROY': Decimal('1178'),
    'S01_CL_D13_W07_REWOOL': Decimal('1565'),
    'S01_CL_D13_W07_REWOOL_FIREPROOF': Decimal('1604'),
    'S01_CL_D13_W07_CORDUROY_COVER': Decimal('199'),
    'S01_CL_D13_W07_REWOOL_COVER': Decimal('529'),
    'S01_CL_D13_W08_CORDUROY': Decimal('1288'),
    'S01_CL_D13_W08_REWOOL': Decimal('1699'),
    'S01_CL_D13_W08_REWOOL_FIREPROOF': Decimal('1744'),
    'S01_CL_D13_W08_CORDUROY_COVER': Decimal('199'),
    'S01_CL_D13_W08_REWOOL_COVER': Decimal('529'),
    'S01_CL_D13_W09_CORDUROY': Decimal('1392'),
    'S01_CL_D13_W09_REWOOL': Decimal('1841'),
    'S01_CL_D13_W09_REWOOL_FIREPROOF': Decimal('1890'),
    'S01_CL_D13_W09_CORDUROY_COVER': Decimal('199'),
    'S01_CL_D13_W09_REWOOL_COVER': Decimal('529'),
    # seater
    'S01_ST_D08_W06_CORDUROY': Decimal('803'),
    'S01_ST_D08_W06_REWOOL': Decimal('1080'),
    'S01_ST_D08_W06_REWOOL_FIREPROOF': Decimal('1115'),
    'S01_ST_D08_W06_CORDUROY_COVER': Decimal('149'),
    'S01_ST_D08_W06_REWOOL_COVER': Decimal('359'),
    'S01_ST_D08_W07_CORDUROY': Decimal('882'),
    'S01_ST_D08_W07_REWOOL': Decimal('1189'),
    'S01_ST_D08_W07_REWOOL_FIREPROOF': Decimal('1230'),
    'S01_ST_D08_W07_CORDUROY_COVER': Decimal('149'),
    'S01_ST_D08_W07_REWOOL_COVER': Decimal('359'),
    'S01_ST_D08_W08_CORDUROY': Decimal('962'),
    'S01_ST_D08_W08_REWOOL': Decimal('1303'),
    'S01_ST_D08_W08_REWOOL_FIREPROOF': Decimal('1349'),
    'S01_ST_D08_W08_CORDUROY_COVER': Decimal('149'),
    'S01_ST_D08_W08_REWOOL_COVER': Decimal('359'),
    'S01_ST_D08_W09_CORDUROY': Decimal('1046'),
    'S01_ST_D08_W09_REWOOL': Decimal('1422'),
    'S01_ST_D08_W09_REWOOL_FIREPROOF': Decimal('1473'),
    'S01_ST_D08_W09_CORDUROY_COVER': Decimal('149'),
    'S01_ST_D08_W09_REWOOL_COVER': Decimal('359'),
    'S01_ST_D09_W06_CORDUROY': Decimal('863'),
    'S01_ST_D09_W06_REWOOL': Decimal('1151'),
    'S01_ST_D09_W06_REWOOL_FIREPROOF': Decimal('1183'),
    'S01_ST_D09_W06_CORDUROY_COVER': Decimal('149'),
    'S01_ST_D09_W06_REWOOL_COVER': Decimal('359'),
    'S01_ST_D09_W07_CORDUROY': Decimal('938'),
    'S01_ST_D09_W07_REWOOL': Decimal('1250'),
    'S01_ST_D09_W07_REWOOL_FIREPROOF': Decimal('1297'),
    'S01_ST_D09_W07_CORDUROY_COVER': Decimal('149'),
    'S01_ST_D09_W07_REWOOL_COVER': Decimal('359'),
    'S01_ST_D09_W08_CORDUROY': Decimal('1025'),
    'S01_ST_D09_W08_REWOOL': Decimal('1374'),
    'S01_ST_D09_W08_REWOOL_FIREPROOF': Decimal('1419'),
    'S01_ST_D09_W08_CORDUROY_COVER': Decimal('149'),
    'S01_ST_D09_W08_REWOOL_COVER': Decimal('359'),
    'S01_ST_D09_W09_CORDUROY': Decimal('1119'),
    'S01_ST_D09_W09_REWOOL': Decimal('1533'),
    'S01_ST_D09_W09_REWOOL_FIREPROOF': Decimal('1584'),
    'S01_ST_D09_W09_CORDUROY_COVER': Decimal('149'),
    'S01_ST_D09_W09_REWOOL_COVER': Decimal('359'),
    # footrest
    'S01_FR_D05_W06_CORDUROY': Decimal('377'),
    'S01_FR_D05_W06_REWOOL': Decimal('477'),
    'S01_FR_D05_W06_REWOOL_FIREPROOF': Decimal('480'),
    'S01_FR_D05_W06_CORDUROY_COVER': Decimal('99'),
    'S01_FR_D05_W06_REWOOL_COVER': Decimal('199'),
    'S01_FR_D05_W07_CORDUROY': Decimal('413'),
    'S01_FR_D05_W07_REWOOL': Decimal('536'),
    'S01_FR_D05_W07_REWOOL_FIREPROOF': Decimal('541'),
    'S01_FR_D05_W07_CORDUROY_COVER': Decimal('99'),
    'S01_FR_D05_W07_REWOOL_COVER': Decimal('199'),
    'S01_FR_D05_W08_CORDUROY': Decimal('447'),
    'S01_FR_D05_W08_REWOOL': Decimal('582'),
    'S01_FR_D05_W08_REWOOL_FIREPROOF': Decimal('588'),
    'S01_FR_D05_W08_CORDUROY_COVER': Decimal('99'),
    'S01_FR_D05_W08_REWOOL_COVER': Decimal('199'),
    'S01_FR_D05_W09_CORDUROY': Decimal('482'),
    'S01_FR_D05_W09_REWOOL': Decimal('629'),
    'S01_FR_D05_W09_REWOOL_FIREPROOF': Decimal('633'),
    'S01_FR_D05_W09_CORDUROY_COVER': Decimal('99'),
    'S01_FR_D05_W09_REWOOL_COVER': Decimal('199'),
    'S01_FR_D06_W06_CORDUROY': Decimal('414'),
    'S01_FR_D06_W06_REWOOL': Decimal('522'),
    'S01_FR_D06_W06_REWOOL_FIREPROOF': Decimal('527'),
    'S01_FR_D06_W06_CORDUROY_COVER': Decimal('99'),
    'S01_FR_D06_W06_REWOOL_COVER': Decimal('199'),
    'S01_FR_D06_W07_CORDUROY': Decimal('464'),
    'S01_FR_D06_W07_REWOOL': Decimal('600'),
    'S01_FR_D06_W07_REWOOL_FIREPROOF': Decimal('607'),
    'S01_FR_D06_W07_CORDUROY_COVER': Decimal('99'),
    'S01_FR_D06_W07_REWOOL_COVER': Decimal('199'),
    'S01_FR_D06_W08_CORDUROY': Decimal('509'),
    'S01_FR_D06_W08_REWOOL': Decimal('678'),
    'S01_FR_D06_W08_REWOOL_FIREPROOF': Decimal('685'),
    'S01_FR_D06_W08_CORDUROY_COVER': Decimal('99'),
    'S01_FR_D06_W08_REWOOL_COVER': Decimal('199'),
    'S01_FR_D06_W09_CORDUROY': Decimal('577'),
    'S01_FR_D06_W09_REWOOL': Decimal('780'),
    'S01_FR_D06_W09_REWOOL_FIREPROOF': Decimal('788'),
    'S01_FR_D06_W09_CORDUROY_COVER': Decimal('99'),
    'S01_FR_D06_W09_REWOOL_COVER': Decimal('199'),
    'S01_FR_D07_W07_CORDUROY': Decimal('507'),
    'S01_FR_D07_W07_REWOOL': Decimal('668'),
    'S01_FR_D07_W07_REWOOL_FIREPROOF': Decimal('675'),
    'S01_FR_D07_W07_CORDUROY_COVER': Decimal('99'),
    'S01_FR_D07_W07_REWOOL_COVER': Decimal('199'),
    'S01_FR_D07_W08_CORDUROY': Decimal('564'),
    'S01_FR_D07_W08_REWOOL': Decimal('756'),
    'S01_FR_D07_W08_REWOOL_FIREPROOF': Decimal('763'),
    'S01_FR_D07_W08_CORDUROY_COVER': Decimal('99'),
    'S01_FR_D07_W08_REWOOL_COVER': Decimal('199'),
    'S01_FR_D07_W09_CORDUROY': Decimal('618'),
    'S01_FR_D07_W09_REWOOL': Decimal('844'),
    'S01_FR_D07_W09_REWOOL_FIREPROOF': Decimal('852'),
    'S01_FR_D07_W09_CORDUROY_COVER': Decimal('99'),
    'S01_FR_D07_W09_REWOOL_COVER': Decimal('199'),
    'S01_FR_D08_W08_CORDUROY': Decimal('616'),
    'S01_FR_D08_W08_REWOOL': Decimal('842'),
    'S01_FR_D08_W08_REWOOL_FIREPROOF': Decimal('851'),
    'S01_FR_D08_W08_CORDUROY_COVER': Decimal('99'),
    'S01_FR_D08_W08_REWOOL_COVER': Decimal('199'),
    'S01_FR_D08_W09_CORDUROY': Decimal('661'),
    'S01_FR_D08_W09_REWOOL': Decimal('893'),
    'S01_FR_D08_W09_REWOOL_FIREPROOF': Decimal('903'),
    'S01_FR_D08_W09_CORDUROY_COVER': Decimal('99'),
    'S01_FR_D08_W09_REWOOL_COVER': Decimal('199'),
    'S01_FR_D09_W09_CORDUROY': Decimal('719'),
    'S01_FR_D09_W09_REWOOL': Decimal('972'),
    'S01_FR_D09_W09_REWOOL_FIREPROOF': Decimal('983'),
    'S01_FR_D09_W09_CORDUROY_COVER': Decimal('99'),
    'S01_FR_D09_W09_REWOOL_COVER': Decimal('199'),
    # corner
    'S01_CN_D08_W08_CORDUROY': Decimal('1122'),
    'S01_CN_D08_W08_REWOOL': Decimal('1610'),
    'S01_CN_D08_W08_REWOOL_FIREPROOF': Decimal('1640'),
    'S01_CN_D08_W08_CORDUROY_COVER': Decimal('199'),
    'S01_CN_D08_W08_REWOOL_COVER': Decimal('529'),
    'S01_CN_D09_W09_CORDUROY': Decimal('1260'),
    'S01_CN_D09_W09_REWOOL': Decimal('1799'),
    'S01_CN_D09_W09_REWOOL_FIREPROOF': Decimal('1835'),
    'S01_CN_D09_W09_CORDUROY_COVER': Decimal('199'),
    'S01_CN_D09_W09_REWOOL_COVER': Decimal('529'),
}


def get_coefficients(
    region_name: Optional[str],
    overrides: dict = None,
) -> dict[str, Decimal]:
    coefficients = elements_coefs

    if overrides:
        coefficients.update(overrides)

    return coefficients
