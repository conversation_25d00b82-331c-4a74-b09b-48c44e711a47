import typing

from decimal import Decimal

from custom.enums import Axis

if typing.TYPE_CHECKING:
    from gallery.models import Watty

Element = dict[str, int]
ElementsList = list[Element]


def calculate_elements_price(
    watty: 'Watty',
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    return {
        'bars': calculate_bars_price(watty.bars, price_coefficients),
        'slabs': calculate_slabs_price(watty.slabs, price_coefficients),
        'drawers': calculate_watty_drawers_price(
            watty.drawers,
            price_coefficients,
        ),
        'walls': calculate_walls_price(watty.walls, price_coefficients),
        'extra_height': calculate_extra_height_price(
            watty.height,
            watty.width,
            price_coefficients,
        ),
        'lighting': calculate_lighting_price(
            watty.lighting,
            price_coefficients,
        ),
    }


def compute_length(element: Element, axis: Axis) -> Decimal:
    """Compute element length along an axis and convert it to meters."""
    return abs(Decimal(element[f'{axis}2']) - Decimal(element[f'{axis}1'])) / 1000


def sum_lengths(elements: ElementsList, axis: Axis) -> Decimal:
    return sum(compute_length(element, axis) for element in elements)


def calculate_bars_price(
    bars: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Bars (Watty element) price is based on their length."""
    regular_bars = [bar for bar in bars if bar.get('subtype', '') != 'z']
    cross_bars = [bar for bar in bars if bar.get('subtype', '') == 'z']
    regular_bar_length = sum_lengths(regular_bars, Axis.X)
    return (
        regular_bar_length * price_coefficients['bar_length']
        + price_coefficients['bar_unit'] * len(regular_bars)
        + price_coefficients['crossbar_unit'] * len(cross_bars)
    )


def calculate_slabs_price(
    slabs: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Slabs (Watty element) price is based on their length."""
    length = sum_lengths(slabs, Axis.X)
    length_price = length * price_coefficients['slab_length']
    unit_price = len(slabs) * price_coefficients['slab_unit']
    return length_price + unit_price


def calculate_watty_drawers_price(
    drawers: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Drawers in Watty have a standard height,
    so their price is based on their width.
    """
    internal_drawers = [drawer for drawer in drawers if drawer['subtype'] in {'i', 'b'}]
    external_drawers = [drawer for drawer in drawers if drawer['subtype'] == 'e']
    return _calculate_drawers_price(
        internal_drawers,
        price_coefficients['watty_drawer_length'],
        price_coefficients['watty_internal_drawer_unit'],
    ) + _calculate_drawers_price(
        external_drawers,
        price_coefficients['watty_drawer_length'],
        price_coefficients['watty_external_drawer_unit'],
    )


def _calculate_drawers_price(
    drawers: ElementsList,
    length_coefficient: Decimal,
    count_coefficient: Decimal,
):
    length = sum_lengths(drawers, Axis.X)
    length_price = length * length_coefficient
    unit_price = count_coefficient * len(drawers)
    return length_price + unit_price


def calculate_walls_price(
    walls: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Two outermost walls are included in the watty's frame (base price)."""
    number_of_inner_walls = len(set(wall['x1'] for wall in walls)) - 2
    return number_of_inner_walls * price_coefficients['wall_unit']


def calculate_extra_height_price(
    height: int,
    width: int,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Only for heights over 2.37m."""
    if height < 2370:
        return Decimal('0')
    if height == 2370:
        return _calculate_extra_height_price(
            height,
            width,
            price_coefficients['watty_additional_height_length'],
            price_coefficients['watty_additional_height_height'],
            price_coefficients['watty_additional_height_unit'],
        )
    return _calculate_extra_height_price(
        height,
        width,
        price_coefficients['watty_height+_length'],
        price_coefficients['watty_height+_height'],
        price_coefficients['watty_height+_unit'],
    )


def _calculate_extra_height_price(
    height: int,
    width: int,
    additional_width_coefficient: Decimal,
    additional_height_coefficient: Decimal,
    additional_height_count_coefficient: Decimal,
):
    width_m = Decimal(width) / 1000
    extra_height_m = Decimal(height - 2000) / 1000
    return (
        width_m * additional_width_coefficient
        + extra_height_m * additional_height_coefficient
        + additional_height_count_coefficient
    )


def get_number_of_modules_with_lighting(lighting):
    return len(set((light['x1'], light['x2']) for light in lighting))


def calculate_lighting_price(
    lighting: ElementsList,
    price_coefficients: dict[str, Decimal],
):
    number_of_modules_with_light = get_number_of_modules_with_lighting(lighting)
    modules_price = number_of_modules_with_light * price_coefficients['light_module']
    length = sum_lengths(lighting, Axis.X)
    length_price = length * price_coefficients['light_length']
    unit_price = len(lighting) * price_coefficients['light_unit']
    return modules_price + length_price + unit_price
