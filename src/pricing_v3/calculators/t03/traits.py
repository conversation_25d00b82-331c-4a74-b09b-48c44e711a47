import typing

from decimal import Decimal

if typing.TYPE_CHECKING:
    from gallery.models import <PERSON><PERSON>


def calculate_traits_price(
    watty: 'Watty',
    price_sum: Decimal,
    price_coefficients: typing.Dict[str, Decimal],
) -> typing.Dict[str, Decimal]:
    return {
        'category': calculate_category_price(
            price_sum,
            watty.furniture_category,
            price_coefficients,
        ),
    }


def calculate_category_price(
    price_sum: Decimal,
    category: str,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Price correction based on the shelf category."""
    return price_sum * price_coefficients[f'category_{category}_factor']
