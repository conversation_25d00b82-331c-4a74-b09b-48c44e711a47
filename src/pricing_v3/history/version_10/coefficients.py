from decimal import Decimal
from typing import Optional

from .price_buckets import BUCKET_ROUNDING_REGIONS

FACTOR_EURO = Decimal('4.3')

base_coefs = {
    'base': Decimal('73.17890333560'),
    'logistic_front_area': Decimal('131.03446552230'),
    'logistic_drawers': <PERSON><PERSON><PERSON>('24.55328836945'),
    'logistic_base': <PERSON><PERSON><PERSON>('49.83697595330'),
    'watty_base_width': Decima<PERSON>('877.39003'),
    'watty_base_unit': Decimal('1155.72886'),
    'type13_base_unit': Decimal('54.9059'),
    'type13_base_area': Decimal('16.1588'),
    'type13_veneer_base_area': Decimal('54.9059'),
    'type13_veneer_base_unit': Decimal('16.1588'),
}

shelves_coefs = {
    'horizontal_length': Decimal('42.1277105'),
    'horizontal_unit': Decimal('36.2353815'),
    'vertical_length': Decimal('40.5219990'),
    'vertical_unit': <PERSON><PERSON><PERSON>('16.3103315'),
    'support_length': Decimal('15.9445'),
    'support_unit': Decimal('11.8780950'),
    'backs_perimeter': Decimal('12.36870132205'),
    'backs_unit': Decimal('3.67375672420'),
    'doors_perimeter': Decimal('13.45778911230'),
    'doors_unit': Decimal('50.36443623655'),
    'drawers_perimeter': Decimal('38.00699500960'),
    'drawers_unit': Decimal('110.90280304525'),
    'long_leg_unit': Decimal('21.2649455'),
    'plinth_length': Decimal('53.6315000'),
    'insert_length': Decimal('23.1344660'),
    'insert_unit': Decimal('8.6249710'),
    'cable_management_unit': Decimal('21.4159165'),
    'bar_length': Decimal('17.9515'),
    'bar_unit': Decimal('10.813270'),
    'slab_length': Decimal('68.38295'),
    'slab_unit': Decimal('26.15790'),
    'desk_beam_length': Decimal('107.85258'),
    'desk_beam_unit': Decimal('40.28535'),
}

type03_coefs = {
    'watty_drawer_length': Decimal('85.02483'),
    'watty_internal_drawer_unit': Decimal('140.06377'),
    'watty_external_drawer_unit': Decimal('220.94377'),
    'wall_unit': Decimal('398.39349'),
    'watty_additional_height_unit': Decimal('115.3071348'),
    'watty_additional_height_height': Decimal('87.444588'),
    'watty_additional_height_length': Decimal('193.59175'),
    'watty_height+_length': Decimal('193.59175'),
    'watty_height+_height': Decimal('1093.05735'),
    'watty_height+_unit': Decimal('288.26784'),
    'watty_base_depth_factor': Decimal('1.15'),
    'watty_depth_400_factor': Decimal('0.5'),
    'watty_depth_800_factor': Decimal('0.01'),
    'duotone_factor': Decimal('0.2'),
    'light_module': Decimal('412.69'),
    'light_length': Decimal('89.88'),
    'light_unit': Decimal('31.41'),
    'crossbar_unit': Decimal('55.3800'),
}

type13_coefs = {
    'type13_wall_unit': Decimal('22.6979'),
    'type13_t01_wall_area': Decimal('253.87'),
    'type13_t02_wall_area': Decimal('73.93'),
    'type13_slab_unit': Decimal('19.3694'),
    'type13_slab_area': Decimal('78.9872'),
    'type13_door_unit': Decimal('48.0713'),
    'type13_door_perimeter': Decimal('20.1187'),
    'type13_backs_unit': Decimal('20.1504'),
    'type13_backs_area': Decimal('73.0940'),
    'type13_bar_unit': Decimal('13.1048'),
    'type13_bar_length': Decimal('30.1818'),
    'type13_crossbar_unit': Decimal('55.3800'),
    'type13_drawer_width': Decimal('69.7642'),
    'type13_internal_drawer_unit': Decimal('170.8268'),
    'type13_external_drawer_unit': Decimal('145.7164'),
    'type13_logs_vol': Decimal('85.0000'),
    'type13_logs_drawers': Decimal('0'),
    'type13_logs_base': Decimal('0'),
    'type13_cable_management_unit': Decimal('15'),
}


type13_veneer_coefs = {
    'type13_veneer_wall_unit': Decimal('22.6979'),
    'type13_veneer_t01_wall_area': Decimal('253.87'),
    'type13_veneer_t02_wall_area': Decimal('73.93'),
    'type13_veneer_slab_unit': Decimal('19.3694'),
    'type13_veneer_slab_area': Decimal('78.9872'),
    'type13_veneer_door_unit': Decimal('48.0713'),
    'type13_veneer_door_perimeter': Decimal('20.1187'),
    'type13_veneer_backs_unit': Decimal('20.1504'),
    'type13_veneer_backs_area': Decimal('73.0940'),
    'type13_veneer_bar_unit': Decimal('13.1048'),
    'type13_veneer_bar_length': Decimal('30.1818'),
    'type13_veneer_crossbar_unit': Decimal('55.3800'),
    'type13_veneer_drawer_width': Decimal('69.7642'),
    'type13_veneer_internal_drawer_unit': Decimal('170.8268'),
    'type13_veneer_external_drawer_unit': Decimal('145.7164'),
    'type13_veneer_logs_vol': Decimal('85.0000'),
    'type13_veneer_logs_drawers': Decimal('0'),
    'type13_veneer_logs_base': Decimal('0'),
    'type13_veneer_cable_management_unit': Decimal('16.61902'),
}

traits_coefs = {
    't02_factor': Decimal('-0.2961'),
    't01v_factor': Decimal('0.28415'),
    # raptor_factor works differently than other factors.
    # it describes a fake price increase on the raptor-specific fields.
    'raptor_factor': Decimal('2.0'),
    'depth_factor': Decimal('0.08541352'),
    'depth_factor_500': Decimal('0.23287721'),
    'category_bookcase_factor': Decimal('0.192464'),
    'category_chest_factor': Decimal('0.192464'),
    'category_shoerack_factor': Decimal('0.20393'),
    'category_sideboard_factor': Decimal('0.20393'),
    'category_tvstand_factor': Decimal('0.20393'),
    'category_vinyl_storage_factor': Decimal('0.201211'),
    'category_wallstorage_factor': Decimal('0.192464'),
    'category_wardrobe_factor': Decimal('0'),
    'category_bedside_table_factor': Decimal('0.20393'),
    'category_desk_factor': Decimal('0.23272'),
}

margin_coefs = {
    'jetty_margin_base': Decimal('1.39375'),
    'watty_margin_base': Decimal('1.02212'),
    # categories for Egde - values from previously used t13_margin_base, will be changed
    'type13_margin_wardrobe': Decimal('2.472'),
    'type13_margin_bookcase': Decimal('3.0028'),
    'type13_margin_wallstorage': Decimal('3.0028'),
    'type13_margin_chest': Decimal('2.7374'),
    # categories for Wood Egde
    'type13_veneer_margin_wardrobe': Decimal('2.472'),
    'type13_veneer_margin_bookcase': Decimal('3.0028'),
    'type13_veneer_margin_wallstorage': Decimal('3.0028'),
    'type13_veneer_margin_chest': Decimal('2.7374'),
    # increase margins
    'minimal_front_area_for_increase': Decimal('0.36795'),
    'maximal_front_area_for_increase': Decimal('4.0140'),
    'maximal_increase': Decimal('0.50175'),
}

material_coefs = {
    't01_group_1': Decimal('0.0174'),
    't01_group_2': Decimal('0.0584'),
    't02_group_1': Decimal('0.0298'),
    't02_group_2': Decimal('0.0537'),
    't02_group_3': Decimal('0.0971'),
    't02_group_4': Decimal('0.3425'),
}

vbp_coefs = {
    'width_300': Decimal('0.2230'),
    'height_250': Decimal('0.1115'),
    'depth_400': Decimal('0.05575'),
    'depth_500': Decimal('0.10363581'),
}

regional_coefs = {
    'switzerland': Decimal('307.5'),
    'united_kingdom': Decimal('307.5'),
}

additional_factors = {
    'type_03_additional_increase': Decimal('0.3901'),
    'type_13_additional_increase': Decimal('0.0352'),
    'type_13_veneer_additional_increase': Decimal('0.3975'),
    'type_01_additional_increase': Decimal('0.133'),
    'type_02_additional_increase': Decimal('0.0815'),
    'type_01v_additional_increase': Decimal('0.133'),
}

RAPTOR_FACTORABLE_COEFFICIENTS = {
    'long_leg_unit',
    'plinth_length',
    'insert_length',
    'insert_unit',
    'cable_management_unit',
    'raptor_factor',
}


def get_coefficients(
    region_name: Optional[str],
    overrides: dict = None,
) -> dict[str, Decimal]:
    defaults = get_default_coefficients(region_name)
    coefficients = defaults['coefficients']
    regional_increase = defaults['regional_increase']

    if overrides:
        coefficients.update(overrides)

    coefficients['regional_increase'] = regional_increase
    coefficients['bucket_rounding_enabled'] = region_name in BUCKET_ROUNDING_REGIONS
    return coefficients


def get_default_coefficients(region_name: Optional[str] = None) -> dict:
    coefficients = {
        **base_coefs,
        **shelves_coefs,
        **traits_coefs,
        **margin_coefs,
        **material_coefs,
        **vbp_coefs,
        **type03_coefs,
        **type13_coefs,
        **type13_veneer_coefs,
        **additional_factors,
        'bucket_rounding_enabled': BUCKET_ROUNDING_REGIONS,
    }

    try:
        regional_increase = regional_coefs[region_name]
    except (AttributeError, KeyError):
        regional_increase = Decimal('0.0')

    return {
        'coefficients': coefficients,
        'regional_increase': regional_increase,
    }
