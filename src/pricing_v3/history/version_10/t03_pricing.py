import typing

from decimal import Decimal

from django.conf import settings

from . import (
    base_prices,
    coefficients,
    elements,
    margins,
    traits,
)

if typing.TYPE_CHECKING:
    from gallery.models import <PERSON>y


def calculate_elements_price(
    watty: 'Watty',
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    return {
        'bars': elements.calculate_bars_price(watty.bars, price_coefficients),
        'slabs': elements.calculate_slabs_price(watty.slabs, price_coefficients),
        'drawers': elements.calculate_watty_drawers_price(
            watty.drawers,
            price_coefficients,
        ),
        'walls': elements.calculate_walls_price(watty.walls, price_coefficients),
        'extra_height': elements.calculate_extra_height_price(
            watty.height,
            watty.width,
            price_coefficients,
        ),
        'lighting': elements.calculate_lighting_price(
            watty.lighting,
            price_coefficients,
        ),
    }


def calculate_margins(
    watty: 'Watty',
    price_sum: Decimal,
    sum_without_light: Decimal,
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    depth_price = traits.calculate_watty_depth_price(
        sum_without_light,
        watty.depth,
        price_coefficients,
    )
    material_price = traits.calculate_watty_material_price(
        sum_without_light,
        watty.color,
        price_coefficients,
    )
    return {
        'margin_base': margins.calculate_watty_base_margin(
            price_sum + depth_price + material_price,
            price_coefficients,
        ),
    }


def calculate_traits_price(
    watty: 'Watty',
    price_sum: Decimal,
    price_coefficients: typing.Dict[str, Decimal],
) -> typing.Dict[str, Decimal]:
    return {
        'category': traits.calculate_category_price(
            price_sum,
            watty.furniture_category,
            price_coefficients,
        ),
    }


def prepare_price(watty: 'Watty', region_name: str, pricing_version=None) -> dict:
    if pricing_version is None:
        price_coefficients = coefficients.get_coefficients(region_name)
    else:
        price_coefficients = pricing_version.coefficients
    base_watty_price = base_prices.calculate_base_watty_price(watty, price_coefficients)
    watty_price = sum(base_watty_price.values())

    elements_price = calculate_elements_price(watty, price_coefficients)
    watty_price += sum(elements_price.values())

    traits_price = calculate_traits_price(
        watty=watty,
        price_sum=watty_price,
        price_coefficients=price_coefficients,
    )
    watty_price += sum(traits_price.values())

    margins_price = calculate_margins(
        watty=watty,
        price_sum=watty_price,
        sum_without_light=watty_price - elements_price['lighting'],
        price_coefficients=price_coefficients,
    )
    watty_price += sum(margins_price.values())

    logistic_price = base_prices.calculate_watty_logistics_price(
        watty,
        price_coefficients,
    )
    watty_price += sum(logistic_price.values())

    regional_increase = base_prices.calculate_watty_regional_price_increase(
        price=watty_price,
        price_coefficients=price_coefficients,
    )

    watty_price += sum(regional_increase.values())

    return {
        'watty_price_gross': watty_price * Decimal(settings.POLISH_VAT_FACTOR),
        **base_watty_price,
        **elements_price,
        **traits_price,
        **margins_price,
        **logistic_price,
    }


def get_pricing_dict(watty: 'Watty', region_name: str, in_pln: bool) -> dict:
    pricing_dict = prepare_price(watty=watty, region_name=region_name)

    price_in_pln = pricing_dict['watty_price_gross']
    if in_pln:
        final_price = price_in_pln
    else:
        final_price = int(price_in_pln / coefficients.FACTOR_EURO)
    pricing_dict['total_rounded_gross_regional'] = final_price
    return pricing_dict


def calculate_price(watty: 'Watty', region_name: str, pricing_version=None) -> int:
    pricing_dict = prepare_price(watty, region_name, pricing_version)
    euro_price_gross = pricing_dict['watty_price_gross'] / coefficients.FACTOR_EURO
    return int(euro_price_gross)
