import typing

from decimal import Decimal

if typing.TYPE_CHECKING:
    from gallery.models import <PERSON><PERSON>


def calculate_base_price(
    watty: 'Watty',
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    """COGS for less customer-valued but important elements, like frame."""
    width_m = Decimal(watty.width) / 1000
    cost_base = (
        price_coefficients['watty_base_width'] * width_m
        + price_coefficients['watty_base_unit']
    )
    return {'base': cost_base}


def calculate_regional_price_increase(
    price: Decimal,
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    regional_increase = price_coefficients['type_03_additional_increase'] * price
    regional_increase += price_coefficients['regional_increase']
    return {'regional_increase': regional_increase}
