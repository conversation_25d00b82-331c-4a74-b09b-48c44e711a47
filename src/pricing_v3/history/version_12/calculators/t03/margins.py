import typing

from decimal import Decimal

from custom.enums import ColorEnum

if typing.TYPE_CHECKING:
    from gallery.models import <PERSON><PERSON>


def calculate_margins(
    watty: 'Watty',
    price_sum: Decimal,
    sum_without_light: Decimal,
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    depth_price = calculate_watty_depth_price(
        sum_without_light,
        watty.depth,
        price_coefficients,
    )
    material_price = calculate_watty_material_price(
        sum_without_light,
        watty.color,
        price_coefficients,
    )
    return {
        'margin_base': calculate_watty_base_margin(
            price_sum + depth_price + material_price,
            price_coefficients,
        ),
    }


def calculate_watty_material_price(
    price_sum: Decimal,
    color: ColorEnum,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    if color.is_multicolor:
        return price_sum * price_coefficients['duotone_factor']
    return Decimal('0')


def calculate_watty_depth_price(
    price_sum: Decimal,
    depth: int,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    depth_m = Decimal(depth) / Decimal(1000)
    first_standard_depth = Decimal('0.53')
    second_standard_depth = Decimal('0.63')
    base_depth_price = (depth_m - first_standard_depth) * price_coefficients[
        'watty_base_depth_factor'
    ]
    if depth_m < first_standard_depth:
        depth_price = (
            base_depth_price
            + (Decimal('0.52') - depth_m) * price_coefficients['watty_depth_400_factor']
        )
    elif depth_m <= second_standard_depth:
        depth_price = base_depth_price
    else:
        depth_price = base_depth_price + (
            (depth_m - second_standard_depth)
            * price_coefficients['watty_depth_800_factor']
        )
    return depth_price * price_sum


def calculate_watty_base_margin(
    price_sum: Decimal, price_coefficients: dict[str, Decimal]
) -> Decimal:
    return price_sum * price_coefficients['watty_margin_base']
