import typing

from decimal import Decimal

from custom.enums import ShelfType

if typing.TYPE_CHECKING:
    from gallery.models import Jetty


def calculate_traits_price(
    jetty: 'Jetty',
    price_sum: Decimal,
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    return {
        'shelf_type': _calculate_shelf_type_price(
            price_sum,
            jetty.shelf_type,
            price_coefficients,
        ),
        'depth': _calculate_depth_price(
            price_sum,
            jetty.depth,
            price_coefficients,
        ),
        'category': _calculate_category_price(
            price_sum,
            jetty.furniture_category,
            price_coefficients,
        ),
    }


def _calculate_shelf_type_price(
    price_sum: Decimal,
    shelf_type: ShelfType,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Price correction based on the shelf type.
    Always 0 for Type01 (the default type).
    Always a negative value for Type02 (cheaper than T01).
    Always a positive value for Type01<PERSON>eneer (more expensive than T01).
    """
    if shelf_type == ShelfType.TYPE02:
        return price_sum * price_coefficients['t02_factor']
    if shelf_type == ShelfType.VENEER_TYPE01:
        return price_sum * price_coefficients['t01v_factor']
    return Decimal('0')


def _calculate_depth_price(
    price_sum: Decimal, depth: int, price_coefficients: dict[str, Decimal]
) -> Decimal:
    """Additional price increase for shelves by depth.

    We assume anything between 320 and 400 is depth == 400.
    We assume anything larger than 400 is depth == 500.
    """
    if depth > 320 and depth <= 400:
        return price_coefficients['depth_factor'] * price_sum
    if depth > 400:
        return price_coefficients['depth_factor_500'] * price_sum
    return Decimal('0')


def _calculate_category_price(
    price_sum: Decimal,
    category: str,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Price correction based on the shelf category."""
    return price_sum * price_coefficients[f'category_{category}_factor']
