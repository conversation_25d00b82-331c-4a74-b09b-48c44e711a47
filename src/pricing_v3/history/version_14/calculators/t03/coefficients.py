from decimal import Decimal
from typing import Optional

base_coefs = {
    'watty_base_width': Decimal('877.39003'),
    'watty_base_unit': Dec<PERSON><PERSON>('1155.72886'),
}
type03_coefs = {
    'bar_length': Decimal('17.9515'),
    'bar_unit': Decimal('10.813270'),
    'slab_length': <PERSON><PERSON><PERSON>('68.38295'),
    'slab_unit': <PERSON><PERSON><PERSON>('26.15790'),
    'watty_drawer_length': <PERSON><PERSON><PERSON>('85.02483'),
    'watty_internal_drawer_unit': Decimal('140.06377'),
    'watty_external_drawer_unit': <PERSON>ima<PERSON>('220.94377'),
    'wall_unit': Decimal('398.39349'),
    'watty_additional_height_unit': Decimal('115.3071348'),
    'watty_additional_height_height': Decimal('87.444588'),
    'watty_additional_height_length': <PERSON>ima<PERSON>('193.59175'),
    'watty_height+_length': Decimal('193.59175'),
    'watty_height+_height': <PERSON><PERSON><PERSON>('1093.05735'),
    'watty_height+_unit': <PERSON><PERSON><PERSON>('288.26784'),
    'watty_base_depth_factor': Decimal('1.15'),
    'watty_depth_400_factor': Decimal('0.5'),
    'watty_depth_800_factor': Decimal('0.01'),
    'duotone_factor': Decimal('0.2'),
    'light_module': Decimal('412.69'),
    'light_length': Decimal('89.88'),
    'light_unit': Decimal('31.41'),
    'crossbar_unit': Decimal('55.3800'),
    'category_wardrobe_factor': Decimal('0'),
}


margin_coefs = {
    'watty_margin_base': Decimal('1.02212'),
}

regional_coefs = {
    'switzerland': Decimal('307.5'),
    'united_kingdom': Decimal('307.5'),
}

additional_factors = {
    'type_03_additional_increase': Decimal('0.3901'),
}


def get_coefficients(
    region_name: Optional[str],
    overrides: dict = None,
) -> dict[str, Decimal]:
    defaults = get_default_coefficients(region_name)
    coefficients = defaults['coefficients']
    regional_increase = defaults['regional_increase']

    if overrides:
        coefficients.update(overrides)

    coefficients['regional_increase'] = regional_increase
    return coefficients


def get_default_coefficients(region_name: Optional[str] = None) -> dict:
    coefficients = {
        **base_coefs,
        **margin_coefs,
        **type03_coefs,
        **additional_factors,
    }

    try:
        regional_increase = regional_coefs[region_name]
    except (AttributeError, KeyError):
        regional_increase = Decimal('0.0')

    return {
        'coefficients': coefficients,
        'regional_increase': regional_increase,
    }
