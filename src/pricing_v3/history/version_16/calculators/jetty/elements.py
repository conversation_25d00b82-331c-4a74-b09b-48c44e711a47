import typing

from collections import defaultdict
from decimal import Decimal

from custom.enums import Axis

if typing.TYPE_CHECKING:
    from gallery.models import Jetty


Element = dict[str, int]
ElementsList = list[Element]

MAX_DOOR_HEIGHT_FOR_TREX = Decimal('0.382')


def calculate_elements_price(
    jetty: 'Jetty',
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    return {
        'horizontals': _calculate_horizontals_price(
            jetty.horizontals,
            price_coefficients,
        ),
        'verticals': _calculate_verticals_price(
            jetty.verticals,
            price_coefficients,
        ),
        'supports': _calculate_supports_price(
            jetty.supports,
            jetty.backs,
            price_coefficients,
        ),
        'backs': _calculate_backs_price(jetty.backs, price_coefficients),
        'doors': _calculate_doors_price(jetty.doors, price_coefficients),
        'drawers': _calculate_jetty_drawers_price(jetty.drawers, price_coefficients),
        'long_legs': _calculate_long_legs_price(
            jetty.long_legs,
            price_coefficients,
        ),
        'plinth': _calculate_plinth_price(
            jetty.plinth,
            jetty.width,
            price_coefficients,
        ),
        'inserts': _calculate_inserts_price(jetty.inserts, price_coefficients),
        'cable_management': _calculate_cable_managements_price(
            jetty.cable_management,
            price_coefficients,
        ),
        'desk_beams': _calculate_deskbeam_price(
            jetty.desk_beams,
            price_coefficients,
        ),
    }


def _compute_length(element: Element, axis: Axis) -> Decimal:
    """Compute element length along an axis and convert it to meters."""
    return abs(Decimal(element[f'{axis}2']) - Decimal(element[f'{axis}1'])) / 1000


def _calculate_area(elements: ElementsList, axis1: Axis, axis2: Axis) -> Decimal:
    return sum(
        _compute_length(element, axis1) * _compute_length(element, axis2)
        for element in elements
    )


def _sum_lengths(elements: ElementsList, axis: Axis) -> Decimal:
    return sum(_compute_length(element, axis) for element in elements)


def _remove_supports_overlapping_with_backs(supports, backs):
    """In case when backs and supports take up the same space,
    only backs will be produced.
    So let's remove the supports.
    """
    backs_by_minimal_y = defaultdict(list)
    for back in backs:
        back_y_min = min(back['y1'], back['y2'])
        backs_by_minimal_y[back_y_min].append(back)

    new_supports = []
    for support in supports:
        support_y_min = min(support['y1'], support['y2'])

        found_overlapping_back = False
        for back in backs_by_minimal_y[support_y_min]:
            back_x_min = min(back['x1'], back['x2'])
            back_x_max = max(back['x1'], back['x2'])
            if (
                back_x_min <= support['x1'] <= back_x_max
                and back_x_min <= support['x2'] <= back_x_max
            ):
                found_overlapping_back = True
                break
        if not found_overlapping_back:
            new_supports.append(support)

    return new_supports


def _calculate_horizontals_price(
    horizontals: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Horizontals price is based only on their width (assume depth==320)."""
    count, length_sum = get_horizontals_count_and_length_sum(horizontals)
    return (
        length_sum * price_coefficients['horizontal_length']
        + count * price_coefficients['horizontal_unit']
    )


def get_horizontals_count_and_length_sum(horizontals: list) -> tuple[int, Decimal]:
    count = len(horizontals)
    length_sum = Decimal('0')
    for horizontal in horizontals:
        horizontal_length = _compute_length(horizontal, Axis.X)
        if horizontal_length >= Decimal('2.3'):
            # add "fake joint" - increase the price gradually, so when the actual joints
            # appear (at width=2.4m) we won't have a sudden increase in price
            # simplest way to do it is to introduce a part of a horizontal,
            # so now if the horizontal is 2.35m long, it'll count as 1.5 horizontals.
            count += (horizontal_length - Decimal('2.3')) / Decimal('0.1')
        length_sum += horizontal_length
    return count, length_sum


def _calculate_deskbeam_price(
    desk_beams: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Desk beam aluminum profile price is based on it's size"""
    if not desk_beams:
        return Decimal()
    length = _sum_lengths(desk_beams, axis=Axis.X)
    return (
        length * price_coefficients['desk_beam_length']
        + price_coefficients['desk_beam_unit']
    )


def _calculate_verticals_price(
    verticals: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Verticals price is based only on their height (assume depth==320)."""
    length = _sum_lengths(verticals, axis=Axis.Y)
    count = len(verticals)
    return (
        length * price_coefficients['vertical_length']
        + count * price_coefficients['vertical_unit']
    )


def _calculate_supports_price(
    supports: ElementsList,
    backs: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Supports price is based only on their height (constant width==125).
    Sometimes the supports are overlapping with backs (i.e. backs were enabled
    but supports weren't removed from the Jetty even though they won't be produced.
    So we need to clean them first - remove the supports that won't be produced.
    """
    supports = _remove_supports_overlapping_with_backs(supports, backs)
    length = _sum_lengths(supports, axis=Axis.Y)
    count = len(supports)
    return (
        length * price_coefficients['support_length']
        + count * price_coefficients['support_unit']
    )


def _calculate_backs_price(
    backs: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Backs price is based on both width and height."""
    price = Decimal(0)
    for back in backs:
        width = _compute_length(back, axis=Axis.X)
        height = _compute_length(back, axis=Axis.Y)
        perim = 2 * (width + height)
        price += (
            perim * price_coefficients['backs_perimeter']
            + price_coefficients['backs_unit']
        )
    return price


def _calculate_doors_price(
    doors: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Doors price is based on both width and height."""
    price = Decimal(0)
    for door in doors:
        width = _compute_length(door, axis=Axis.X)
        height = _compute_length(door, axis=Axis.Y)
        perim = 2 * (width + height)
        door_price = (
            perim * price_coefficients['doors_perimeter']
            + price_coefficients['doors_unit']
        )
        if height > MAX_DOOR_HEIGHT_FOR_TREX:
            door_price *= price_coefficients['raptor_factor']
        price += door_price
    return price


def _calculate_jetty_drawers_price(
    drawers: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Drawers price is based on the width and height of their front
    (assume depth==320).
    """
    price = Decimal(0)
    for drawer in drawers:
        width = _compute_length(drawer, axis=Axis.X)
        height = _compute_length(drawer, axis=Axis.Y)
        front_perim = 2 * (width + height)
        price += (
            front_perim * price_coefficients['drawers_perimeter']
            + price_coefficients['drawers_unit']
        )
    return price


def _calculate_long_legs_price(
    long_legs: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Long legs price is constant."""
    return (
        len(long_legs)
        * price_coefficients['long_leg_unit']
        * price_coefficients['raptor_factor']
    )


def _calculate_plinth_price(
    plinth: ElementsList,
    jetty_width: int,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Plinth price is based on the Jetty width (assume depth==320)."""
    if plinth:
        width_in_m = Decimal(jetty_width / 1000)
        return (
            width_in_m
            * price_coefficients['plinth_length']
            * price_coefficients['raptor_factor']
        )
    return Decimal(0)


def _calculate_inserts_price(
    inserts: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Inserts price is based on their width
    (horizontal inserts, subtype ``h`` or ``t``)
    or height (vertical inserts, subtype ``v``) (assume depth==320).
    """
    price = Decimal(0)
    for insert in inserts:
        if insert['subtype'] in {'h', 't'}:
            length = _compute_length(insert, Axis.X)
        else:
            length = _compute_length(insert, Axis.Y)
        price += (
            length * price_coefficients['insert_length']
            + price_coefficients['insert_unit']
        )
    return price * price_coefficients['raptor_factor']


def _calculate_cable_managements_price(
    cable_managements: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Cable management price is constant."""
    return (
        len(cable_managements)
        * price_coefficients['cable_management_unit']
        * price_coefficients['raptor_factor']
    )
