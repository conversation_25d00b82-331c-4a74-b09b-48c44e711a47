from decimal import Decimal
from typing import Optional

base_coefs = {
    't23_base_unit': Decimal('41.3206'),
    't23_base_area': Decimal('6.9274'),
}


element_coefs = {
    't23_wall_unit': Decimal('36.08'),
    't23_wall_area': Decimal('129.1596'),
    't23_slab_unit': <PERSON><PERSON><PERSON>('24.3695'),
    't23_slab_area': <PERSON><PERSON><PERSON>('107.7191'),
    't23_door_unit': <PERSON><PERSON>l('52.2804'),
    't23_door_perimeter': Decimal('42.2355'),
    't23_backs_unit': Decimal('29.016'),
    't23_backs_area': <PERSON>ima<PERSON>('72.883'),
    't23_drawer_width': <PERSON><PERSON><PERSON>('102.2505'),
    't23_drawer_unit': Decimal('279.5522'),
    't23_logs_vol': <PERSON><PERSON><PERSON>('538.1784'),
    't23_logs_drawers': Decimal('15.00'),
    't23_logs_base': Decimal('20.00'),
    't23_cable_management_unit': Decimal('25.5068'),
    't23_kielbasa_legs_length': Decimal('0'),
    't23_kielbasa_legs_unit': Decimal('0'),
}


margin_coefs = {
    't23_margin_sideboard': Decimal('0'),
    't23_margin_tvstand': Decimal('0'),
    't23_margin_chest': Decimal('0'),
    't23_margin_bedside_table': Decimal('0'),
    't23_margin_wardrobe': Decimal('0'),
}

additional_factors = {
    't23_additional_increase': Decimal('1.8417'),
}


def get_coefficients(
    region_name: Optional[str],
    overrides: dict = None,
) -> dict[str, Decimal]:
    defaults = get_default_coefficients(region_name)
    coefficients = defaults['coefficients']

    if overrides:
        coefficients.update(overrides)

    return coefficients


def get_default_coefficients(region_name: Optional[str] = None) -> dict:
    coefficients = {
        **base_coefs,
        **margin_coefs,
        **element_coefs,
        **additional_factors,
    }

    return {
        'coefficients': coefficients,
    }
