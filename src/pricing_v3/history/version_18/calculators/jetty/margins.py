import typing

from decimal import Decimal

if typing.TYPE_CHECKING:
    from gallery.models import Jetty


def calculate_margins(
    jetty: 'Jetty',
    price_sum: Decimal,
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    return {
        'margin_base': _calculate_base_margin(price_sum, price_coefficients),
        'margin_additional': _calculate_additional_small_furniture_margin(
            jetty.width,
            jetty.height,
            price_coefficients,
        ),
    }


def _calculate_base_margin(
    price_sum: Decimal, price_coefficients: dict[str, Decimal]
) -> Decimal:
    return price_sum * price_coefficients['jetty_margin_base']


def _calculate_additional_small_furniture_margin(
    width: int, height: int, price_coefficients: dict[str, Decimal]
) -> Decimal:
    """Aka. marza_x. Higher margins for small furniture, as producing them is a bother.
    The margin increase is linear with respect to the front area.
    """
    area_m = Decimal(width * height / 1000 / 1000)

    min_area = price_coefficients['minimal_front_area_for_increase']
    max_area = price_coefficients['maximal_front_area_for_increase']

    area_ratio = (max_area - area_m) / (max_area - min_area)
    area_ratio_normalized = max(min(area_ratio, 1), 0)
    return area_ratio_normalized * price_coefficients['maximal_increase']
