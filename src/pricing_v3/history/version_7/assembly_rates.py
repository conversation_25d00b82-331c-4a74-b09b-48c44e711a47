from decimal import Decimal

from custom.enums import ShelfType

ASSEMBLY_RATES = {
    'switzerland': [
        0.9579,
        0.6386,
        0.4790,
        0.3832,
        0.3193,
        0.2737,
        0.2395,
        0.2129,
        0.1916,
        0.1782,
        0.1670,
        0.1575,
        0.1494,
        0.1521,
        0.1444,
        0.1442,
        0.1440,
        0.1439,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1409,
        0.1412,
        0.1314,
        0.1295,
        0.1289,
        0.1285,
        0.1281,
        0.1278,
        0.1275,
        0.1273,
        0.1271,
        0.1270,
        0.1268,
        0.1267,
        0.1266,
        0.1265,
    ],
    'france': [
        1.5703,
        1.0469,
        0.7852,
        0.6281,
        0.5234,
        0.4487,
        0.3926,
        0.349,
        0.3141,
        0.29,
        0.27,
        0.253,
        0.2385,
        0.2259,
        0.2149,
        0.2122,
        0.2099,
        0.2078,
        0.2026,
        0.201,
        0.1996,
        0.1983,
        0.1971,
        0.1960,
        0.195,
        0.1941,
        0.1932,
        0.1924,
        0.1916,
        0.1909,
        0.1902,
        0.1896,
        0.1890,
        0.1885,
        0.1879,
        0.1875,
        0.187,
        0.1865,
        0.1861,
        0.1857,
        0.1853,
        0.1850,
        0.1846,
        0.1843,
        0.1840,
        0.1837,
        0.1834,
        0.1831,
        0.1828,
        0.1826,
        0.1823,
        0.1821,
        0.1818,
        0.1816,
        0.1814,
        0.1812,
        0.181,
        0.1808,
        0.1806,
        0.1804,
        0.1803,
        0.1801,
        0.1799,
        0.1798,
        0.1796,
        0.1795,
        0.1793,
        0.1792,
        0.179,
        0.1789,
        0.1788,
        0.1787,
        0.1785,
        0.1784,
        0.1783,
        0.1782,
        0.1781,
        0.178,
        0.1779,
        0.1778,
        0.1777,
        0.1776,
        0.1775,
        0.1774,
        0.1773,
        0.1772,
        0.1771,
        0.177,
        0.1769,
        0.1769,
        0.1768,
        0.1767,
        0.1766,
        0.1766,
        0.1765,
        0.1764,
        0.1763,
        0.1763,
        0.1762,
        0.2026,
        0.1861,
        0.1806,
        0.1779,
        0.1762,
        0.1751,
        0.1552,
        0.1546,
        0.1541,
        0.1538,
        0.1535,
        0.1532,
        0.153,
        0.1528,
        0.1527,
        0.1525,
        0.1524,
        0.1523,
        0.1522,
        0.1521,
    ],
    'default': [
        1.23,
        0.8200,
        0.6150,
        0.4920,
        0.4100,
        0.3514,
        0.3075,
        0.2733,
        0.2460,
        0.2281,
        0.2132,
        0.2006,
        0.1898,
        0.1804,
        0.1722,
        0.1650,
        0.1585,
        0.1528,
    ],
}

'''
Linear coefficients have been calculated based on thousands shelves sold
in the specific time (6-months) in specific country to calculate assembly service
Basing on price instead of weight:
    as_price = shelf weight * as price for 1kg netto
is equivalent to:
    shelf_coefficients = ASSEMBLY_LINEAR_COEFFICIENTS[country][shelf_type]
    as_price = shelf_coefficients[0] * price + shelf_coefficients[1]

We don't have historical data needed for calculating linear regression for T13.
T13 are made of pure particle board or from particle board with plywood walls, but by
now, for simplicity T13 assembly service prices are the same as T02.
'''
ASSEMBLY_LINEAR_COEFFICIENTS = {
    # 16k shelves sold in the UK in Jan - May 2022
    # as price 1kg netto = 1.33 euro
    'united_kingdom': {
        ShelfType.TYPE01: (Decimal('0.07142663'), Decimal('16.61230207')),
        ShelfType.TYPE02: (Decimal('0.08560065'), Decimal('17.51190952')),
        ShelfType.VENEER_TYPE01: (Decimal('0.07593903'), Decimal('-8.27191237')),
        ShelfType.TYPE13: (Decimal('0.08560065'), Decimal('17.51190952')),
    },
    # 7k shelves sold in the DK in Feb - Aug 2022
    # as_price 1kg netto = 1 euro
    'denmark': {
        ShelfType.TYPE01: (Decimal('0.05177466'), Decimal('9.57821638')),
        ShelfType.TYPE02: (Decimal('0.06825348'), Decimal('1.91434229')),
        ShelfType.VENEER_TYPE01: (Decimal('0.05079478'), Decimal('0.26448909')),
        ShelfType.TYPE13: (Decimal('0.06825348'), Decimal('1.91434229')),
    },
    # 1.3k shelves sold in the PL ever
    # as_price 1kg netto = 0.82
    'poland': {
        ShelfType.TYPE01: (Decimal('0.04245522'), Decimal('7.85413743')),
        ShelfType.TYPE02: (Decimal('0.05596785'), Decimal('1.56976068')),
        ShelfType.VENEER_TYPE01: (Decimal('0.04165172'), Decimal('0.21688105')),
        ShelfType.TYPE13: (Decimal('0.05596785'), Decimal('1.56976068')),
    },
}
