from django.db import migrations

from pricing_v3.calculators.coefficients import get_coefficients


def update_pricing_version(apps, schema_editor):
    PricingVersion = apps.get_model('pricing_v3', 'PricingVersion')
    Region = apps.get_model('regions', 'Region')
    for region in Region.objects.filter(
        name__in={
            'switzerland',
            'germany',
            'united_kingdom',
        }
    ):
        PricingVersion(coefficients=get_coefficients(region), region=region).save()
    PricingVersion(coefficients=get_coefficients(region_name=None)).save()


class Migration(migrations.Migration):

    dependencies = [
        ('pricing_v3', '0003_update_chest_bedside_table_prices'),
    ]

    operations = [
        migrations.RunPython(
            update_pricing_version,
            migrations.RunPython.noop,
            elidable=True,
        ),
    ]
