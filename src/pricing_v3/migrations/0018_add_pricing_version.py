from datetime import datetime

from django.db import migrations

from pricing_v3.calculators.coefficients import get_coefficients

REGIONS = {'switzerland', 'germany', 'united_kingdom'}
CURRENT_VERSION = datetime(2024, 6, 17, 8, 0)


def create_current_pricing_version(apps, schema_editor):
    PricingVersion = apps.get_model('pricing_v3', 'PricingVersion')
    Region = apps.get_model('regions', 'Region')
    PricingHistoryEntry = apps.get_model('pricing_v3', 'PricingHistoryEntry')
    for region in Region.objects.filter(name__in=REGIONS):
        pv = PricingVersion.objects.create(
            coefficients=get_coefficients(region.name), region=region
        )
        pv.created_at = CURRENT_VERSION
        pv.save()
    pv = PricingVersion.objects.create(coefficients=get_coefficients(region_name=None))
    pv.created_at = CURRENT_VERSION
    pv.save()
    PricingHistoryEntry.objects.create(
        start_date=CURRENT_VERSION,
        end_date=None,
        calculator_version='current',
    )


def delete_current_pricing_history(apps, schema_editor):
    PricingHistoryEntry = apps.get_model('pricing_v3', 'PricingHistoryEntry')
    try:
        PricingHistoryEntry.objects.get(start_date=CURRENT_VERSION).delete()
    except PricingHistoryEntry.DoesNotExist:
        pass


def disable_old_pricing_version(apps, schema_editor):
    PricingHistoryEntry = apps.get_model('pricing_v3', 'PricingHistoryEntry')
    PricingHistoryEntry.objects.filter(end_date=None).update(
        end_date=CURRENT_VERSION,
        calculator_version='version_11.calculator',
    )


def enable_old_pricing_version(apps, schema_editor):
    PricingHistoryEntry = apps.get_model('pricing_v3', 'PricingHistoryEntry')
    PricingHistoryEntry.objects.filter(end_date=CURRENT_VERSION).update(
        end_date=None, calculator_version='current'
    )


class Migration(migrations.Migration):

    dependencies = [
        ('pricing_v3', '0017_add_pricing_version'),
    ]

    operations = [
        migrations.RunPython(
            disable_old_pricing_version,
            enable_old_pricing_version,
            elidable=True,
        ),
        migrations.RunPython(
            create_current_pricing_version,
            delete_current_pricing_history,
            elidable=True,
        ),
    ]
