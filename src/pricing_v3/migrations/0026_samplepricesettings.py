# Generated by Django 4.1.13 on 2025-05-10 14:46

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('pricing_v3', '0025_add_pricing_version'),
    ]

    operations = [
        migrations.CreateModel(
            name='SamplePriceSettings',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'storage_sample_price',
                    models.DecimalField(
                        decimal_places=0, default=2, help_text='In EUR', max_digits=2
                    ),
                ),
                (
                    'storage_sample_sale_price',
                    models.DecimalField(
                        decimal_places=0, default=1, help_text='In EUR', max_digits=2
                    ),
                ),
                ('storage_sample_promo_active', models.BooleanField(default=False)),
                (
                    'sofa_sample_price',
                    models.DecimalField(
                        decimal_places=0, default=1, help_text='In EUR', max_digits=2
                    ),
                ),
                (
                    'sofa_sample_sale_price',
                    models.DecimalField(
                        decimal_places=0, default=0, help_text='In EUR', max_digits=2
                    ),
                ),
                ('sofa_sample_promo_active', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name_plural': 'Sample Price Settings',
            },
        ),
    ]
