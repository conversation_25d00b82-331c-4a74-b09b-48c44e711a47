from decimal import Decimal


class AggregateItemPriceMixin:
    @property
    def aggregate_price(self):
        return self._aggregate_value('price')

    @property
    def aggregate_price_net(self):
        return self._aggregate_value('price_net')

    @property
    def aggregate_region_price(self):
        return self._aggregate_value('region_price')

    @property
    def aggregate_region_price_net(self):
        return self._aggregate_value('region_price_net')

    @property
    def aggregate_assembly_price(self):
        return self._aggregate_value('assembly_price')

    @property
    def aggregate_region_assembly_price(self):
        return self._aggregate_value('region_assembly_price')

    @property
    def aggregate_delivery_price(self):
        return self._aggregate_value('delivery_price')

    @property
    def aggregate_region_delivery_promo_value(self):
        return self._aggregate_value('region_delivery_promo_value')

    @property
    def aggregate_delivery_price_without_discount(self):
        return self._aggregate_value('delivery_price_without_discount')

    @property
    def aggregate_region_delivery_price(self):
        return self._aggregate_value('region_delivery_price')

    @property
    def aggregate_vat_amount(self):
        return self._aggregate_value('vat_amount')

    @property
    def aggregate_region_vat_amount(self):
        return self._aggregate_value('region_vat_amount')

    @property
    def aggregate_region_promo_value(self):
        return self._aggregate_value('region_promo_value')

    @property
    def aggregate_recycle_tax_value(self):
        return self._aggregate_value('recycle_tax_value')

    def _aggregate_value(self, attribute: str) -> Decimal:
        return getattr(self, attribute) * self.quantity
