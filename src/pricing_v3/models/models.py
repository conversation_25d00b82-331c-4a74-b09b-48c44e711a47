import typing

from datetime import datetime
from decimal import Decimal

from django.db import models

from custom.constants import QUARTER
from custom.encoders import (
    ExplicitDecimalJSONDecoder,
    ExplicitDecimalJSONEncoder,
)
from custom.models import (
    CacheInvalidationMixin,
    SingletonManager,
)
from custom.utils.in_memory_cache import expiring_lru_cache
from regions.models import Region

if typing.TYPE_CHECKING:
    from carts.models import Cart
    from orders.models import Order


class PricingVersion(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    coefficients = models.JSONField(
        default=dict,
        encoder=ExplicitDecimalJSONEncoder,
        decoder=ExplicitDecimalJSONDecoder,
    )
    region = models.ForeignKey(
        'regions.Region',
        related_name='pricing_versions',
        on_delete=models.CASCADE,
        null=True,
        default=None,
    )

    @property
    def is_generic(self) -> bool:
        return self.region is None

    @staticmethod
    def get_for_instance(instance: typing.Union['Order', 'Cart']) -> 'PricingVersion':
        return PricingVersion.get_for_datetime(
            datetime_=instance.price_updated_at,
            region=instance.region,
        )

    @staticmethod
    def get_for_datetime(
        datetime_: datetime,
        region: Region,
    ) -> 'PricingVersion':
        return PricingVersion._get_valid_pricing_for_region(datetime_, region)

    @staticmethod
    def _get_valid_pricing_for_region(
        datetime_: datetime,
        region: Region,
    ) -> 'PricingVersion':
        version = PricingVersion.objects.filter(
            created_at__lte=datetime_,
            region=region,
        ).first()
        if version:
            return version
        return PricingVersion._get_valid_generic_pricing_version(datetime_)

    @staticmethod
    def _get_valid_generic_pricing_version(
        datetime_: datetime,
    ) -> 'PricingVersion':
        version = PricingVersion.objects.filter(
            created_at__lte=datetime_,
            region__isnull=True,
        ).first()
        if version:
            return version
        return PricingVersion._get_fallback()

    @staticmethod
    def _get_fallback() -> 'PricingVersion':
        return PricingVersion.objects.filter(region__isnull=True).last()

    def __str__(self):
        region_repr = 'Generic'
        if not self.is_generic:
            region_repr = f"{self.region.name}'s"
        return (
            f'{region_repr} Pricing Version - '
            f'{self.created_at.strftime("%Y-%m-%d %H:%M:%S")}'
        )

    class Meta:
        ordering = ('-created_at',)


class PricingHistoryEntry(models.Model):
    start_date = models.DateTimeField(null=True)
    end_date = models.DateTimeField(null=True)
    calculator_version = models.CharField(
        max_length=50,
        help_text='Relative path to pricing calculator version. '
        'Preferably "version_1", "version_2", etc. ',
    )

    class Meta:
        unique_together = (
            'start_date',
            'end_date',
        )


class SamplePriceSettings(CacheInvalidationMixin, models.Model):
    """
    Keeps all data related to samples prices
    """

    storage_sample_price = models.DecimalField(
        max_digits=2,
        decimal_places=0,
        default=2,
        help_text='In EUR',
    )
    storage_sample_sale_price = models.DecimalField(
        max_digits=2,
        decimal_places=0,
        default=1,
        help_text='In EUR',
    )
    storage_sample_promo_active = models.BooleanField(
        default=False,
    )
    sofa_sample_price = models.DecimalField(
        max_digits=2,
        decimal_places=0,
        default=1,
        help_text='In EUR',
    )
    sofa_sample_sale_price = models.DecimalField(
        max_digits=2,
        decimal_places=0,
        default=0,
        help_text='In EUR',
    )
    sofa_sample_promo_active = models.BooleanField(
        default=False,
    )

    objects = SingletonManager()

    class Meta:
        verbose_name_plural = 'Sample Price Settings'

    @classmethod
    def _get_instance(cls):
        return cls.objects.first()

    @classmethod
    def _get_price(cls, base_price_field, sale_price_field, promo_active_field):
        """Generic method to calculate current price based on promo status"""
        instance = cls._get_instance()
        base_price = getattr(instance, base_price_field)
        sale_price = getattr(instance, sale_price_field)
        is_promo = getattr(instance, promo_active_field)
        return sale_price if is_promo else base_price

    # Wooden sample methods
    @classmethod
    @expiring_lru_cache(ttl=QUARTER)
    def get_storage_sample_price_in_euro(cls) -> Decimal:
        return cls._get_price(
            'storage_sample_price',
            'storage_sample_sale_price',
            'storage_sample_promo_active',
        )

    @classmethod
    @expiring_lru_cache(ttl=QUARTER)
    def get_storage_sample_base_price(cls) -> Decimal:
        return cls._get_instance().storage_sample_price

    @classmethod
    @expiring_lru_cache(ttl=QUARTER)
    def get_storage_sample_sale_price(cls) -> Decimal:
        return cls._get_instance().storage_sample_sale_price

    @classmethod
    @expiring_lru_cache(ttl=QUARTER)
    def is_storage_sample_promo_active(cls) -> bool:
        return cls._get_instance().storage_sample_promo_active

    # Sofa sample methods
    @classmethod
    @expiring_lru_cache(ttl=QUARTER)
    def get_sofa_sample_price_in_euro(cls) -> Decimal:
        return cls._get_price(
            'sofa_sample_price',
            'sofa_sample_sale_price',
            'sofa_sample_promo_active',
        )

    @classmethod
    @expiring_lru_cache(ttl=QUARTER)
    def get_sofa_sample_base_price(cls) -> Decimal:
        return cls._get_instance().sofa_sample_price

    @classmethod
    @expiring_lru_cache(ttl=QUARTER)
    def get_sofa_sample_sale_price(cls) -> Decimal:
        return cls._get_instance().sofa_sample_sale_price

    @classmethod
    @expiring_lru_cache(ttl=QUARTER)
    def is_sofa_sample_promo_active(cls) -> bool:
        return cls._get_instance().sofa_sample_promo_active
