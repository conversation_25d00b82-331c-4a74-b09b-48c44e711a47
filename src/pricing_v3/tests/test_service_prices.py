from unittest import mock

import pytest

from carts.services.cart_service import CartService
from gallery.constants import (
    ONE_MODULE_OR_LESS,
    THREE_MODULES_OR_MORE,
    TWO_MODULES,
)
from services.constants import OLD_SOFA_COLLECTION_PRICE_MAPPER
from vouchers.enums import VoucherType
from vouchers.services.voucher_service import VoucherService


@pytest.mark.django_db
class TestAdditionalServicePricesInCart:
    @pytest.mark.parametrize(
        ('number_of_modules', 'expected_service_price'),
        [
            (1, OLD_SOFA_COLLECTION_PRICE_MAPPER[ONE_MODULE_OR_LESS]),
            (2, OLD_SOFA_COLLECTION_PRICE_MAPPER[TWO_MODULES]),
            (3, OLD_SOFA_COLLECTION_PRICE_MAPPER[THREE_MODULES_OR_MORE]),
            (4, OLD_SOFA_COLLECTION_PRICE_MAPPER[THREE_MODULES_OR_MORE]),
        ],
    )
    def test_add_service(
        self,
        cart_factory,
        cart_item_factory,
        region_de,
        number_of_modules,
        expected_service_price,
    ):
        cart = cart_factory(region=region_de, items=[])
        cart_item_factory(cart=cart, is_sotty=True)
        service = CartService(cart)
        assert cart.items.count() == 1
        assert not cart.has_old_sofa_collection
        with mock.patch(
            'carts.models.Cart.get_all_sofas_modules_number',
            return_value=number_of_modules,
        ):
            service.change_old_sofa_collection(activate=True)

        assert cart.items.count() == 2
        service_item = cart.items.get(content_type__model='additionalservice')
        sofa_item = cart.items.get(content_type__model='sotty')
        assert service_item.price == expected_service_price
        assert service_item.assembly_price == 0
        assert service_item.region_assembly_price == 0
        assert service_item.with_assembly is False
        assert service_item.recycle_tax_value == 0

        assert (
            cart.total_price
            == expected_service_price + sofa_item.price + sofa_item.delivery_price
        )

    def test_remove_old_sofa_collection(self, cart_with_sofa_and_old_sofa_collection):
        cart = cart_with_sofa_and_old_sofa_collection
        service = cart.items.get(content_type__model='additionalservice')

        service_price = service.price
        service_region_price = service.region_price
        old_total_price = cart.total_price
        old_region_total_price = cart.region_total_price

        assert cart.items.count() == 2
        assert cart.has_old_sofa_collection

        cart_service = CartService(cart)
        cart_service.change_old_sofa_collection(activate=False)

        cart.refresh_from_db()
        assert cart.items.count() == 1
        assert not cart.has_old_sofa_collection
        assert cart.total_price == old_total_price - service_price
        assert cart.region_total_price == old_region_total_price - service_region_price

    @pytest.mark.parametrize(
        'voucher_type', (VoucherType.PERCENTAGE, VoucherType.ABSOLUTE)
    )
    def test_no_promo_is_applied(
        self, cart_with_sofa_and_old_sofa_collection, voucher_factory, voucher_type
    ):
        cart = cart_with_sofa_and_old_sofa_collection
        voucher = voucher_factory(value=50, kind_of=voucher_type)
        service = cart.items.get(content_type__model='additionalservice')
        service_price_before = service.price
        service_region_price_before = service.region_price
        cart_price_before = cart.total_price
        VoucherService(cart, voucher.code).process_voucher()
        cart.refresh_from_db()
        service.refresh_from_db()
        assert service.price == service_price_before
        assert service.region_price == service_region_price_before
        assert cart_price_before != cart.total_price
