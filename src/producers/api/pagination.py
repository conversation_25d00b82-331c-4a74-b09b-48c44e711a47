from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response


class ProducerPagePagination(PageNumberPagination):
    page_size_query_param = 'page_size'
    page_query_param = 'page_number'
    page_size = 100
    max_page_size = 1000


class ProducerBatchPagePagination(ProducerPagePagination):
    def get_paginated_response(self, data):
        return Response(
            {
                'page_size': self.page_size,
                'page_number': self.page.number,
                'count': self.page.paginator.count,
                'batches': [
                    batch.get_cached_batch_description_for_manufactor()
                    for batch in data
                ],
            }
        )
