from io import By<PERSON><PERSON>
from typing import (
    TYPE_CHECKING,
    List,
    Optional,
    Tuple,
    Union,
)
from zipfile import ZipFile

from pdfrw import (
    PdfReader,
    PdfWriter,
)

from custom.enums import Furniture
from custom.utils.report_file import ReportFile
from producers.constants import EMPTY_PDF_PATH
from producers.models import Manufactor
from producers.utils import (
    add_bytes_to_zip,
    add_file_to_zip,
    add_unzipped_file_to_zip,
)

if TYPE_CHECKING:
    from django.db.models.fields.files import FieldFile

    from producers.models import ProductBatch
    from producers.models_split.product_batch_details import (
        ProductBatchDetailsJetty,
        ProductBatchDetailsWatty,
    )


def _add_frontview_pdf_pages(writer: PdfWriter, frontview_path: str) -> None:
    """Merge pages from frontview_path pdf file into PdfWriter handler

    We also add an empty page if the number of pages in the file were not even

    :param writer: PdfWriter that we can add pages in place
    :param frontview_path: location of a single frontview pdf to take pages from
    """
    pages = PdfReader(frontview_path).pages
    empty_page = PdfReader(EMPTY_PDF_PATH).pages

    writer.addpages(pages)
    if len(pages) % 2 != 0:
        writer.addpages(empty_page)


def _add_batch_details_files(
    batch_details: Union['ProductBatchDetailsJetty', 'ProductBatchDetailsWatty'],
    bath_files_list: List['FieldFile'],
    zipfile: ZipFile,
) -> None:
    """Add batch_details connected files from the bath_files_list to the zipfile

    Special manufactor cases:
    - CenterMebel needs nesting_zip unpacked into PLIKI_LINIA directory
    """
    if (
        batch_details.product_batch.manufactor.name == Manufactor.CENTERMEBEL
        and batch_details.nesting_zip
        and batch_details.nesting_zip in bath_files_list
    ):
        # CenterMebel wants its OPTI and LINIA files extracted
        bath_files_list.remove(batch_details.nesting_zip)
        add_unzipped_file_to_zip(
            batch_details.nesting_zip,
            zipfile,
            'PLIKI_LINIA/',
        )
    for file in bath_files_list:
        add_file_to_zip(file, zipfile)


def _get_zipped_frontview(batch: 'ProductBatch') -> Tuple[BytesIO, str]:
    """Iterate over all products in a batch and zip their fronview pdfs together

    :return: BytesIO file-like object and filename of the zipped result
    """
    front_view_file = BytesIO()
    front_view_filename = 'frontview_package.zip'
    with ZipFile(front_view_file, 'w') as front_view_zipfile:
        for product in batch.batch_items.all().order_by('id'):
            product_details = product.details
            add_file_to_zip(product_details.front_view, front_view_zipfile)
    return front_view_file, front_view_filename


def _get_merged_pdf_frontview(batch: 'ProductBatch') -> Tuple[BytesIO, str]:
    """Iterate over all products in a batch and merge their fronview pdfs into one pdf

    :return: BytesIO file-like object and filename of the zipped result
    """
    front_view_file = BytesIO()
    pdf_writer = PdfWriter()
    front_view_filename = f'front_pack_B{batch.id}.pdf'

    for product in batch.batch_items.all().order_by('id'):
        product_details = product.details
        _add_frontview_pdf_pages(pdf_writer, product_details.front_view)

    pdf_writer.write(front_view_file)
    return front_view_file, front_view_filename


def _add_front_view_file_zip(main_zipfile: ZipFile, batch: 'ProductBatch') -> None:
    front_view_file, front_view_filename = _get_zipped_frontview(batch)
    add_bytes_to_zip(front_view_file.getvalue(), front_view_filename, main_zipfile)
    front_view_file.close()


def _add_front_view_file_merged_pdf(
    main_zipfile: ZipFile, batch: 'ProductBatch'
) -> None:
    front_view_file, front_view_filename = _get_merged_pdf_frontview(batch)
    add_bytes_to_zip(front_view_file.getvalue(), front_view_filename, main_zipfile)
    front_view_file.close()


def _get_production_files_jetty(
    zipfile: ZipFile,
    batch: 'ProductBatch',
) -> None:
    """Pack all needed (and available) Jetty production files to the provided zipfile

    Used on pages/producer_batches/ by manufactors who need to get the files in order to
    produce the furniture.

    Special manufactor cases:

    - Drewtur needs front_view files as zipped individual files
    - Telmex needs production drawings for blende elements
    """
    batch_details = batch.details

    for product in batch.batch_items.all().order_by('id'):
        product_details = product.details

        if batch.manufactor.name == Manufactor.TELMEX:
            add_file_to_zip(
                file=product_details.production_drawings,
                zipfile=zipfile,
                directory='drawings',
            )
        elif batch.manufactor.name == Manufactor.DREWTUR:
            add_file_to_zip(
                file=product_details.front_view_zip,
                zipfile=zipfile,
                directory='front_view_zip',
            )

        add_file_to_zip(
            file=product_details.cnc_connections,
            zipfile=zipfile,
            directory='CNC',
        )
        product_details.write_cnc_connections_to_zip(zipfile)

    if batch.manufactor.name in {Manufactor.DREWTUR, Manufactor.S93}:
        _add_front_view_file_zip(zipfile, batch)
    _add_front_view_file_merged_pdf(zipfile, batch)

    batch_files = [
        batch_details.nesting,
        batch_details.nesting_backs,
        batch_details.nesting_drawers,
        batch_details.nesting_bottom_drawers,
        batch_details.nesting_front_drawers,
        batch_details.nesting_handle_blende,
        batch_details.nesting_desk_beam,
        batch_details.packaging_csvs,
        batch_details.nesting_zip,
        batch_details.cardboard_beams_xls,
        batch_details.rotated_elements,
    ]

    _add_batch_details_files(batch_details, batch_files, zipfile)


def _get_production_files_watty(
    zipfile: ZipFile,
    batch: 'ProductBatch',
) -> None:
    """Pack all needed (and available) Watty production files to the provided zipfile

    Used on pages/producer_batches/ by manufactors who need to get the files in order to
    produce the furniture.

    Special manufactor cases:
    - Drewtur needs front_view files as zipped individual files
    """
    batch_details = batch.details

    for product in batch.batch_items.all().order_by('id'):
        product_details = product.details

        add_file_to_zip(
            file=product_details.production_drawings,
            zipfile=zipfile,
            directory='rysunki',
        )

        product_details.write_cnc_connections_to_zip(zipfile)

    if batch.manufactor.name in {Manufactor.DREWTUR, Manufactor.S93}:
        _add_front_view_file_zip(zipfile, batch)
    _add_front_view_file_merged_pdf(zipfile, batch)

    batch_files = [
        batch_details.nesting_zip,  # TODO: re-pack?
        batch_details.nesting_bar,
        batch_details.nesting_mask,
        batch_details.nesting_drawer_synchro,
        batch_details.nesting_hang_slat,
        batch_details.nesting_led_profile,
        batch_details.lighting_completion_list,
        batch_details.labels_elements,
        batch_details.labels_packaging,
        batch_details.labels_adapters,
        batch_details.labels_logistic,
        batch_details.packaging_csvs,
        batch_details.rotated_elements,
    ]

    _add_batch_details_files(batch_details, batch_files, zipfile)


def _get_packaging_files_jetty(
    zipfile: ZipFile,
    batch: 'ProductBatch',
) -> None:
    """Pack all needed (and available) Jetty packaging files to the provided zipfile

    Used on pages/producer_batches/ by manufactors who need to get the files in order to
    pack the product.
    """
    for product in batch.batch_items.all():
        product_details = product.details

        add_file_to_zip(
            product_details.packaging_instruction,
            zipfile,
            directory='pakowanie',
        )

        add_file_to_zip(
            product_details.vertical_labels,
            zipfile,
            directory='vertical_labels',
        )

    details = batch.details
    labels_files = [
        details.labels_elements,
        details.labels_packaging,
        details.labels_verticals,
    ]
    for file in labels_files:
        add_file_to_zip(file, zipfile, directory='etykiety')

    add_file_to_zip(details.packaging_csvs, zipfile)
    add_file_to_zip(details.cardboard_beams_xls, zipfile)


def _get_packaging_files_watty(
    zipfile: ZipFile,
    batch: 'ProductBatch',
) -> None:
    """Pack all needed (and available) Watty packaging files to the provided zipfile

    Used on pages/producer_batches/ by manufactors who need to get the files in order to
    pack the product.
    """
    for product in batch.batch_items.all():
        product_details = product.details
        add_file_to_zip(
            file=product_details.packaging_instruction,
            zipfile=zipfile,
            directory='pakowanie',
        )
    batch_files = (
        batch.details.labels_elements,
        batch.details.labels_packaging,
        batch.details.packaging_csvs,
    )
    for file in batch_files:
        add_file_to_zip(file, zipfile)


def generate_zip_with_all_batch_files(
    packaging: bool,
    batch: 'ProductBatch',
    filename: str,
) -> Optional[ReportFile]:
    """Generate a zip file with selected production or packaging files

    If packaging is True packaging files will be zipped
    If packaging is False production files will be zipped

    :return: Bytes representation of a zipped file
    """
    production_files_handlers = {
        Furniture.jetty.value: _get_production_files_jetty,
        Furniture.watty.value: _get_production_files_watty,
    }
    packaging_handlers = {
        Furniture.jetty.value: _get_packaging_files_jetty,
        Furniture.watty.value: _get_packaging_files_watty,
    }

    stream = BytesIO()
    with ZipFile(stream, 'w') as zipfile:
        product_type = batch.product_type
        if packaging:
            handler = packaging_handlers.get(product_type, None)
        else:
            handler = production_files_handlers.get(product_type, None)

        if handler is not None:
            handler(zipfile, batch)

    return ReportFile(content=stream.getvalue(), name=filename)
