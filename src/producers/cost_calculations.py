from dataclasses import dataclass
from datetime import date
from decimal import Decimal

from django.db.models import Avg
from django.utils import timezone

from producers.constants import (
    ADDITIONAL_ELEMENT_TO_SERVICE_COSTS,
    CODENAMES_EXCLUDED_SERVICES,
)
from producers.utils import round_down_if_item
from production_margins.models import (
    CustomPricingFactor,
    ElementManagedInfo,
    MaterialManagementCost,
    PricingFactors,
)

PRICE_COL_NAME = 'price_per_unit'
LOSS_COL_NAME = 'loss_factor'


class CachedCustomPricing:
    def __init__(self, manufactor, date_to_check=None):
        self.cached_custom_pricing = self._get_custom_pricing_dict(
            manufactor, date_to_check
        )
        self.cached_pricing_factor = PricingFactors.get_solo().df

    @classmethod
    def _get_custom_pricing_dict(cls, manufactor, date_to_check):
        query = (
            CustomPricingFactor.objects.valid_for_date(
                date_to_check,
            )
            .filter(manufactor=manufactor)
            .values_list(
                'pricing_factor_item__codename',
                'loss_factor',
                'price',
                'manufacturer_code',
            )
        )
        return {
            codename: {
                'price': price,
                'loss_factor': loss_factor,
                'manufacturer_code': manufacturer_code,
            }
            for codename, loss_factor, price, manufacturer_code in query
        }

    def get_loss_factor(self, codename):
        custom_pricing = self.cached_custom_pricing.get(codename)
        if custom_pricing:
            return custom_pricing['loss_factor'] / 100

        loss_factor = self.cached_pricing_factor[LOSS_COL_NAME][codename]
        if loss_factor < 1:
            raise Exception(
                'Loss factor from PricingFactors model cannot be less than 1.'
            )
        return loss_factor - 1

    def get_price(self, codename):
        custom_price = self.cached_custom_pricing.get(codename)
        if custom_price:
            return custom_price['price']
        return self.cached_pricing_factor[PRICE_COL_NAME][codename]

    def get_manufacturer_code(self, codename):
        custom_price = self.cached_custom_pricing.get(codename)
        if not custom_price['manufacturer_code']:
            return ''
        return custom_price['manufacturer_code']


class CachedElementManagedInfo:
    def __init__(self, manufactor, date_to_check=None):
        self.element_managed_info = self._get_element_managed_info_dict(
            manufactor, date_to_check
        )

    @classmethod
    def _get_element_managed_info_dict(cls, manufactor, date_to_check):
        query = (
            ElementManagedInfo.objects.valid_for_date(date_to_check)
            .filter(manufactor=manufactor)
            .values_list('pricing_factor_item__codename', 'managed_by_us')
        )
        return dict(query)

    def is_managed_by_us(self, codename):
        if is_service_cost(codename):
            return False
        if codename in self.element_managed_info:
            return self.element_managed_info.get(codename)
        return True


class CachedCostData:
    def __init__(self, manufactor, date_to_check):
        date_to_check = date_to_check or date.today()
        self.custom_pricing = CachedCustomPricing(manufactor, date_to_check)
        self.element_management_info = CachedElementManagedInfo(
            manufactor, date_to_check
        )


def get_average_price(codename, date_to_check=None):
    if date_to_check is None:
        date_to_check = date.today()

    custom_pricing_factor = CustomPricingFactor.objects.valid_for_date(
        date_to_check,
    ).filter(
        pricing_factor_item__codename=codename,
    )
    if not custom_pricing_factor.exists():
        # Price is not overriden, trying from default model.
        return Decimal(PricingFactors.get_solo().df[PRICE_COL_NAME][codename])
    return custom_pricing_factor.aggregate(avg_price=Avg('price'))['avg_price']


def get_price(codename, manufactor, date_to_check=None):
    if date_to_check is None:
        date_to_check = date.today()

    try:
        custom_pricing_factor = CustomPricingFactor.objects.valid_for_date(
            date_to_check,
        ).get(
            pricing_factor_item__codename=codename,
            manufactor=manufactor,
        )
    except CustomPricingFactor.DoesNotExist:
        # Price is not overriden, trying from default model.
        return PricingFactors.get_solo().df[PRICE_COL_NAME][codename]
    return custom_pricing_factor.price


def get_loss_factor(codename, manufactor, date_to_check=None):
    if date_to_check is None:
        date_to_check = date.today()

    try:
        custom_pricing_factor = CustomPricingFactor.objects.valid_for_date(
            date_to_check,
        ).get(
            pricing_factor_item__codename=codename,
            manufactor=manufactor,
        )
        return custom_pricing_factor.loss_factor / 100
    except (CustomPricingFactor.DoesNotExist, AttributeError):
        # Loss factor is not overriden, trying from default model.
        # Value from PricingFactors is returned as (1+loss_factor)
        loss_factor = PricingFactors.get_solo().df[LOSS_COL_NAME][codename]

    if loss_factor < 1:
        raise Exception('Loss factor from PricingFactors model cannot be less than 1.')

    return loss_factor - 1


def get_manufacturer_code(date_to_check, manufactor, codename):
    try:
        custom_pricing_factor = CustomPricingFactor.objects.valid_for_date(
            date_to_check,
        ).get(
            pricing_factor_item__codename=codename,
            manufactor=manufactor,
        )
        return custom_pricing_factor.manufacturer_code.code
    except (CustomPricingFactor.DoesNotExist, AttributeError):
        return ''


def get_management_cost(manufactor, date_to_check=None):
    if date_to_check is None:
        date_to_check = date.today()

    try:
        management_cost = (
            float(
                MaterialManagementCost.objects.valid_for_date(date_to_check)
                .get(
                    manufactor=manufactor,
                )
                .management_cost
            )
            / 100
        )
    except MaterialManagementCost.DoesNotExist:
        management_cost = 0.05

    return management_cost


def is_managed_by_us(manufactor, current_codename, date_to_check=None):
    if is_service_cost(current_codename):
        return False

    if date_to_check is None:
        date_to_check = timezone.now().date()

    try:
        managed_obj = ElementManagedInfo.objects.valid_for_date(date_to_check).get(
            pricing_factor_item__codename=current_codename,
            manufactor=manufactor,
        )
        return managed_obj.managed_by_us
    except ElementManagedInfo.DoesNotExist:
        return True


def is_sofa_part(codename):
    return codename.startswith('semiproduct_S1_')


def is_sofa_material(codename):
    return codename.startswith('cover_')


def is_fitting(codename: str) -> bool:
    return codename.startswith('fitting_')


def is_service_cost(codename):
    is_not_excluded_service = codename.startswith(
        'service_'
    ) and not is_excluded_service(codename)
    return is_not_excluded_service or codename in ADDITIONAL_ELEMENT_TO_SERVICE_COSTS


def is_excluded_service(codename):
    return codename in CODENAMES_EXCLUDED_SERVICES


@dataclass
class CodenameCostsFactors:
    name: str
    unit: str
    unit_price: float
    quantity: float
    loss_factor: float
    managed_by_us: bool
    management_cost: float
    manufactor_fault: bool

    @property
    def quantity_with_losses(self):
        if not is_excluded_service(self.name):
            quantity_with_losses = self.quantity * (1 + self.loss_factor)
            return round_down_if_item(
                quantity_with_losses,
                self.name,
                self.unit,
            )
        return self.quantity

    def get_costs(self):
        if not (is_service_cost(self.name) or not is_excluded_service(self.name)):
            return None
        return CodenameCosts(self)


class CodenameCosts:
    def __init__(self, cost_factors: CodenameCostsFactors):
        self.cost_factors = cost_factors
        self.fault_factor = -1 if cost_factors.manufactor_fault else 1
        self.cost_with_losses = self._get_cost_with_losses() * self.fault_factor
        self.cost_of_management = self._get_cost_of_management() * self.fault_factor
        self.total_cost = self._get_total_cost() * self.fault_factor

    def _get_cost_with_losses(self):
        return self.cost_factors.unit_price * self.cost_factors.quantity_with_losses

    def _get_cost_of_management(self):
        if is_service_cost(self.cost_factors.name) or self.cost_factors.managed_by_us:
            return 0.0
        return self.cost_with_losses * self.cost_factors.management_cost

    def _get_total_cost(self):
        return self.cost_with_losses + self.cost_of_management
