import typing

from datetime import datetime

from django import forms
from django.db.models import QuerySet

from producers.choices import (
    DeliveryPriority,
    ProductPriority,
    ProductStatus,
    SourcePriority,
)
from producers.enums import Manufacturers

if typing.TYPE_CHECKING:
    from producers.models import Product


class ChangeStatusWithHistoryForm(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
    status_to_change = forms.TypedChoiceField(
        choices=ProductStatus.choices,
        coerce=int,
    )
    date_added = forms.DateTimeField(
        initial=datetime.now,
        widget=forms.DateTimeInput(attrs={'class': 'datetime-input'}),
    )
    description = forms.CharField(widget=forms.Textarea, initial='', required=False)

    def __init__(self, *args, products: QuerySet['Product'], **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.products = products

    def clean(self) -> None:
        cleaned_data = super().clean()
        status_to_change = cleaned_data.get('status_to_change')

        if int(status_to_change) == ProductStatus.QUALITY_BLOCKER:
            products_with_status = [
                (product.id, product.status == ProductStatus.TO_BE_SHIPPED)
                for product in self.products
            ]
            if not all(product_status[1] for product_status in products_with_status):
                illegal_products = ','.join(
                    str(with_status) for with_status in products_with_status
                )
                raise forms.ValidationError(
                    f"Following products: {illegal_products} can't be set to "
                    f'Quality Blocker because of current status.'
                )


class ChangePriorityWithHistoryForm(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
    priority_to_change = forms.TypedChoiceField(
        choices=ProductPriority.choices_available_for_users(),
        coerce=int,
    )
    requested_postponed_delivery_date = forms.DateField(
        widget=forms.TextInput(attrs={'type': 'date'}),
        required=False,
    )


class ChangeManufactorForProductForm(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
    manufactor_to_change = forms.TypedChoiceField(
        choices=Manufacturers.choices(),
        coerce=int,
    )


class ChangeDeliveryPriorityForm(forms.Form):
    priority = forms.TypedChoiceField(
        choices=DeliveryPriority.choices,
        coerce=int,
    )
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)


class ChangeSourcePriorityForm(forms.Form):
    priority = forms.TypedChoiceField(
        choices=SourcePriority.choices,
        coerce=int,
    )
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
