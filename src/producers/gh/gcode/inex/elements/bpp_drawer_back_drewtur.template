[HEADER]
TYPE=BPP
VER=150

[DESCRIPTION]
|

[VARIABLES]
PAN=LPX|{{ width }}||4|
PAN=LPY|{{ height }}||4|
PAN=LPZ|{{ thickness }}||4|
PAN=ORLST|"1"||3|
PAN=SIMMETRY|1||1|
PAN=TLCHK|0||1|
PAN=TOOLING|""||3|
PAN=CUSTSTR|$B$KBsExportToNcRoverNET.XncExtraPanelData$V""$KBsSkipperProcessor1.PreProc$V""||3|
PAN=FCN|1.000000||2|
PAN=XCUT|0||4|
PAN=YCUT|0||4|
PAN=JIGTH|0||4|
PAN=CKOP|0||1|
PAN=UNIQUE|0||1|
PAN=MATERIAL|"wood"||3|
PAN=PUTLST|""||3|
PAN=OPPWKRS|0||1|
PAN=UNICLAMP|0||1|
PAN=CHKCOLL|0||1|
PAN=WTPIANI|0||1|
PAN=COLLTOOL|0||1|
PAN=CALCEDTH|0||1|
PAN=ENABLELABEL|0||1|
PAN=LOCKWASTE|0||1|
PAN=LOADEDGEOPT|0||1|
PAN=ITLTYPE|0||1|
PAN=RUNPAV|0||1|
PAN=FLIPEND|0||1|

[PROGRAM]


@ PRESS_DIS, "", "", 186026460, "", 0 :
{%- if height == 98 %}
@ BG, "", "", 94261708, "", 0 : 0, "1", 7-0.2, 39-1, 0, 9.5, 15.2, 0, -1, 0, 0, 0, 0, 0, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 4, -1, "P1005", 0, "", "WIED15", 1, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0
{%- elif height == 198 %}
@ BG, "", "", 94261708, "", 0 : 0, "1", 7-0.2, 89-1, 0, 9.5, 15.2, 0, -1, 0, 0, 0, 0, 0, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 4, -1, "P1005", 0, "", "WIED15", 1, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0
{%- elif height == 298 %}
@ BG, "", "", 94261708, "", 0 : 0, "1", 7-0.2, 139-1, 0, 9.5, 15.2, 0, -1, 0, 0, 0, 0, 0, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 4, -1, "P1005", 0, "", "WIED15", 1, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0
{%- endif %}
@ BG, "", "", 94710916, "", 0 : 0, "1", 7, 11-1, 0, 10, 6, 0, -1, 32, 32, 50, 0, 45, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 3, -1, "P1002", 0, "", "WIED6U", 1, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0
@ BG, "", "", 93843684, "", 0 : 0, "1", 50, 6-1, 0, 5, 5, 1, -1, 32, 32, 50, 0, 45, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 5, -1, "P1004", 0, "", "WIED5", 1, 0, 0, 0, 1000, "", 0, 0, 0, 0, 0, "", "", "BG", 0
@ BG, "", "", 172988860, "", 0 : 0, "4", 50, 6-1, 0, 5, 5, 1, -1, 32, 32, 50, 0, 45, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 5, -1, "P1004_2", 0, "", "WIED5", 1, 0, 0, 0, 1000, "", 0, 0, 0, 0, 0, "", "", "BG", 0
@ BG, "", "", 86485604, "", 0 : 0, "4", 7, 11-1, 0, 10, 6, 0, -1, 32, 32, 50, 0, 45, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 3, -1, "P1002_2", 0, "", "WIED6U", 1, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0
{%- if height == 98 %}
@ BG, "", "", 86651572, "", 0 : 0, "4", 7-0.2, 39-1, 0, 9.5, 15.2, 0, -1, 0, 0, 0, 0, 0, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 4, -1, "P1005_2", 0, "", "WIED15", 1, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0
{%- elif height == 198 %}
@ BG, "", "", 86651572, "", 0 : 0, "4", 7-0.2, 89-1, 0, 9.5, 15.2, 0, -1, 0, 0, 0, 0, 0, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 4, -1, "P1005_2", 0, "", "WIED15", 1, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0
{%- elif height == 298 %}
@ BG, "", "", 86651572, "", 0 : 0, "4", 7-0.2, 139-1, 0, 9.5, 15.2, 0, -1, 0, 0, 0, 0, 0, 0, "", 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 4, -1, "P1005_2", 0, "", "WIED15", 1, 0, 0, 0, 0, "", 0, 0, 0, 0, 0, "", "", "BG", 0
{%- endif %}

[VBSCRIPT]

[MACRODATA]

[TDCODES]

[PCF]

[TOOLING]

[SUBPROGS]

