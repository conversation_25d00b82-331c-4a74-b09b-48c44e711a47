####Biesse base coding in bpp: 

0 - top, 1 - left, 3 - right, 4 - front, 2 - back, 5 - bottom
```
          +--------------------+
          |                    |
          |         4          |
          |                    |
          +--------------------+

+-----+   +--------------------+  +-----+  +----------------------+
|2   1|   |1                  4|  |4   3|  |                      |
|     |   |                    |  |     |  |                      |
|  1  |   |         0          |  |  3  |  |          5           |
|     |   |                    |  |     |  |                      |
|3   4|   |2                  3|  |1   2|  |                      |
+-----+   +--------------------+  +-----+  +----------------------+

          +--------------------+
          |                    |
          |         2          |
          |                    |
          +--------------------+
```
horizontal orientation, bottom first, rotation through Y axis
```
                    front

      +-----------------------------+
      |                             |
      |                             |
      |            bottom           |
left  |                             |  right
      |                             |
      |                             |
      +-----------------------------+

                    back
    +-----+
          |
       +---------------------------> y
          |
    <-----+


```