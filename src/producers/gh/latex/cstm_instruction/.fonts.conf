<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<!-- /etc/fonts/fonts.conf file to configure system font access -->
<fontconfig>

<!-- Font directory list -->

    <dir>C:\4.projects\01_IVY\01_IVY_MAIN\4_Produkcja na BE\latex\cstm_instruction\webfonts</dir>
<!-- Font cache directory list -->

    <cachedir>~/.fontconfig</cachedir>

<!-- Accept deprecated 'mono' alias, replacing it with 'monospace' -->
    <match target="pattern">
        <test qual="any" name="family">
            <string>mono</string>
        </test>
        <edit name="family" mode="assign">
            <string>monospace</string>
        </edit>
    </match>

<!-- Accept alternate 'sans serif' spelling, replacing it with 'sans-serif' -->
    <match target="pattern">
        <test qual="any" name="family">
            <string>sans serif</string>
        </test>
        <edit name="family" mode="assign">
            <string>sans-serif</string>
        </edit>
    </match>

<!-- Accept deprecated 'sans' alias, replacing it with 'sans-serif' -->
    <match target="pattern">
        <test qual="any" name="family">
            <string>sans</string>
        </test>
        <edit name="family" mode="assign">
            <string>sans-serif</string>
        </edit>
    </match>

</fontconfig>
