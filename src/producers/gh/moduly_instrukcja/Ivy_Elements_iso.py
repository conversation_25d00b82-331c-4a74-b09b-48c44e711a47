from past.utils import old_div

from producers.gh.moduly_glowne import Ivy_Settings
from producers.gh.moduly_instrukcja.manual_geometry import get_circle_geometry

from . import (
    Ivy_instrukcja_shapes,
    Ivy_isometric,
)


class IvyElementsIso(object):
    @staticmethod
    def get_element_centroid(pts):
        """
        policz centroid dla listy pkt
        :param pts:
        :return:
        """
        _centr = [0, 0, 0]
        div = len(pts)
        if isinstance(pts, dict):
            for pt in list(pts.values()):
                _centr[0] += pt[0]
                _centr[1] += pt[1]
                if len(pt) > 2:
                    _centr[2] += pt[2]
        elif isinstance(pts, list):
            for pt in pts:
                _centr[0] += pt[0]
                _centr[1] += pt[1]
                if len(pt) > 2:
                    _centr[2] += pt[2]
        centroid = [
            old_div(_centr[0], div),
            old_div(_centr[1], div),
            old_div(_centr[2], div),
        ]
        return centroid

    def get_element_transform(
        self,
        position='basic',
        shelf_height=1,
        explode=0,
    ):
        """
        element transform, to draw elements f.
        ex. in assembly step, separated from shelf body
        :param position:
        :param shelf_height:
        :param explode:
        :return:
        """
        # TODO refactor magic numbers in this def
        explode_dist = Ivy_Settings.elem_explode.get(self.ELEM_TYPE, 400)
        explode_module = Ivy_Settings.module_explode
        # przesuniecie w osiach na potrzeby rysunkow zlozenia szafki
        transform = (0, 0, 0)
        if position == 'basic':
            transform = (0, 0, 0)
        elif position == 'assembly':
            if self.ELEM_TYPE == 'V':
                if 'd' in self.subtype:
                    # desk vertical solo has different assembly position
                    transform = (0, -explode_dist, 0)
                else:
                    transform = (0, explode_dist, 0)
            elif self.ELEM_TYPE == 'H':
                transform = (0, explode_dist, 0)
            elif self.ELEM_TYPE in 'SB':
                transform = (0, 0, -explode_dist / 3.0)
            elif self.ELEM_TYPE == 'D':
                transform = (0, 0, 0)
            elif self.ELEM_TYPE == 'I':
                transform = (0, 0, explode_dist * 1.5)
            elif self.ELEM_TYPE == 'L':
                transform = (0, explode_dist * 2, 0)
            elif self.ELEM_TYPE == 'P':
                transform = (0, -explode_dist / 2, 0)
        elif position == 'assembly_special':
            if self.ELEM_TYPE == 'L':
                transform = (0, -explode_dist * 2.75, 0)
            if self.ELEM_TYPE == 'P':
                transform = (0, 0, -explode_dist)

        elif position == 'exploded':
            if self.ELEM_TYPE == 'H':
                _x = 0
                _y = (
                    -abs(self.y1)
                    - shelf_height
                    - 2 * explode_dist
                    + shelf_height * explode * 1.2
                )
                _z = 0
            else:
                _y = -abs(self.y2) + old_div((self.y2 - self.y1), 2) - explode_dist * 2
                if self.x1 <= 0:
                    _x = abs(old_div(self.x1, 2))
                elif self.x1 > 0:
                    _x = old_div(-self.x1, 2)
                _z = 0
            transform = (_x, _y, _z)

        elif position == 'exploded_assembly':

            _x = -abs(self.x1)
            _y = (
                old_div(-(self.y1 + self.y2), 2) - explode
            )  # + float(self.name[-2]) * 2 * explode_dist
            _z = 0
            transform = (_x, _y, _z)

        elif position == 'exploded_x':
            _x = explode_module * (self.module - 1)
            _y = 0
            _z = 0
            transform = (_x, _y, _z)
        return transform

    # TODO do przebudowania bardziej zgodnie z logika components
    #  i elementow z kilku czesci
    def get_element_faces(self, direction, position, shelf_height=1, explode=0):
        """
        slownik z danymi faceow (polygonow)
        front face:
        1-4
        | |
        2-3
        back face:
        5-8
        | |
        6-7
        :param direction:
        :param position:
        :param shelf_height:
        :param explode:
        :return:
        """
        # TODO refactor face names to enum
        transform = self.get_element_transform(
            position=position, shelf_height=shelf_height, explode=explode
        )

        filter_type = (
            ['T_front', 'T_handle']
            if self.ELEM_TYPE == 'T' and position in ['assembly', 'basic']
            else None
        )
        reorder = (
            ['T_back', 'T_side1', 'T_bottom', 'T_side2', 'T_front', 'T_handle']
            if self.ELEM_TYPE == 'T'
            else None
        )
        pts_list = self.get_element_vertices(
            transform, filter_by_type=filter_type, reorder_list=reorder
        )

        centroid = self.get_element_centroid(pts=pts_list[0])
        faces = []
        for pts in pts_list:
            faces.append(self.get_faces_from_vertex(pts))

        if direction == 0:  # szafka stojaca
            # jesli drzwi dodaj tylny face, bo jest widoczny
            if self.ELEM_TYPE == 'D' and position == 'assembly':
                faces_to_pick = ['front', 'right']  # ["front", "top", "left"]
            elif self.ELEM_TYPE == 'T' and position == 'exploded_assembly':
                faces_to_pick = ['front', 'top', 'right']
            else:
                faces_to_pick = ['front', 'top', 'right']
        elif direction == 1:  # szafka lezaca

            faces_to_pick = ['back', 'top', 'right']
        elif direction == 2:  # szafka widoczna z tylu
            if self.ELEM_TYPE == 'B' and position == 'assembly':
                faces_to_pick = ['top', 'back', 'right']
            elif self.ELEM_TYPE == 'B' and position == 'basic':
                faces_to_pick = ['back']
            else:
                faces_to_pick = ['top', 'back', 'right']
        elif direction in {3, 4}:  # horizontal do gory nogami
            faces_to_pick = ['front', 'top', 'right', 'back']
        elif direction == 5:  # plinth upside down
            faces_to_pick = ['back', 'bottom', 'right']

        else:
            faces_to_pick = ['front']
        if self.ELEM_TYPE in 'DT':
            pass
        _picked_faces = []
        for index, face in enumerate(faces):
            # for doors and drawers we use only front face for T_front component
            # to avoid overlapping of other faces on other elements
            if self.ELEM_TYPE in 'TD' and index == 0 and position in [
                'assembly',
                'basic',
            ]:
                _faces_to_pick = ['front']
            else:
                _faces_to_pick = faces_to_pick

            _picked_faces += [
                dict(pts=face[k], type=k)
                for k in sorted(set(_faces_to_pick) & set(face.keys()))
            ]

        faces_and_centroid = dict(faces=_picked_faces, centroid=centroid)
        return faces_and_centroid

    def get_tags(self):
        tags = Ivy_instrukcja_shapes.shapes_1
        tag = tags[f'{self.ELEM_TYPE}{self.subtype}']
        return tag

    def place_tags(self, faces, direction=0):
        tags_placed = []
        tags = self.get_tags()

        for tag in tags:
            if self.ELEM_TYPE in {'L', 'P'}:
                face_to_map = [
                    face for face in faces if face['type'] == tag['face'][direction]
                ][0]['pts']
                tag = Ivy_isometric.InstructionShapes.map_shape(
                    tag['shape'],
                    face_to_map,
                    u=tag['insert'][direction][0],
                    v=tag['insert'][direction][1],
                    rotate_angle=tag['rotation'][direction],
                    scale=tag['scale'],
                )
            else:
                face_to_map = [face for face in faces if face['type'] == tag['face']][
                    0
                ]['pts']

                tag = Ivy_isometric.InstructionShapes.map_shape(
                    tag['shape'],
                    face_to_map,
                    u=tag['insert'][0],
                    v=tag['insert'][1],
                    rotate_angle=tag['rotation'],
                    scale=tag['scale'],
                )
            tags_placed.append(tag)
        return tags_placed

    @staticmethod
    def get_pt_iso(pt, direction=0):
        # TODO refactor this, too much ifs, change variable owerrite to new ones
        # update direction to enum
        alpha, beta, gamma = Ivy_Settings.iso_angles
        if direction in [0, 3]:
            _pt = Ivy_isometric.IvyIsometricView.rotate_point(
                pt,
                rotation_axis='Y',
                angle=alpha,
            )
            _pt = Ivy_isometric.IvyIsometricView.rotate_point(
                _pt,
                rotation_axis='X',
                angle=beta,
            )
        elif direction == 1:
            _pt = Ivy_isometric.IvyIsometricView.rotate_point(
                pt,
                rotation_axis='X',
                angle=gamma,
            )
            _pt = Ivy_isometric.IvyIsometricView.rotate_point(
                _pt,
                rotation_axis='Y',
                angle=alpha,
            )
            _pt = Ivy_isometric.IvyIsometricView.rotate_point(
                _pt,
                rotation_axis='X',
                angle=beta,
            )
        elif direction == 2:
            _pt = Ivy_isometric.IvyIsometricView.rotate_point(
                pt,
                rotation_axis='Y',
                angle=3.14 - alpha,
            )
            _pt = Ivy_isometric.IvyIsometricView.rotate_point(
                _pt,
                rotation_axis='X',
                angle=beta,
            )
        elif direction == 4:
            _pt = Ivy_isometric.IvyIsometricView.rotate_point(
                pt,
                rotation_axis='X',
                angle=gamma,
            )
            _pt = Ivy_isometric.IvyIsometricView.rotate_point(
                _pt,
                rotation_axis='Y',
                angle=3.14 - alpha,
            )
            _pt = Ivy_isometric.IvyIsometricView.rotate_point(
                _pt,
                rotation_axis='z',
                angle=beta,
            )
        elif direction == 5:
            _pt = Ivy_isometric.IvyIsometricView.rotate_point(
                pt,
                rotation_axis='X',
                angle=gamma * 2,
            )
            _pt = Ivy_isometric.IvyIsometricView.rotate_point(
                _pt,
                rotation_axis='Y',
                angle=alpha,
            )
            _pt = Ivy_isometric.IvyIsometricView.rotate_point(
                _pt,
                rotation_axis='X',
                angle=beta,
            )
        else:
            _pt = pt
        return _pt

    def get_element_iso_data(
        self,
        direction,
        position,
        state,
        shelf_height=1,
        explode=0,
    ):
        faces = self.get_element_faces(
            direction,
            position,
            shelf_height=shelf_height,
            explode=explode,
        )
        iso_data = faces
        if state == 'fully_tagged':
            iso_data['textures'] = self.place_tags(faces['faces'], direction=direction)
        else:
            iso_data['textures'] = []
        return iso_data

    def get_pins(self, direction, pin_position='out', transform=(0, 0, 0)):
        """
        zwraca pkt lini bolcow w horizontalach na potrzeby narysowania w instrukcji
        :param shelf_depth:
        :param direction:
        :return:
        """
        pins = dict(axis=None, pins=None)
        _pin_length = (
            Ivy_Settings.pin_length
            if pin_position == 'out'
            else old_div(Ivy_Settings.pin_length, 2)
        )
        _pin_x_offset = Ivy_Settings.pin_offset if pin_position == 'out' else 0
        _pin_y_offset = (
            -Ivy_Settings.pin_offset
        )  # offset bolcow od krawedzi horizontala w glab w rzucie

        depth = self.unit_converter(
            self.z_domain[1] - self.z_domain[0], to_unit='mm', to_int=True
        )

        _pt_a_start = (self.x2 + _pin_x_offset, self.y2, depth + _pin_y_offset)
        _pt_a_end = (
            _pt_a_start[0] + 5 * _pin_x_offset,
            _pt_a_start[1],
            _pt_a_start[2],
        )
        _pt_b_start = (self.x2 + _pin_x_offset, self.y2, -_pin_y_offset)
        _pt_b_end = (
            _pt_b_start[0] + 5 * _pin_x_offset,
            _pt_b_start[1],
            _pt_b_start[2],
        )

        axis_pts = [[_pt_a_start, _pt_a_end], [_pt_b_start, _pt_b_end]]

        pin_pts = [
            [
                (_pt_a_end[0] + transform[0], _pt_a_end[1], _pt_a_end[2]),
                (
                    _pt_a_end[0] + _pin_length + transform[0],
                    _pt_a_end[1],
                    _pt_a_end[2],
                ),
            ],
            [
                (_pt_b_end[0] + transform[0], _pt_b_end[1], _pt_b_end[2]),
                (
                    _pt_b_end[0] + _pin_length + transform[0],
                    _pt_b_end[1],
                    _pt_b_end[2],
                ),
            ],
        ]
        # dodaje punkty do laczen w szerokich szafkach, do osadzenia bolcow
        pins['axis'] = [
            [self.get_pt_iso(p[0], direction), self.get_pt_iso(p[1], direction)]
            for p in axis_pts
        ]
        pins['pins'] = [
            [self.get_pt_iso(p[0], direction), self.get_pt_iso(p[1], direction)]
            for p in pin_pts
        ]

        return pins

    def get_element_handle(
        self,
        direction,
        transform=None,
    ):
        """
        draws the handle on door or drawer,
        :param shelf_depth:
        :param direction:
        :return:
        """
        if not transform:
            transform = [0, 0, 0]
        handle = dict(axis=None)
        _door_width = Ivy_Settings.doors_handle_width
        _door_offset = Ivy_Settings.doors_handle_offset
        _door_handle_type02_length = Ivy_Settings.doors_handle_length_type02

        if self.ELEM_TYPE == 'D':
            if self.shelf_type == 0:
                # handle line on doors is vertical
                if self.direction == 1:
                    _pt_start = (
                        self.x2 - _door_width + transform[0],
                        self.y2 - _door_offset + transform[1],
                        0 + transform[2],
                    )
                    _pt_end = (
                        _pt_start[0] + transform[0],
                        self.y1 + _door_offset + transform[1],
                        _pt_start[2] + transform[2],
                    )
                else:
                    _pt_start = (
                        self.x1 + _door_width + transform[0],
                        self.y2 - _door_offset + transform[1],
                        0 + transform[2],
                    )
                    _pt_end = (
                        _pt_start[0] + transform[0],
                        self.y1 + _door_offset + transform[1],
                        _pt_start[2] + transform[2],
                    )
            else:
                # handle line on doors in type 02 is horizontal
                _door_width = old_div(_door_width, 4)
                _door_offset = _door_offset * 1.5
                _pt_start = (
                    self.x1 + _door_offset + transform[0],
                    self.y2 - _door_offset + transform[1],
                    0 + transform[2],
                )
                _pt_end = (
                    self.x1 + _door_handle_type02_length + transform[0],
                    _pt_start[1] + transform[1],
                    _pt_start[2] + transform[2],
                )

        elif self.ELEM_TYPE == 'T':
            if self.shelf_type == 0:
                # handle line on drawers is horizontal and has no direction
                _door_width = old_div(_door_width, 2)
                _door_offset = _door_offset * 1.5
                _pt_start = (
                    self.x2 - _door_width + transform[0],
                    self.y2 - _door_offset + transform[1],
                    0 + transform[2],
                )
                _pt_end = (
                    self.x1 + _door_width + transform[0],
                    _pt_start[1] + transform[1],
                    _pt_start[2] + transform[2],
                )
            else:
                # handle line on drawers in type 02 is horizontal
                _door_width = old_div(_door_width, 4)
                _door_offset = _door_offset * 2
                _pt_start = (
                    self.x1 + _door_offset + transform[0],
                    self.y2 - _door_offset + transform[1],
                    0 + transform[2],
                )
                _pt_end = (
                    self.x1 + _door_handle_type02_length + transform[0],
                    _pt_start[1] + transform[1],
                    _pt_start[2] + transform[2],
                )

        axis_pts = [[_pt_start, _pt_end]]
        # dodaje punkty do laczen w szerokich szafkach, do osadzenia bolcow
        handle['axis'] = [
            [self.get_pt_iso(p[0], direction), self.get_pt_iso(p[1], direction)]
            for p in axis_pts
        ]

        return handle

    def get_grommets_geometries(self, transform):
        transform = transform or [0, 0, 0]

        grommets = [
            fitting for fitting in self.fittings if fitting.get('name') == 'grommet'
        ]

        if not grommets:
            return []
        unit_convert = 100
        grommets_geometries = []
        for grommet in grommets:
            grommet_middle = {
                'x': (grommet['x_domain'][0] + grommet['x_domain'][1]) / 2,
                'y': (grommet['y_domain'][0] + grommet['y_domain'][1]) / 2,
                'z': (grommet['z_domain'][0] + grommet['z_domain'][1]) / 2,
            }
            grommets_geometries.append(
                {
                    'grommet_size': (grommet['x_domain'][1] - grommet['x_domain'][0])
                    / unit_convert,
                    'x_offset': grommet_middle['x'] / unit_convert + transform[0],
                    'y_offset': grommet_middle['y'] / unit_convert + transform[1],
                    'z_offset': grommet_middle['z'] / unit_convert + transform[2],
                }
            )
        return grommets_geometries

    def get_grommets(self, direction, transform=None):
        grommets_geometries = self.get_grommets_geometries(transform)
        grommets = []
        for grommet in grommets_geometries:
            polygon = get_circle_geometry(size_factor=grommet['grommet_size'] / 2)
            if self.ELEM_TYPE == 'B':
                grommet_points = [
                    self.get_pt_iso(
                        [
                            pt['x'] + grommet['x_offset'],
                            pt['y'] + grommet['y_offset'],
                            grommet['z_offset'],
                        ],
                        direction,
                    )
                    for pt in polygon
                ]

            else:  # grommets in desk horizontals
                grommet_points = [
                    self.get_pt_iso(
                        [
                            pt['x'] + grommet['x_offset'],
                            grommet['y_offset'],
                            pt['y'] + grommet['z_offset'],
                        ],
                        direction,
                    )
                    for pt in polygon
                ]
            grommets.append(grommet_points)
        return grommets

    def get_desk_beam(self, transform=None):
        desk_beam = self.get_fitting_by_name('desk_beam')[0]
        transform = transform or [0, 0, 0, 0]
        points = self.get_vertex_from_domain(
            desk_beam['x_domain'],
            desk_beam['y_domain'],
            desk_beam['z_domain'],
            transform=transform,
        )
        desk_beam_offset = 7
        return [
            points['p1'],
            points['p2'],
            [points['p3'][0] - desk_beam_offset, points['p3'][1], points['p3'][2]],
            [points['p4'][0] - desk_beam_offset, points['p4'][1], points['p4'][2]],
        ]

    def get_element_iso(
        self,
        direction,
        position,
        state,
        shelf_height=1,
        explode=0,
        pin_position='out',
    ):
        iso_data = self.get_element_iso_data(
            direction,
            position,
            state,
            shelf_height=shelf_height,
            explode=explode,
        )
        transform = self.get_element_transform(position=position)
        polygons = []
        textures = []
        iso = {}
        edge_midpoint = []
        connectors_rotated = []
        handle = []
        grommets = []

        doors_angle = Ivy_Settings.doors_assembly_angle

        # add desk beam face to horizontal from fitting domains
        if self.ELEM_TYPE == 'H' and self.get_fitting_by_name('desk_beam'):
            if direction == 0:
                desk_beam_points = self.get_desk_beam(transform=transform)
                desk_polygon = []
                for pt in desk_beam_points:
                    _pt = self.get_pt_iso(pt, direction)
                    desk_polygon.append(_pt)
                polygons.append(desk_polygon)
            grommets = self.get_grommets(direction, transform)

        for face in iso_data['faces']:
            poly = []

            if self.ELEM_TYPE == 'D' and position == 'assembly':
                if direction == 0:  # jesli szafka stoi pionowo
                    _x = (
                        self.x1 if self.direction == 1 else self.x2
                    )  # jesli drzwi otwieraja sie w lewo lub prawo
                    _angle = doors_angle if self.direction == 0 else -doors_angle
                    for pt in face['pts']:
                        _pt_backs = Ivy_isometric.IvyIsometricView.rotate_point(
                            pt,
                            rotation_axis='Y',
                            angle=_angle,
                            origin_pt=[_x, self.y1, 0],
                        )
                        _pt = self.get_pt_iso(_pt_backs, direction)
                        poly.append(_pt)
                        handle = self.get_element_handle(direction)
                else:
                    for pt in face['pts']:
                        _pt = self.get_pt_iso(pt, direction)
                        poly.append(_pt)
            else:
                for pt in face['pts']:
                    _pt = self.get_pt_iso(pt, direction)
                    poly.append(_pt)
                if self.ELEM_TYPE in 'DT' and position != 'exploded_assembly':
                    handle = self.get_element_handle(direction, transform=transform)

            polygons.append(poly)
            if face['type'] == 'right':
                edge_midpoint = Ivy_isometric.IvyIsometricView.get_polygon_center(
                    face['pts']
                )
                edge_midpoint = self.get_pt_iso(edge_midpoint, direction)
                if self.ELEM_TYPE == 'H':
                    connectors_rotated = self.get_pins(
                        direction, pin_position, transform
                    )

        if self.ELEM_TYPE == 'B' and direction == 1:
            if self.grommet_type:
                grommets = self.get_grommets(direction, transform)

        centroid = self.get_pt_iso(iso_data['centroid'], direction)
        if iso_data['textures']:
            for texture in iso_data['textures']:
                _texture = []
                for pt in texture['points']:
                    _pt = self.get_pt_iso(pt, direction)
                    _texture.append(_pt)
                textures.append(_texture)
            iso['tag_polygons'] = textures

        iso['polygons'] = polygons
        iso['centroid'] = centroid
        iso['edge_midpoint'] = edge_midpoint
        iso['connectors_pts'] = connectors_rotated
        iso['handle'] = handle
        iso['grommets'] = grommets
        return iso

    def get_element_draw_data(
        self,
        shelf_depth,
        direction,
        position,
        state='neutral',
        pack_number='-',
        additional_text='',
        scale=1,
        shelf_height=1,
        explode=0,
        text_offset=1,
        has_fixing=None,
        has_joint=None,
        has_connectors=None,
        pin_position='out',
        is_charlie=False,
    ):
        element_iso = self.get_element_iso(
            direction,
            position,
            state=state,
            shelf_height=shelf_height,
            explode=explode,
            pin_position=pin_position,
        )
        if state == 'fully_tagged':
            tags = element_iso['tag_polygons']
        else:
            tags = []
        element_type = self.__class__.__name__
        text_position = ()

        if state == 'tagged' and position not in ['exploded', 'exploded_assembly']:
            tag_position = element_iso['centroid']
        elif state in {'fully_tagged', 'selected_draw_last'}:
            if element_type == 'Horizontal':
                tag_position = element_iso['centroid']
            elif element_type == 'Vertical':
                tag_position = (
                    element_iso['centroid'][0],
                    element_iso['centroid'][1] - shelf_depth * 1.15,
                )
            elif element_type in ['Support', 'Backs']:
                tag_position = (
                    element_iso['centroid'][0],
                    element_iso['centroid'][1] - shelf_depth,
                )
            elif element_type == 'Plinth':
                tag_position = (
                    element_iso['centroid'][0],
                    element_iso['centroid'][1] + shelf_depth,
                )
            else:
                tag_position = element_iso['centroid']

        elif position == 'exploded' and state == 'tagged':
            if element_type == 'Horizontal':
                tag_position = (
                    element_iso['centroid'][0],
                    element_iso['centroid'][1],
                )
                text_position = (tag_position[0] + text_offset, tag_position[1])
            else:
                tag_position = (
                    element_iso['centroid'][0],
                    element_iso['centroid'][1] - 3 * text_offset,
                )
                text_position = (
                    element_iso['centroid'][0] + text_offset,
                    tag_position[1],
                )

        elif position == 'exploded_assembly' and state == 'tagged':
            _polygons = element_iso['polygons']
            _pts_flat = [
                pt for pts in _polygons for pt in pts
            ]  # splaszcz liste wszystkich pkt

            _center = Ivy_isometric.IvyIsometricView.get_polygon_center(
                _pts_flat
            )  # znajdz srodek calego elementy
            polygon_center_y = _center[1]
            if element_type:
                text_position = (
                    max([y[0] for x in element_iso['polygons'] for y in x])
                    + text_offset,
                    polygon_center_y,
                )
                tag_position = (
                    min([y[0] for x in element_iso['polygons'] for y in x])
                    - text_offset,
                    polygon_center_y,
                )
            else:
                text_position = (_polygons[2][0][0] + text_offset, polygon_center_y)
                tag_position = (_polygons[0][0][0] - text_offset, polygon_center_y)

        else:
            tag_position = element_iso['centroid']
            text_position = tag_position

        if is_charlie:  # in charlie we alter horizontal tag position for readability
            if state == 'tagged' and element_type == 'Horizontal':
                tag_position = (
                    element_iso['centroid'][0] - text_offset * 0.5,
                    element_iso['centroid'][1] - shelf_depth * 0.5,
                )
                text_position = tag_position

        # wall fixing
        if has_fixing:
            fix = [
                self.get_pt_iso(p, direction) for p in self._get_wall_fitting_points()
            ]
            fix_side = self.get_wall_fittings_side()
        else:
            fix = None
            fix_side = None

        # polaczenie horizontala + horizontal
        joint = None
        connectors = None
        if has_joint:
            if element_type == 'Horizontal' and self.horizontal_connected:
                for j in self.horizontal_connected:
                    if j['side'] == 'right':
                        joint = element_iso['edge_midpoint']
        if has_connectors and self.ELEM_TYPE == 'H' and self.joint_right > 0:
            connectors = element_iso['connectors_pts']

        tag_name = self.surname
        draw_data = {
            'polygons': element_iso['polygons'],
            'name': self.name,
            'surname': tag_name,
            'type': element_type,
            'tag_position': tag_position,
            'state': state,
            'tag_polygons': tags,
            'pack_number': pack_number,
            'text': additional_text,
            'position': position,
            'text_position': text_position,
            'fixing': fix,
            'fixing_side': fix_side,
            'joint': joint,
            'connectors': connectors,
            'handle': None,
            'grommets': element_iso.get('grommets'),
        }
        return draw_data

    def _get_new_fittings(self):
        return [
            fitting for fitting in self.fittings if fitting['name'] == 'wall_fitting'
        ]

    def _get_old_fittings(self):
        return (
            [fitting for fitting in self.additional['wall_fittings']['points']]
            if 'wall_fittings' in self.additional
            else []
        )

    def _get_wall_fitting_points(self):
        new_fittings = self._get_new_fittings()
        fittings_y = self.unit_converter(
            sum(self.y_domain) / 2,
            to_unit='mm',
            to_int=False,
        )
        fittings_y += -9 if self.get_wall_fittings_side() == 'bottom' else 9
        if new_fittings:
            points = [
                (
                    self.unit_converter(
                        sum(fitting['x_domain']) / 2,
                        to_unit='mm',
                        to_int=False,
                    ),
                    fittings_y,
                    250,
                )
                for fitting in new_fittings
            ]
        else:
            fixing = self.additional['wall_fittings']
            points = [
                (
                    self.unit_converter(p[0], to_unit='mm', to_int=False),
                    fittings_y,
                    250,
                )
                for p in fixing['points']
            ]
        return points

    def get_wall_fittings_count(self):
        return len(self._get_new_fittings()) or len(self._get_old_fittings())

    def get_wall_fittings_side(self):
        new_fittings = self._get_new_fittings()
        if new_fittings:
            return 'top' if new_fittings[0]['connected_to'] == 2 else 'bottom'
        elif 'wall_fitting' in self.additional:
            return self.additional['wall_fittings']['side']
        return None
