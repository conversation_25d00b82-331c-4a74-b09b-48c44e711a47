import enum

from typing import NamedTuple

from producers.gh.moduly_instrukcja.enums import AssemblyStyle


class VerticalSolo(enum.IntEnum):

    DOES_NOT_EXIST = 0
    LEFT = 1
    RIGHT = 2


class ManualManager(NamedTuple):
    """Prepare some data for creating manual flow scenario."""

    # TODO, just a namedtuple for now, use it for creating all manual scenarios

    has_doors: bool
    has_drawers: bool
    has_hafele_hinges: bool
    has_inserts: bool
    module_zero: list
    modules: list
    modules_amount: int
    rows_type: int
    shelf_type: int
    fixing: int
    fixing_raptor: bool
    long_legs_modules: set
    has_plinth: bool = False
    assembly_style: int = AssemblyStyle.DORIC
    has_high_doors: bool = False
    is_desk: bool = False
    desk_solo_vertical: VerticalSolo = VerticalSolo.DOES_NOT_EXIST
    is_charlie: bool = False
