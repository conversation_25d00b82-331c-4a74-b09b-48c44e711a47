import qrcode


def get_qrcode_matrix(string_to_encode: str) -> list:
    qr = qrcode.QRCode(border=0)
    qr.add_data(string_to_encode)
    qr.make(fit=True)
    return qr.get_matrix()


def get_qrcode_rectangles(string_to_encode: str, grid_size: int = 10) -> list:
    qr_matrix = get_qrcode_matrix(string_to_encode)
    rectangles = []
    for row_index, row in enumerate(qr_matrix):
        for index, fill_value in enumerate(row):
            x = index * grid_size
            y = row_index * grid_size
            fill = ['white', 'black'][fill_value]
            rect = {'x': x, 'y': y, 'fill': fill}
            rectangles.append(rect)
    return rectangles
