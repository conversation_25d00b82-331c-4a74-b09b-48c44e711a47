from time import sleep
from typing import Optional

from django.conf import settings
from django.core.management import BaseCommand
from django.db.models import Count

import requests

from producers.choices import BatchNeededActions
from producers.enums import FileStatus
from producers.models import ProductBatch
from producers.services import ProductBatchService


class Command(BaseCommand):
    help = 'Check if all files are recalculated for batch and products in some time.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--min-batch-size',
            help='Minimum batch size which be collected for recalculation',
            type=int,
            default=10,
        )
        parser.add_argument(
            '--number-of-batches',
            help='Number of batches which be collected for recalculation',
            type=int,
            default=20,
        )
        parser.add_argument(
            '--time-to-wait',
            help=(
                'Time in seconds to wait for the files to be generated after '
                + 'the recalculation has been called '
                + '(default calculated automatically)'
            ),
            type=int,
            default=None,
        )

    def handle(self, *args, **options):
        self.recalculate_last_batches_with_products(
            min_batch_size=options['min_batch_size'],
            number_of_batches=options['number_of_batches'],
            time_to_wait=options['time_to_wait'],
        )

    def recalculate_last_batches_with_products(
        self,
        min_batch_size: int = 10,
        number_of_batches: int = 20,
        time_to_wait: Optional[int] = None,
    ) -> None:
        batches = (
            ProductBatch.objects.prefetch_related(
                'batch_details_jetty',
                'batch_details_watty',
                'batch_items',
                'batch_items__product_details_jetty',
                'batch_items__product_details_watty',
            )
            .annotate(
                no_products=Count('batch_items'),
            )
            .filter(no_products__gte=min_batch_size)
            .order_by('-pk')
        )[:number_of_batches]

        no_products = self._run_recalculation(batches)

        time_to_recalculate_one_batch = 30
        time_to_recalculate_one_product = 15

        time_to_wait_for_batches = len(batches) * time_to_recalculate_one_batch
        time_to_wait_for_products = no_products * time_to_recalculate_one_product

        if time_to_wait is None:
            time_to_wait = max(
                time_to_wait_for_batches,
                time_to_wait_for_products,
            )
        sleep(time_to_wait)

        (
            missing_files_for_batches,
            missing_files_for_products,
            batches_actions_needed,
        ) = self._check_if_files_are_generated(batches)
        if (
            missing_files_for_batches
            or missing_files_for_products
            or batches_actions_needed
        ):
            exception_message = (
                f'Some files are missing after {time_to_wait}s',
                missing_files_for_batches,
                missing_files_for_products,
                batches_actions_needed,
            )
            if settings.SLACK_WEBHOOK:
                requests.post(
                    settings.SLACK_WEBHOOK,
                    json={
                        'text': (
                            ':piotr-teraz-to-juz-nie-chce-party: '
                            f'Something wrong with files recalculation '
                            f'with following error: {str(exception_message)}'
                        ),
                        'channel': 'files-recalculation-bot',
                        'username': 'Batch bot',
                        'icon_emoji': ':piotr-teraz-to-juz-nie-chce-party:',
                    },
                )
            raise Exception(exception_message)
        if settings.SLACK_WEBHOOK:
            requests.post(
                settings.SLACK_WEBHOOK,
                json={
                    'text': 'Success!',
                    'channel': 'files-recalculation-bot',
                    'username': 'Mati bot',
                    'icon_emoji': ':mati-hold-my-beer:',
                },
            )

    def _run_recalculation(self, batches):
        no_products = 0
        for batch in batches:
            for product in batch.batch_items.all():
                ProductBatchService._remove_old_files(product)
                ProductBatchService._generate_files_for(product)
                no_products += 1
            ProductBatchService._remove_old_files(batch)
            batch.generate_all_files()
        return no_products

    def _check_if_files_are_generated(self, batches):
        missing_files_for_batches = []
        missing_files_for_products = []
        batches_actions_needed = []
        for batch in batches:
            missing_files_for_batches.extend(
                self._get_missing_files_for_objects(batch),
            )
            if batch.actions_needed != BatchNeededActions.NO_ACTION_NEEDED:
                batches_actions_needed.append(
                    f'Batch {batch} has wrong status {batch.actions_needed}'
                )
            for product in batch.batch_items.all():
                missing_files_for_products.extend(
                    self._get_missing_files_for_objects(product),
                )

        return (
            missing_files_for_batches,
            missing_files_for_products,
            batches_actions_needed,
        )

    def _get_missing_files_for_objects(self, obj):
        missing_files = []
        for field_name in obj.details._file_fields:
            field = getattr(obj.details, field_name)
            file_status = obj.details.get_file_status(field_name)
            if (
                file_status == FileStatus.READY
                and field
                and not field.storage.exists(field.name)
            ):
                missing_files.append(f'Missing file {field_name} for {obj}')
            elif file_status > FileStatus.READY:
                missing_files.append(
                    f'Wrong file status {file_status} for {field_name} for {obj}',
                )
        return missing_files
