# Generated by Django 4.1.13 on 2024-11-12 11:48

import django.core.files.storage

from django.db import (
    migrations,
    models,
)

import producers.utils


class Migration(migrations.Migration):

    dependencies = [
        ('producers', '0110_autobatchingmailingconfiguration_attach_t25_legs_report'),
    ]

    operations = [
        migrations.AlterField(
            model_name='productbatchdetailsjetty',
            name='nesting_handle_blende',
            field=models.FileField(
                blank=True,
                max_length=400,
                null=True,
                storage=django.core.files.storage.FileSystemStorage(),
                upload_to=producers.utils.RandomizedUploadTo(
                    'producers/batch/nesting_handle_blende/%Y/%m'
                ),
                verbose_name='Rozkroj blendy uchwytu',
            ),
        ),
    ]
