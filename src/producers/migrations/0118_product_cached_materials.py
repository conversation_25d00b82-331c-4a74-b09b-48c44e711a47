# Generated by Django 4.1.13 on 2025-02-13 15:40

from django.db import (
    migrations,
    models,
)


def cache_sotty_materials(apps, schema_editor):
    Product = apps.get_model('producers', 'Product')
    Sotty = apps.get_model('gallery', 'Sotty')
    for product in Product.objects.filter(cached_product_type='sotty'):
        try:
            related_sotty = Sotty.objects.get(pk=product.order_item.object_id)
            product.cached_materials = related_sotty.materials
            product.save()
        except (Sotty.DoesNotExist, AttributeError):
            product.cached_materials = []
            product.save()


class Migration(migrations.Migration):

    dependencies = [
        ('producers', '0117_autobatchingmailingconfiguration_attach_s93_karton'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='cached_materials',
            field=models.JSONField(
                db_index=True, default=list, editable=False, null=True
            ),
        ),
        migrations.RunPython(cache_sotty_materials, migrations.RunPython.noop),
    ]
