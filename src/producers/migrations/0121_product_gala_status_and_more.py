# Generated by Django 4.1.13 on 2025-03-28 09:32

import django.core.files.storage
import django.db.models.deletion

from django.db import (
    migrations,
    models,
)

import producers.utils


class Migration(migrations.Migration):

    dependencies = [
        ('producers', '0120_productbatchdetailswatty_labels_adapters'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='gala_status',
            field=models.CharField(blank=True, default='', max_length=100),
        ),
        migrations.AddField(
            model_name='productbatch',
            name='withdrawn_from_production',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='productdetailssotty',
            name='labels_packaging',
            field=models.FileField(
                blank=True,
                max_length=400,
                storage=django.core.files.storage.FileSystemStorage(),
                upload_to=producers.utils.RandomizedUploadTo(
                    'producers/products/labels_packaging/%Y/%m'
                ),
                verbose_name='Etykiety pakowanie',
            ),
        ),
        migrations.CreateModel(
            name='GalaProductStatusHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('new_status', models.CharField(max_length=100)),
                (
                    'previous_status',
                    models.CharField(blank=True, default='', max_length=100),
                ),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'product',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='gala_status_history',
                        to='producers.product',
                    ),
                ),
            ],
        ),
    ]
