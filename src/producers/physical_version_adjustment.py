import typing

from custom.enums import PhysicalProductVersion as PPV
from producers.models import Product

KEEP_ORIGINAL = 'keep_original'

DREWTUR = 1
MEBLEPL = 24
NOVUM = 25
S93 = 29
AMIR = 30
INEX = 31
CENTERMEBEL = 33

TYPE01 = 'T1'
TYPE02 = 'T2'
TYPE01v = 'F1'
TYPE03 = 'W3'
TYPE13 = 'W13'
TYPE13v = 'F13'
TYPE23 = 'T23'
TYPE24 = 'T24'
TYPE25 = 'T25'


class AdjusterParameters(typing.NamedTuple):
    manufacturer: int | None = None
    shelf_type: str | None = None
    physical_product_version: PPV | None = None
    material: int | None = None

    @property
    def match_lookups(self):
        """
        Generates possible lookups for the given parameters.

        The order of the lookups is important - more closely matching lookups
        will appear first.

        """
        yield AdjusterParameters(
            self.manufacturer,
            self.shelf_type,
            self.physical_product_version,
            self.material,
        )
        yield AdjusterParameters(
            self.manufacturer,
            self.shelf_type,
            self.physical_product_version,
        )
        yield AdjusterParameters(
            self.manufacturer,
            physical_product_version=self.physical_product_version,
        )
        yield AdjusterParameters(
            self.manufacturer,
            self.shelf_type,
        )
        yield AdjusterParameters(self.manufacturer)
        yield AdjusterParameters()


class BasePPVAdjuster:
    necessary_adjustments: dict[AdjusterParameters, PPV] = {}

    def __init__(self, product):
        self.product = product

    def get_target_ppv(self) -> PPV:
        my_parameters = AdjusterParameters(
            self.product.manufactor_id,
            self.product.cached_shelf_type,
            PPV(self.product.cached_physical_product_version),
            self.product.cached_material,
        )

        for match_lookup in my_parameters.match_lookups:
            if match_lookup in self.necessary_adjustments:
                return self._resolve_ppv_value(self.necessary_adjustments[match_lookup])

        raise NotImplementedError(
            f'Incorrectly configured PPV adjuster for {self.product.cached_shelf_type}.'
        )

    def _resolve_ppv_value(self, suggested_version):
        if suggested_version == KEEP_ORIGINAL:
            return PPV(self.product.cached_physical_product_version)
        return max(suggested_version, self.product.cached_physical_product_version)


class JettyPPVAdjuster(BasePPVAdjuster):
    necessary_adjustments = {
        AdjusterParameters(DREWTUR, TYPE01): PPV.KARNO,
        AdjusterParameters(DREWTUR, TYPE01v): PPV.BRACHIO,
        AdjusterParameters(NOVUM, TYPE01v): PPV.BRACHIO,
        AdjusterParameters(DREWTUR, TYPE02): PPV.TRICE,
        AdjusterParameters(CENTERMEBEL, TYPE02): PPV.PACHY,
        AdjusterParameters(NOVUM, TYPE02): PPV.PACHY,
        AdjusterParameters(S93, TYPE02): PPV.PACHY,
        AdjusterParameters(MEBLEPL, TYPE02): PPV.PACHY,
        AdjusterParameters(): PPV.BRONTO,
    }


class T03PPVAdjuster(BasePPVAdjuster):
    necessary_adjustments = {
        AdjusterParameters(): KEEP_ORIGINAL,
    }


class T13PPVAdjuster(BasePPVAdjuster):
    default_target_ppv = PPV.RAPTOR

    necessary_adjustments = {
        AdjusterParameters(DREWTUR): PPV.DIPLO,
        AdjusterParameters(): PPV.RAPTOR,
    }


class Veneer13PPVAdjuster(BasePPVAdjuster):
    necessary_adjustments = {
        AdjusterParameters(): PPV.DIPLO,
    }


class MattyPPVAdjuster(BasePPVAdjuster):
    necessary_adjustments = {
        AdjusterParameters(): PPV.DIPLO,
    }


class PhysicalVersionAdjustment:
    """Adjusts physical product version of a Product during batching process."""

    def apply_to(self, product: Product) -> None:
        """PPV needs to be adjusted both in the Product and in related Jetty."""
        if product.reproduction_origin_complaint is not None:
            return  # PPV of complaints should not be changed
        if product.is_sotty:
            return
        adjuster_class = self._get_adjuster_instance(product)
        target_ppv = adjuster_class.get_target_ppv()
        if target_ppv == product.cached_physical_product_version:
            return
        gallery_object = product.order_item.order_item
        gallery_object.physical_product_version = target_ppv
        gallery_object.save()
        if product.order_item_serialized:  # just to be sure, no clue when it updates
            product.order_item_serialized['physical_product_version'] = target_ppv
        product.serialization_updater.update_cached_features()

    def _get_adjuster_instance(self, product):
        try:
            adjuster = {
                TYPE01: JettyPPVAdjuster,
                TYPE02: JettyPPVAdjuster,
                TYPE01v: JettyPPVAdjuster,
                TYPE03: T03PPVAdjuster,
                TYPE13: T13PPVAdjuster,
                TYPE13v: Veneer13PPVAdjuster,
                TYPE23: MattyPPVAdjuster,
                TYPE24: MattyPPVAdjuster,
                TYPE25: MattyPPVAdjuster,
            }[product.cached_shelf_type]
        except KeyError:
            raise NotImplementedError(
                f'No ppv adjuster for shelf type {product.cached_shelf_type}'
            )
        return adjuster(product)
