from datetime import datetime

from django.core.paginator import Paginator
from django.db.models import QuerySet

from custom.utils.report_file import ReportFile
from production_margins.choices import MeasurementUnit
from production_margins.models import CustomPricingFactor


class CustomPricingFactorChangeRequestsCSVExport:
    headers = [
        'Codename',
        'Polska nazwa',
        'Kod producenta',
        'Jednostka',
        'Cena',
        'Loss factor',
        'Data od',
        'Data do',
        'Zlecenia zmiany',
    ]

    def __init__(self, queryset: QuerySet[CustomPricingFactor]):
        self.queryset = queryset

    def generate_report_data(self) -> list:
        report_rows = [self.headers]
        for page in Paginator(self.queryset, 100):
            for obj in page.object_list:
                report_rows.append(self.get_row_data(obj))
        return report_rows

    def get_row_data(self, custom_pricing_factor):
        return [
            custom_pricing_factor.pricing_factor_item.codename
            if custom_pricing_factor.pricing_factor_item
            else '-',
            custom_pricing_factor.manufacturer_code.name
            if custom_pricing_factor.manufacturer_code
            else '-',
            custom_pricing_factor.manufacturer_code.code
            if custom_pricing_factor.manufacturer_code
            else '-',
            self.get_unit(custom_pricing_factor),
            custom_pricing_factor.price,
            custom_pricing_factor.loss_factor,
            custom_pricing_factor.date_from,
            custom_pricing_factor.date_to,
            self.get_requests_numbers(custom_pricing_factor),
        ]

    @staticmethod
    def get_unit(custom_pricing_factor):
        return MeasurementUnit(
            custom_pricing_factor.pricing_factor_item.measurement_unit
        ).to_polish_admin_display_only()

    @staticmethod
    def get_requests_numbers(custom_pricing_factor):
        return '; '.join(
            list(
                custom_pricing_factor.custompricingfactorchangerequest_set.values_list(
                    'request_number', flat=True
                )
            )
        )


def generate_response_with_custom_pricing_factor_export(
    queryset: QuerySet[CustomPricingFactor],
):
    date_today = datetime.today().strftime('%y%m%d_%H%M')
    file_name = f'custom_pricing_factors_{date_today}.csv'
    report_data = CustomPricingFactorChangeRequestsCSVExport(
        queryset
    ).generate_report_data()
    report = ReportFile.load_list_as_csv_file(report_data, file_name=file_name)

    return report.get_as_http_response()
