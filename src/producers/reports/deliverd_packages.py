from collections import defaultdict
from datetime import date
from typing import (
    Any,
)

from django.conf import settings
from django.core.paginator import Paginator
from django.db.models import (
    DateField,
    QuerySet,
    Value,
)
from django.db.models.fields.json import KeyTextTransform
from django.db.models.functions import (
    Cast,
    Coalesce,
)
from django.db.models.query_utils import Q

from complaints.models import Complaint
from custom.utils.report_file import ReportFile
from orders.enums import OrderType
from producers.choices import ProductStatus
from producers.models import (
    Product,
    ProductStatusHistory,
)
from producers.reports.dto import (
    ComplaintDTO,
    PackageReportingDTO,
)


def get_products_delivered(
    from_date: date = date(2024, 1, 1),
    manufactor_name: str | None = None,
    exclude_complaints: bool = False,
) -> QuerySet[Product]:
    product_ids = ProductStatusHistory.objects.filter(
        changed_at__gte=from_date,
        status=ProductStatus.DELIVERED_TO_CUSTOMER,
    ).values_list('product_id', flat=True)

    queryset = Product.objects.filter(
        id__in=product_ids,
        order__order_type__in=[
            OrderType.CUSTOMER,
            OrderType.B2B,
            OrderType.CUSTOM_ORDER,
        ],
    ).annotate(
        sent_date=Coalesce(
            Cast(
                KeyTextTransform(
                    'sent_to_customer', 'order__serialized_logistic_info__0'
                ),
                DateField(),
            ),
            Value('2000-01-01', output_field=DateField()),
        )
    ).filter(
        sent_date__gte=from_date
    )

    if manufactor_name:
        queryset = queryset.filter(manufactor__name=manufactor_name)

    if exclude_complaints:
        complaint_products = Complaint.objects.exclude(product_id__isnull=True).values(
            'product_id'
        )
        complaint_reproductions = Complaint.objects.exclude(
            reproduction_product_id__isnull=True
        ).values('reproduction_product_id')

        queryset = queryset.exclude(
            Q(id__in=complaint_products) | Q(id__in=complaint_reproductions)
        )

    return queryset


def get_all_elements_in_package(product: Product) -> dict[str, list[str]]:
    elements = defaultdict(list)
    serialized_elements = product.get_serialization().item.get('elements', [])

    for element in serialized_elements:
        surname = element.get('surname')
        package_info = element.get('package_info')
        if not surname or not package_info:
            continue
        pack_id = package_info['pack_id']

        components = element.get('components') or []
        components_in_package = [
            component.get('surname')
            for component in components
            if component.get('surname')
        ]

        if not components_in_package:
            elements[pack_id].append(surname)
        else:
            for component in components_in_package:
                elements[pack_id].append(component)

    return elements


def get_complained_elements(complaint: Complaint) -> list[str]:
    elements = complaint.elements['elements']
    formatted_elements = [item.split(' - ')[0].strip() for item in elements]
    features = complaint.elements['features']
    formatted_features = [item.split(' - ')[0].strip() for item in features]

    return formatted_elements + formatted_features


def is_package_complaint(
    package_elements: list[str], complaint_elements: list[str]
) -> bool:
    return bool(set(complaint_elements) & set(package_elements))


def generate_delivered_packages_count_data(
    from_date: date = date(2024, 1, 1), manufactor_name: str | None = None
) -> (list[str], list[list[Any]]):
    product_query = (
        get_products_delivered(from_date, manufactor_name)
        .order_by('id')
        .select_related('manufactor', 'order')
        .prefetch_related('reproduction_complaints', 'complaint_set')
        .order_by('sent_date')
    )

    headers = [
        'Pack id',
        'Is package complaint',
        'Product id',
        'Shelf type',
        'Order id',
        'Batch id',
        'Manufactor',
        'Paid at',
        'Sent at',
        'Delivery date',
        'Dim X',
        'Dim Y',
        'Dim Z',
        'Weight',
        'Typical issue',
        'All elements in pkg',
        'Complaint elements in pkg',
        'All elements in complaint',
        'Complaint id',
    ]
    paginator = Paginator(product_query, 1000)
    rows = []
    for page in paginator:
        for product in page.object_list:
            sent_date = product.order.serialized_logistic_info[0].get(
                'sent_to_customer', '-'
            )
            all_elements_in_package = get_all_elements_in_package(product)
            product_complaints = product.complaint_set.order_by('-id').all()
            for package in product.get_packaging():
                package_dto = PackageReportingDTO(
                    pack_id=package.pack_id,
                    product_id=product.id,
                    shelf_type=product.cached_shelf_type,
                    order_id=product.order_id,
                    batch_id=product.batch_id,
                    manufactor_name=product.manufactor.name,
                    paid_at=str(product.order.paid_at),
                    sent_date=str(sent_date),
                    delivery_date=str(product.order.get_delivery_date()) or '-',
                    dim_x=package.dim_x,
                    dim_y=package.dim_y,
                    dim_z=package.dim_z,
                    weight=package.weight,
                    all_package_elements=all_elements_in_package[package.pack_id],
                    complaints=[
                        ComplaintDTO(
                            id=complaint.id,
                            elements=complaint.elements,
                            typical_issues=(
                                complaint.typical_issues.name
                                if complaint.typical_issues
                                else ''
                            ),
                        )
                        for complaint in product_complaints
                    ],
                )
                if package.pack_id in all_elements_in_package:
                    package_dto.is_package_complaint()

                rows.append(
                    [
                        package_dto.pack_id,
                        package_dto.is_complaint,
                        package_dto.product_id,
                        package_dto.shelf_type,
                        package_dto.order_id,
                        package_dto.batch_id,
                        package_dto.manufactor_name,
                        package_dto.paid_at,
                        package_dto.sent_date,
                        package_dto.delivery_date,
                        package_dto.dim_x,
                        package_dto.dim_y,
                        package_dto.dim_z,
                        package_dto.weight,
                        package_dto.typical_issue,
                        package_dto.get_all_elements_in_package(),
                        package_dto.get_common_elements_in_package(),
                        package_dto.get_complaint_elements(),
                        package_dto.complaint_id,
                    ]
                )
    return headers, rows


def generate_delivered_products_data(
    from_date: date = date(2024, 1, 1), manufactor_name: str | None = None
) -> (list[str], list[list[Any]]):
    product_query = (
        get_products_delivered(from_date, manufactor_name, exclude_complaints=True)
        .order_by('id')
        .select_related('manufactor', 'order')
        .prefetch_related('reproduction_complaints', 'complaint_set')
        .order_by('sent_date')
    )

    headers = [
        'Product id',
        'Shelf type',
        'Order id',
        'Batch id',
        'Manufactor',
        'Paid at',
        'Sent at',
        'Delivery date',
        'Weight (brutto)',
    ]
    paginator = Paginator(product_query, 1000)
    rows = []
    for page in paginator:
        for product in page.object_list:
            sent_date = product.order.serialized_logistic_info[0].get(
                'sent_to_customer', '-'
            )
            rows.append(
                [
                    product.id,
                    product.cached_shelf_type,
                    product.order_id,
                    product.batch_id,
                    product.manufactor.name if product.manufactor else '-',
                    product.order.paid_at,
                    sent_date,
                    product.order.get_delivery_date() or '-',
                    product.get_weight_brutto(),
                ]
            )
    return headers, rows


def generate_and_send_report(
    report_data_generating_function,
    manufactor_name: str | None = None,
    from_date: date = date(2024, 1, 1),
) -> dict[str, list[str]] | None:
    headers, rows = report_data_generating_function(
        from_date=from_date, manufactor_name=manufactor_name
    )
    return {
        'header': headers,
        'content': rows
    }
