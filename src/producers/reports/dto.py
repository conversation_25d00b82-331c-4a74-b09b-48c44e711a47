from dataclasses import (
    dataclass,
    field,
)


@dataclass
class ComplaintDTO:
    id: int
    elements: dict[str, list[str] | dict]
    typical_issues: str = ''

    def get_complained_elements(self) -> list[str]:
        formatted_elements = [
            item.split(' - ')[0].strip() for item in self.elements['elements']
        ]
        formatted_features = [
            item.split(' - ')[0].strip() for item in self.elements['features']
        ]

        return formatted_elements + formatted_features


@dataclass
class PackageReportingDTO:
    dim_x: float
    dim_y: float
    dim_z: float
    pack_id: int
    product_id: int
    weight: float
    order_id: int
    batch_id: int | None = None
    is_complaint: bool = False
    all_package_elements: list[str] = field(default_factory=list)
    common_elements: set[str] = field(default_factory=set)
    complaint_id: int | None = None
    matched_complaint: ComplaintDTO | None = None
    complaints: list[ComplaintDTO] | None = None
    manufactor_name: str | None = None
    shelf_type: str = ''
    typical_issue: str = ''
    paid_at: str = ''
    sent_date: str = ''
    delivery_date: str = ''

    def is_package_complaint(self) -> bool:
        for complaint in self.complaints:
            complaint_elements: list[str] = complaint.get_complained_elements()
            self.is_complaint = bool(
                set(complaint_elements) & set(self.all_package_elements)
            )
            if self.is_complaint:
                self.matched_complaint = complaint
                self.common_elements = set(self.all_package_elements) & set(
                    complaint_elements
                )
                self.complaint_id = complaint.id
                self.typical_issue = complaint.typical_issues
                return True
        return False

    def get_all_elements_in_package(self) -> str:
        return ','.join(self.all_package_elements) if self.all_package_elements else ''

    def get_common_elements_in_package(self) -> str:
        return ','.join(self.common_elements) if self.common_elements else ''

    def get_complaint_elements(self) -> str:
        return (
            ','.join(self.matched_complaint.get_complained_elements())
            if self.matched_complaint
            else ''
        )
