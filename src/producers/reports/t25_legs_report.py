from datetime import (
    date,
    datetime,
)
from typing import List

from django.db.models import (
    Q,
    QuerySet,
)

import openpyxl
import pandas as pd

from custom.utils.report_file import ReportFile
from custom.utils.xlsx import set_columns_widths
from producers.models import Manufactor
from producers.reports.new_t13_handle import ExcelWithStyleWriter
from producers.reports.speedtrans_order import (
    LARGE_COLUMN,
    MEDIUM_COLUMN,
    SMALL_COLUMN,
)
from production_margins.models import CustomPricingFactor

BASE_CODENAME = 'semiproduct_wooden_{color}_long-leg-{length}'
COLOR_TRANSLATIONS = {
    'off-white': 'złamany biały',
    'oyster-beige': 'ostrygowy beżowy',
    'pistachio-green': 'pistacjowy zielony',
    'powder-pink': 'delikatny różowy',
    'inky-black': 'atramentowy czarny',
}


def get_codename(color: str, length: int) -> str:
    return BASE_CODENAME.format(color=color, length=length)


def get_materials_usage_for_t25_legs(products: QuerySet) -> pd.DataFrame:
    data = []
    for product in products:
        serialization = product.get_serialized_product_info()
        for codename, details in serialization['materials'][
            'semiproducts_data'
        ].items():
            data.append(
                {
                    'codename': codename,
                    'product_id': product.id,
                    'usage': details['usage'],
                    'batch_id': product.batch_id,
                }
            )
    return pd.DataFrame(data)


class T25LegsReportXlsx:
    MAIN_HEADER = 'Podsumowanie zamówienia na długie nogi Type 25'
    COLUMNS_LEGS_USAGE = ['l.p.', 'długość nogi [mm]', 'ilość (szt.)']
    COLUMNS_LEGS_USAGE_PER_ITEMS = [
        'l.p.',
        'długość nogi [mm]',
        'ilość (szt.)',
        'item ID',
        'batch ID',
        'zaznacz',
    ]
    LENGTHS = [104, 300, 400, 500, 600, 700, 800, 900, 1000, 1100, 1200, 1300]

    def __init__(self, products: QuerySet):
        self.usage_df: pd.DataFrame = get_materials_usage_for_t25_legs(products)
        self.file_name: str = self._get_file_name(products)
        self.workbook: openpyxl.Workbook = openpyxl.Workbook()
        worksheet: openpyxl.worksheet.worksheet.Worksheet = self.workbook.active
        worksheet.title = self.file_name
        self._set_columns_style(worksheet)
        self.writer: ExcelWithStyleWriter = ExcelWithStyleWriter(worksheet)
        self.row: int = 1

    def generate_report(self) -> ReportFile:
        self._set_main_header_style()
        self._write_section_header('A. Podsumowanie po długościach')
        self._write_legs_usage_per_colors()

        self._write_section_header('B. Podsumowanie wg itemów')
        self._write_legs_usage_with_items_info()

        return ReportFile.load_workbook(self.workbook, file_name=self.file_name)

    def _set_main_header_style(self) -> None:
        self.writer.top_header(row=self.row, column=1, value='')
        self.writer.top_header(
            row=self.row,
            column=2,
            value=f'ZAMÓWIENIE Z DNIA {datetime.now():%Y-%m-%d}',
        )
        self.writer.top_header(row=self.row, column=13, value='tylko')
        self.writer.merge_cells(
            start_row=self.row,
            end_row=self.row,
            start_column=2,
            end_column=13,
        )
        self.row += 1
        self.writer.write_section_header(self.MAIN_HEADER, self.row, 1, column_width=12)
        self.row += 1

    @classmethod
    def _set_columns_style(
        cls, worksheet: openpyxl.worksheet.worksheet.Worksheet
    ) -> None:
        set_columns_widths(
            worksheet,
            {
                'A': SMALL_COLUMN,
                'B': LARGE_COLUMN,
                'C': MEDIUM_COLUMN,
                'D': MEDIUM_COLUMN,
                'E': MEDIUM_COLUMN,
                'F': LARGE_COLUMN,
                'G': MEDIUM_COLUMN,
                'H': SMALL_COLUMN,
                'I': LARGE_COLUMN,
                'J': MEDIUM_COLUMN,
                'K': MEDIUM_COLUMN,
                'L': MEDIUM_COLUMN,
                'M': MEDIUM_COLUMN,
            },
        )

    def _get_legs_usage_info_for_color(self, color: str) -> List[dict]:
        color_usage = []
        for idx, length in enumerate(self.LENGTHS, start=1):
            codename = get_codename(color, length)
            usage_sum = self.usage_df[self.usage_df['codename'] == codename][
                'usage'
            ].sum()
            color_usage.append(
                {
                    self.COLUMNS_LEGS_USAGE[0]: idx,
                    self.COLUMNS_LEGS_USAGE[1]: length,
                    self.COLUMNS_LEGS_USAGE[2]: usage_sum,
                }
            )
        return color_usage

    def _get_legs_usage_info_for_color_with_items(self, color: str) -> List[dict]:
        color_usage = []
        index = 1
        grouped_df = (
            self.usage_df.groupby(['codename', 'product_id'])
            .agg({'usage': 'sum', 'batch_id': 'first'})
            .reset_index()
        )
        for length in self.LENGTHS:
            codename = get_codename(color, length)
            df_filtered = grouped_df[grouped_df['codename'] == codename]
            for _, row in df_filtered.iterrows():
                color_usage.append(
                    {
                        self.COLUMNS_LEGS_USAGE_PER_ITEMS[0]: index,
                        self.COLUMNS_LEGS_USAGE_PER_ITEMS[1]: length,
                        self.COLUMNS_LEGS_USAGE_PER_ITEMS[2]: row['usage'],
                        self.COLUMNS_LEGS_USAGE_PER_ITEMS[3]: row['product_id'],
                        self.COLUMNS_LEGS_USAGE_PER_ITEMS[4]: row['batch_id'],
                        self.COLUMNS_LEGS_USAGE_PER_ITEMS[5]: '',
                    }
                )
                index += 1
        return color_usage

    def _write_legs_usage_per_colors(self) -> None:
        tables_in_one_line = 3
        new_row = 0
        for idx, color in enumerate(COLOR_TRANSLATIONS.keys()):
            start_column = 1 + (idx % tables_in_one_line) * 4
            if new_row and start_column == 1:
                self.row = new_row + 2
            temp_row = self.row
            self.writer.write_batch_header(
                self._get_color_table_header(color),
                self.row,
                start_column,
                column_width=2,
            )
            temp_row += 2
            usage_for_color = self._get_legs_usage_info_for_color(color)
            temp_row = self.writer.write_table_from_dict(
                usage_for_color, temp_row, start_column, self.COLUMNS_LEGS_USAGE
            )
            temp_row += 1
            new_row = max(temp_row, new_row)
        self.row = new_row

    def _write_legs_usage_with_items_info(self) -> None:
        tables_in_one_line = 2
        new_row = 0
        for idx, color in enumerate(COLOR_TRANSLATIONS.keys()):
            start_column = 1 + (idx % tables_in_one_line) * 7
            if new_row and start_column == 1:
                self.row = new_row + 2
            temp_row = self.row
            self.writer.write_batch_header(
                self._get_color_table_header(color),
                self.row,
                start_column,
                column_width=5,
            )
            temp_row += 2
            usage_for_color = self._get_legs_usage_info_for_color_with_items(color)
            temp_row = self.writer.write_table_from_dict(
                usage_for_color,
                temp_row,
                start_column,
                self.COLUMNS_LEGS_USAGE_PER_ITEMS,
            )
            temp_row += 1
            new_row = max(temp_row, new_row)
        self.row = new_row

    def _write_section_header(self, title: str) -> None:
        self.writer.write_section_header(
            title,
            self.row,
            1,
            column_width=12,
        )
        self.row += 2

    @staticmethod
    def _get_file_name(products: QuerySet) -> str:
        batch_ids = products.values_list('batch_id', flat=True)
        return f'Type25_legs_B{min(batch_ids)}-B{max(batch_ids)}.xlsx'

    @staticmethod
    def _get_color_table_header(color: str) -> str:
        return f'Kolor {color.replace("-", " ")} / {COLOR_TRANSLATIONS[color]}'


class T25LegsReportForSourcingCSV:
    LENGTHS = [300, 400, 500, 600, 700, 800, 900, 1000, 1100, 1200, 1300]
    DIFFERENT_LEG_LENGTHS = [104]
    HEADERS = [
        'wysokość finalna nogi z rysunku',
        'wykończenie nogi',
        'numer TY',
        'nazwa',
        'codename',
        'jednostka',
        'ilość do zamówienia',
    ]

    def __init__(self, products: QuerySet):
        self.usage_df: pd.DataFrame = get_materials_usage_for_t25_legs(products)
        self.manufacturer: Manufactor = products[0].manufactor
        self.file_name: str = self._get_file_name(products)

    def generate_report(self) -> ReportFile:
        report_data = self._get_report_data()
        return ReportFile.load_list_as_csv_file(
            data=report_data,
            file_name=self.file_name,
            headers=self.HEADERS,
        )

    def _get_report_data(self) -> List[list[int | str]]:
        report_data = []
        report_data.extend(self._get_usage_data(self.LENGTHS))
        report_data.extend(self._get_usage_data(self.DIFFERENT_LEG_LENGTHS))
        return report_data

    def _get_usage_data(self, lengths: List[int]) -> List[list[int | str]]:
        usage_data = []
        for color in COLOR_TRANSLATIONS.keys():
            for length in lengths:
                codename = get_codename(color, length)
                usage = self.usage_df[self.usage_df['codename'] == codename][
                    'usage'
                ].sum()
                if usage > 0:
                    usage_data.append(
                        self._get_row_data(color, length, codename, usage)
                    )
        return usage_data

    def _get_related_custom_pricing_factor(self, codename: str) -> CustomPricingFactor:
        today = date.today()
        custom_pricing_factor = (
            CustomPricingFactor.objects.filter(
                manufactor=self.manufacturer,
                pricing_factor_item__codename=codename,
            )
            .filter(Q(date_to__isnull=True) | Q(date_to__gt=today))
            .last()
        )
        return custom_pricing_factor

    @staticmethod
    def _get_leg_finish_info(color: str) -> str:
        return f'lakier, {COLOR_TRANSLATIONS[color]}'

    def _get_row_data(
        self, color: str, length: int, codename: str, usage: int
    ) -> list[int | str]:
        custom_pricing_factor = self._get_related_custom_pricing_factor(codename)
        return [
            length,
            self._get_leg_finish_info(color),
            custom_pricing_factor.manufacturer_code.code
            if custom_pricing_factor
            else 'related CPF not found',
            custom_pricing_factor.manufacturer_code.name
            if custom_pricing_factor
            else 'related CPF not found',
            codename,
            'szt',
            usage,
        ]

    @staticmethod
    def _get_file_name(products: QuerySet) -> str:
        batch_ids = products.values_list('batch_id', flat=True)
        return f'Zamówienie_nogi_do_T25_B{min(batch_ids)}-B{max(batch_ids)}'
