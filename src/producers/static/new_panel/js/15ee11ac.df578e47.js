(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["15ee11ac"],{"19f3":function(e,t,a){},2609:function(e,t,a){},"2a43":function(e,t,a){"use strict";var n=a("19f3"),o=a.n(n);o.a},"2fa3":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return r}));a("4917");var n=function(e){var t=window.URL.createObjectURL(new Blob([e.data])),a=document.createElement("a");a.href=t;var n=e.headers["content-disposition"],o=n.match(/filename=(.+)/);a.setAttribute("download",o[1]),document.body.appendChild(a),a.click(),a.remove(),window.URL.revokeObjectURL(t)},o=function(e){var t=document.createElement("a");t.href=e,t.click(),t.remove()},r=function(e){var t=e.completed,a=e.all;if(0===a)return"100% (0/0)";var n=(t/a*100).toFixed(2);return"".concat(n,"% (").concat(t,"/").concat(a,")")}},3125:function(e,t,a){},"3de1":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"q-pa-md"},[a("q-table",e._b({staticClass:"sticky-header-table",attrs:{title:e.title,"row-key":e.rowKey,selection:"multiple",selected:e.selected,pagination:e.pagination,"loading-label":"Pobieranie batchy...","rows-per-page-label":"Ilość na stronie:",flat:"",bordered:"",separator:"cell"},on:{"update:selected":[function(t){e.selected=t},e.updateSelected],"update:pagination":function(t){e.pagination=t},request:e.reloadTable},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,a){return{key:a,fn:function(t){return[e._t(a,null,null,t)]}}})),{key:"loading",fn:function(){return[a("q-inner-loading",{attrs:{showing:"",color:"primary"}})]},proxy:!0}],null,!0)},"q-table",{data:e.tableData,columns:e.columns,loading:e.loading,rowsPerPageOptions:[50,100,250,500,1e3]},!1))],1)},o=[],r=(a("8e6e"),a("8a81"),a("ac6a"),a("cadf"),a("06db"),a("456d"),a("c47a")),s=a.n(r);function i(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?i(a,!0).forEach((function(t){s()(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):i(a).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var l={name:"BaseTable",props:{apiUrl:{type:String,required:!0},title:{type:String,required:!0},columns:{type:Array,required:!0},rowKey:{type:String,required:!0},responseKey:{type:String,required:!0},updateLoading:{type:Function,required:!0},updateSelected:{type:Function,required:!0},filters:{type:Object,default:function(){}}},data:function(){return{pagination:{sortBy:"desc",descending:!1,page:1,rowsPerPage:100,rowsNumber:10},loading:!1,selected:[],tableData:[]}},computed:{tableFilters:function(){return this.filters}},watch:{loading:function(){this.updateLoading(this.loading)}},created:function(){this.getData(this.pagination)},methods:{reloadTable:function(e){this.getData(e.pagination);var t=e.pagination,a=t.page,n=t.rowsPerPage,o=t.sortBy,r=t.descending;this.pagination.page=a,this.pagination.rowsPerPage=n,this.pagination.sortBy=o,this.pagination.descending=r},resetSelected:function(){this.selected=[],this.updateSelected([])},getData:function(e,t){var a=this,n=e||this.pagination,o=c({},this.filters,{},t||{});this.loading=!0,this.$axios.get(this.apiUrl,{params:c({page_number:n.page,page_size:n.rowsPerPage},o)}).then((function(e){a.tableData=e.data[a.responseKey],a.pagination.rowsNumber=e.data.count,a.loading=!1}))}}},u=l,d=(a("2a43"),a("2877")),p=a("fe09"),m=Object(d["a"])(u,n,o,!1,null,null,null);t["a"]=m.exports;m.options.components=Object.assign({QTable:p["x"],QInnerLoading:p["l"]},m.options.components||{})},"410c":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("q-page",{staticClass:"q-pa-lg"},[a("div",{staticClass:"q-pa-md"},[a("div",{staticClass:"q-pa-md"},[a("div",{staticClass:"row"},[a("q-input",{staticClass:"q-ml-md q-pa-sm col-2",attrs:{label:"Codename",filled:""},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.filterData(t)}},model:{value:e.codename,callback:function(t){e.codename=t},expression:"codename"}}),a("q-input",{staticClass:"q-ml-md q-pa-sm col-2",attrs:{label:"Kod producenta",filled:""},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.filterData(t)}},model:{value:e.manufacturerCode,callback:function(t){e.manufacturerCode=t},expression:"manufacturerCode"}}),a("q-select",{staticClass:"q-pa-sm col-2",attrs:{label:"Status",options:e.statusOptions,filled:""},scopedSlots:e._u([{key:"option",fn:function(t){var n=t.itemProps,o=t.itemEvents,r=t.opt;return[a("q-item",e._g(e._b({},"q-item",n,!1),o),[a("q-item-section",[a("q-item-label",{domProps:{innerHTML:e._s(r.label)}})],1),a("q-item-section",{attrs:{side:""}},[a("q-badge",{staticClass:"q-pa-sm",attrs:{color:r.color,rounded:""}})],1)],1)]}}]),model:{value:e.status,callback:function(t){e.status=t},expression:"status"}}),a("q-checkbox",{attrs:{value:"is_not_active"},model:{value:e.is_not_active,callback:function(t){e.is_not_active=t},expression:"is_not_active"}},[e._v("\n          Nieaktywne\n        ")]),a("div",{staticClass:"q-pa-sm"},[a("q-btn",{staticClass:"q-pa-sm",attrs:{color:"dark",label:"Filtruj"},on:{click:e.filterData}})],1),a("div",{staticClass:"q-pa-sm q-ml-xl"},[a("q-btn",{attrs:{disable:0===e.selected.length,color:"primary"},on:{click:e.openCreateRequestDialog}},[e._v("\n            Stwórz zlecenia zmiany"),a("br"),e._v("ceny lub kodu producenta\n          ")])],1),a("div",{staticClass:"q-pa-sm"},[a("q-btn",{staticClass:"q-py-sm",attrs:{disable:0===e.selected.length,color:"primary",label:"CSV",icon:"backup",rounded:""},on:{click:e.downloadCSV}})],1)],1),a("BaseTable",e._b({ref:"tableRef",attrs:{title:"Zarządzanie materiałowe","row-key":"id","response-key":"results"},scopedSlots:e._u([{key:"body-cell-change_requests",fn:function(t){return[a("q-td",e._b({},"q-td",{props:t},!1),e._l(t.row.requests,(function(n){return a("div",{key:n.id},[a("div",{staticClass:"flex justify-center"},[a("RequestChangeInfoTooltip",{attrs:{request:n}}),n.status===e.CUSTOM_PRICING_FACTOR_REQUEST_STATUS.CHANGES_REQUESTED?a("q-btn",{staticClass:"q-ml-sm",attrs:{icon:"edit",color:"black",round:""},on:{click:function(a){return e.openUpdateRequestDialog(n,t.row)}}}):e._e()],1),a("span",[e._v(e._s(n.updated_at))])])})),0)]}}])},"BaseTable",{columns:e.columns,loading:e.loading,filters:e.filters,updateLoading:e.updateLoading,updateSelected:e.updateSelected,apiUrl:e.listApiUrl},!1)),a("q-dialog",{attrs:{"full-width":"","full-height":""},model:{value:e.showCreateRequestDialog,callback:function(t){e.showCreateRequestDialog=t},expression:"showCreateRequestDialog"}},[a("q-card",{staticClass:"q-pa-xl"},[a("CreateRequestChangeForm",{attrs:{"selected-data":e.selected},on:{"close-create-request-modal":e.handleCloseCreateRequestDialog}})],1)],1),a("q-dialog",{attrs:{"full-width":"","full-height":""},model:{value:e.showUpdateRequestDialog,callback:function(t){e.showUpdateRequestDialog=t},expression:"showUpdateRequestDialog"}},[a("q-card",{staticClass:"q-pa-xl"},[a("UpdateRequestChangeForm",{attrs:{"request-data":e.requestToEdit,"related-custom-pricing-factor-data":e.relatedDataForRequestEdit},on:{"close-create-request-modal":e.handleCloseUpdateRequestDialog}})],1)],1)],1)])])},o=[],r=(a("7f7f"),a("3de1")),s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("q-btn",{attrs:{rounded:"",color:e.getTooltipColor(e.request.status)},on:{click:e.toggleTooltip}},[e._v("\n    "+e._s(e.request.request_number)+"\n  ")]),a("div",[a("q-tooltip",{attrs:{"content-class":"bg-white text-black tooltip-container","content-style":"font-size: 16px",anchor:"center left",self:"center right",offset:[10,10]},model:{value:e.showTooltip,callback:function(t){e.showTooltip=t},expression:"showTooltip"}},[a("p",{staticClass:"text-center text-caption q-ma-xs"},[e._v("\n        Status: "),a("b",[e._v(e._s(e.statusMapping[e.request.status]))])]),a("p",{staticClass:"text-caption"},[e._v("\n        Data od:\n        "),a("q-chip",{attrs:{color:"primary","text-color":"white",icon:"event"}},[e._v("\n          "+e._s(e.request.date_from)+"\n        ")])],1),a("p",{staticClass:"text-caption q-ma-xs"},[e._v("\n        Nowy kod producenta: "),a("b",[e._v(e._s(e.request.manufacturer_code))])]),a("p",{staticClass:"text-caption q-ma-xs"},[e._v("\n        Nowa cena: "),a("b",[e._v(e._s(e.request.price))])]),a("p",{staticClass:"text-caption q-ma-xs text-center"},[e._v("\n        Komentarze:\n      ")]),a("div",{staticClass:"column"},e._l(e.request.comments,(function(t){return a("div",{key:t.id,class:[t.is_manufacturer_comment?"bg-blue":"bg-red","text-white","comment-container","text-body2","q-pa-sm","q-my-sm","text-center"],staticStyle:{"max-width":"300px"}},[e._v("\n          "+e._s(t.text)+"\n        ")])})),0)])],1)],1)},i=[],c=a("c47a"),l=a.n(c),u=a("4c6b"),d={name:"RequestChangeInfoTooltip",props:{request:{type:Object,required:!0}},data:function(){var e;return{showTooltip:!1,statusMapping:(e={},l()(e,u["d"].NEW,"Nowe zlecenie"),l()(e,u["d"].ACCEPTED,"Zaakceptowane"),l()(e,u["d"].REJECTED,"Odrzucone"),l()(e,u["d"].CHANGES_REQUESTED,"Do poprawy"),e)}},methods:{toggleTooltip:function(){this.showTooltip=!this.showTooltip},getTooltipColor:function(e){if(this.showTooltip)return"grey";switch(e){case u["d"].NEW:return"primary";case u["d"].ACCEPTED:return"green";case u["d"].REJECTED:return"red";case u["d"].CHANGES_REQUESTED:return"orange";default:return"primary"}}}},p=d,m=(a("4334"),a("2877")),f=a("fe09"),b=Object(m["a"])(p,s,i,!1,null,"ff06df74",null),_=b.exports;b.options.components=Object.assign({QBtn:f["b"],QTooltip:f["z"],QChip:f["f"]},b.options.components||{});var g=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("q-form",{on:{submit:function(t){return t.preventDefault(),t.stopPropagation(),e.createChangeRequest(t)}}},[a("p",{staticClass:"text-h5 q-my-md text-center"},[e._v("\n    Tworzenie zleceń zmian ceny lub kodu producenta\n  ")]),e._l(e.customPricingFactorsData,(function(t){return a("q-card",{key:t.id,staticClass:"align-center justify-between q-pa-md q-ma-md item-wrapper",attrs:{flat:"",bordered:""}},[a("p",{staticClass:"text-h5"},[e._v("\n      "+e._s(t.codename)+"\n    ")]),a("div",[a("div",{staticClass:"row justify-between q-py-xs"},[a("q-input",{staticClass:"col-grow q-mx-sm",attrs:{filled:"",square:"",dense:"",label:"Kod producenta",readonly:""},model:{value:t.manufacturer_code,callback:function(a){e.$set(t,"manufacturer_code",a)},expression:"item.manufacturer_code"}}),a("q-input",{staticClass:"col-grow q-mx-sm",attrs:{filled:"",square:"",dense:"",label:"Cena",readonly:""},model:{value:t.price,callback:function(a){e.$set(t,"price",a)},expression:"item.price"}}),a("q-input",{staticClass:"col-grow q-mx-sm",attrs:{filled:"",square:"",dense:"",type:"date",label:"Data od",readonly:""},model:{value:t.date_from,callback:function(a){e.$set(t,"date_from",a)},expression:"item.date_from"}}),a("q-input",{staticClass:"col-grow q-mx-sm",attrs:{"stack-label":"",filled:"",square:"",dense:"",type:"date",label:"Data do",readonly:""},model:{value:t.date_to,callback:function(a){e.$set(t,"date_to",a)},expression:"item.date_to"}})],1),a("div",{staticClass:"row justify-between q-py-xs"},[a("q-select",{staticClass:"col-grow q-mx-sm",attrs:{outlined:"",square:"",dense:"",label:"Nowy kod producenta",options:t.available_codenames,clearable:"","option-value":"code","option-label":"code","emit-value":"","map-options":"",hint:e.selectedOptionName(t)},model:{value:t.new_manufacturer_code,callback:function(a){e.$set(t,"new_manufacturer_code",a)},expression:"item.new_manufacturer_code"}}),a("q-input",{staticClass:"col-grow q-mx-sm",attrs:{type:"number",step:".01",outlined:"",square:"",dense:"",label:"Nowa cena",rules:[function(e){return!!e||"To pole jest wymagane"}]},model:{value:t.new_price,callback:function(a){e.$set(t,"new_price",a)},expression:"item.new_price"}}),a("q-input",{staticClass:"col-grow q-mx-sm",attrs:{"stack-label":"",outlined:"",square:"",dense:"",type:"date",label:"Nowa data od",rules:[function(e){return!!e||"To pole jest wymagane"},function(e){return e&&e>t.date_from||"Nowa data nie może być mniejsza lub równa obecnej data_od"},function(a){return a&&e.validateDateFromWithOtherRequest(a,t)||"Nowa data od nie może być mniejsza lub równa dacie z innego zlecenia"}]},model:{value:t.new_date_from,callback:function(a){e.$set(t,"new_date_from",a)},expression:"item.new_date_from"}})],1)]),a("div",{staticClass:"q-py-xs"},[a("q-input",{staticClass:"q-mx-sm",attrs:{label:"Komentarz",maxlength:255},model:{value:t.comment,callback:function(a){e.$set(t,"comment",a)},expression:"item.comment"}})],1)])})),a("div",{staticClass:"column q-px-xl q-mx-xl align-center justify-center"},[a("q-file",{attrs:{value:e.attachment,rules:[function(e){return!!e||"To pole jest wymagane"}]},scopedSlots:e._u([{key:"prepend",fn:function(){return[a("q-icon",{attrs:{name:"attachment"}})]},proxy:!0}]),model:{value:e.attachment,callback:function(t){e.attachment=t},expression:"attachment"}}),a("q-btn",{staticClass:"q-mt-md",attrs:{color:"primary",label:"Wyślij",type:"submit"}})],1)],2)},h=[],q=(a("8e6e"),a("8a81"),a("ac6a"),a("cadf"),a("06db"),a("456d"),a("967e")),w=a.n(q);a("96cf"),a("7514");function v(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function y(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?v(a,!0).forEach((function(t){l()(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):v(a).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var C={name:"CreateRequestChangeForm",props:{selectedData:{type:Array,default:function(){return[]}}},data:function(){return{attachment:null,customPricingFactorsData:this.getInitialData()}},computed:{selectedOptionName:function(){return function(e){if(!e.new_manufacturer_code||!e.available_codenames)return"";var t=e.available_codenames.find((function(t){return t.code===e.new_manufacturer_code}));return t?t.name:""}}},methods:{getInitialData:function(){return this.selectedData.map((function(e){return y({},e,{new_manufacturer_code:e.manufacturer_code,new_price:"",new_date_from:"",attachment:null,comment:""})}))},getDataForRequest:function(){return this.customPricingFactorsData.map((function(e){return{custom_pricing_factor_id:e.id,manufacturer_code:e.new_manufacturer_code||e.manufacturer_code,price:e.new_price,date_from:e.new_date_from,comment:e.comment,status:u["d"].NEW}}))},createChangeRequest:function(){var e;return w.a.async((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e=this.prepareFormData(),t.next=4,w.a.awrap(this.postFormData(e));case 4:this.showSuccessNotification(),this.$emit("close-create-request-modal"),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0),this.showErrorNotification(t.t0);case 11:case"end":return t.stop()}}),null,this,[[0,8]])},prepareFormData:function(){var e=new FormData;return e.append("attachment",this.attachment),e.append("requests",JSON.stringify(this.getDataForRequest())),e},postFormData:function(e){var t;return w.a.async((function(a){while(1)switch(a.prev=a.next){case 0:return t={headers:{"Content-Type":"multipart/form-data"}},a.next=3,w.a.awrap(this.$axios.post("/pages/api/create_custom_pricing_factor_change_request/",e,t));case 3:case"end":return a.stop()}}),null,this)},showSuccessNotification:function(){this.$q.notify({message:"Zlecenie zmiany ceny lub kodu producenta zostało utworzone",color:"positive",icon:"check"})},showErrorNotification:function(e){this.$q.notify({message:"Nie udało się utworzyć zlecenia zmiany ceny lub kodu producenta:\n".concat(e.message),color:"negative",icon:"warning"})},validateDateFromWithOtherRequest:function(e,t){var a=t.requests.reduce((function(e,t){return t.status===u["d"].NEW&&e.push(t),e}),[]);return!(a.length>0&&a.some((function(t){return e<=t.date_from})))}}},D=C,E=(a("bb75"),Object(m["a"])(D,g,h,!1,null,"99a8ec08",null)),T=E.exports;E.options.components=Object.assign({QForm:f["j"],QCard:f["d"],QInput:f["m"],QSelect:f["w"],QFile:f["i"],QIcon:f["k"],QBtn:f["b"]},E.options.components||{});var x=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("q-form",{on:{submit:function(t){return t.preventDefault(),t.stopPropagation(),e.createChangeRequest(t)}}},[a("p",{staticClass:"text-h5 q-my-md text-center"},[e._v("\n    Edycja zlecenia zmiany ceny lub kodu producenta\n  ")]),a("q-card",{staticClass:"align-center justify-between q-pa-md q-ma-md item-wrapper",attrs:{flat:"",bordered:""}},[a("p",{staticClass:"text-h5"},[e._v("\n      "+e._s(e.relatedCustomPricingFactorData.codename)+"\n    ")]),a("div",[a("div",{staticClass:"row justify-between q-py-xs"},[a("q-input",{staticClass:"col-grow q-mx-sm",attrs:{filled:"",square:"",dense:"",label:"Kod producenta",readonly:""},model:{value:e.relatedCustomPricingFactorData.manufacturer_code,callback:function(t){e.$set(e.relatedCustomPricingFactorData,"manufacturer_code",t)},expression:"relatedCustomPricingFactorData.manufacturer_code"}}),a("q-input",{staticClass:"col-grow q-mx-sm",attrs:{filled:"",square:"",dense:"",label:"Cena",readonly:""},model:{value:e.relatedCustomPricingFactorData.price,callback:function(t){e.$set(e.relatedCustomPricingFactorData,"price",t)},expression:"relatedCustomPricingFactorData.price"}}),a("q-input",{staticClass:"col-grow q-mx-sm",attrs:{filled:"",square:"",dense:"",type:"date",label:"Data od",readonly:""},model:{value:e.relatedCustomPricingFactorData.date_from,callback:function(t){e.$set(e.relatedCustomPricingFactorData,"date_from",t)},expression:"relatedCustomPricingFactorData.date_from"}}),a("q-input",{staticClass:"col-grow q-mx-sm",attrs:{"stack-label":"",filled:"",square:"",dense:"",type:"date",label:"Data do",readonly:""},model:{value:e.relatedCustomPricingFactorData.date_to,callback:function(t){e.$set(e.relatedCustomPricingFactorData,"date_to",t)},expression:"relatedCustomPricingFactorData.date_to"}})],1),a("div",{staticClass:"row justify-between q-py-xs"},[a("q-select",{staticClass:"col-grow q-mx-sm",attrs:{outlined:"",square:"",dense:"",label:"Nowy kod producenta",options:e.dataToUpdate.available_codenames,clearable:"","option-value":"code","option-label":"code","emit-value":"","map-options":"",hint:e.selectedOptionName(e.dataToUpdate)},model:{value:e.dataToUpdate.new_manufacturer_code,callback:function(t){e.$set(e.dataToUpdate,"new_manufacturer_code",t)},expression:"dataToUpdate.new_manufacturer_code"}}),a("q-input",{staticClass:"col-grow q-mx-sm",attrs:{type:"number",step:".01",outlined:"",square:"",dense:"",label:"Nowa cena",rules:[function(e){return!!e||"To pole jest wymagane"}]},model:{value:e.dataToUpdate.new_price,callback:function(t){e.$set(e.dataToUpdate,"new_price",t)},expression:"dataToUpdate.new_price"}}),a("q-input",{staticClass:"col-grow q-mx-sm",attrs:{"stack-label":"",outlined:"",square:"",dense:"",type:"date",label:"Nowa data od",rules:[function(e){return!!e||"To pole jest wymagane"},function(t){return t&&t>e.relatedCustomPricingFactorData.date_from||"Nowa data nie może być mniejsza lub równa obecnej data_od"},function(t){return t&&e.validateDateFromWithOtherRequest(t,e.relatedCustomPricingFactorData)||"Nowa data od nie może być mniejsza lub równa dacie z innego zlecenia"}]},model:{value:e.dataToUpdate.new_date_from,callback:function(t){e.$set(e.dataToUpdate,"new_date_from",t)},expression:"dataToUpdate.new_date_from"}})],1)]),a("p",{staticClass:"text-h5 q-my-md text-center"},[e._v("\n      Komentarze:\n    ")]),a("div",{staticClass:"column flex-center"},e._l(e.requestData.comments,(function(t){return a("div",{key:t.id,class:[t.is_manufacturer_comment?"bg-blue":"bg-red","text-white","comment-container","text-body2","q-pa-sm","q-my-sm","text-center","full-width"]},[e._v("\n        "+e._s(t.text)+"\n      ")])})),0),a("div",{staticClass:"q-py-xs"},[a("q-input",{staticClass:"q-mx-sm",attrs:{label:"Nowy komentarz",maxlength:255},model:{value:e.dataToUpdate.comment,callback:function(t){e.$set(e.dataToUpdate,"comment",t)},expression:"dataToUpdate.comment"}})],1)]),a("div",{staticClass:"column q-px-xl q-mx-xl align-center justify-center"},[a("q-file",{attrs:{value:e.attachment,label:"Załącznik (dodaj, jeśli dodano wcześniej błędny plik)"},scopedSlots:e._u([{key:"prepend",fn:function(){return[a("q-icon",{attrs:{name:"attachment"}})]},proxy:!0}]),model:{value:e.attachment,callback:function(t){e.attachment=t},expression:"attachment"}}),a("q-btn",{staticClass:"q-mt-md",attrs:{color:"primary",label:"Zapisz",type:"submit"}})],1)],1)},O=[],k={name:"UpdateRequestChangeForm",props:{requestData:{type:Object,default:function(){}},relatedCustomPricingFactorData:{type:Array,default:function(){return[]}}},data:function(){return{attachment:null,dataToUpdate:this.getInitialData()}},computed:{selectedOptionName:function(){return function(e){if(!e.new_manufacturer_code||!e.available_codenames)return"";var t=e.available_codenames.find((function(t){return t.code===e.new_manufacturer_code}));return t?t.name:""}}},methods:{getInitialData:function(){return{new_manufacturer_code:this.requestData.manufacturer_code,new_price:this.requestData.price,new_date_from:this.requestData.date_from,attachment:null,comment:"",available_codenames:this.relatedCustomPricingFactorData.available_codenames}},getDataForRequest:function(){return[{id:this.requestData.id,custom_pricing_factor_id:this.relatedCustomPricingFactorData.id,manufacturer_code:this.dataToUpdate.new_manufacturer_code,price:this.dataToUpdate.new_price,date_from:this.dataToUpdate.new_date_from,comment:this.dataToUpdate.comment,status:u["d"].NEW}]},createChangeRequest:function(){var e;return w.a.async((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e=this.prepareFormData(),t.next=4,w.a.awrap(this.postFormData(e));case 4:this.showSuccessNotification(),this.$emit("close-create-request-modal"),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0),this.showErrorNotification(t.t0);case 11:case"end":return t.stop()}}),null,this,[[0,8]])},prepareFormData:function(){var e=new FormData;return this.attachment&&e.append("attachment",this.attachment),e.append("requests",JSON.stringify(this.getDataForRequest())),e},postFormData:function(e){var t;return w.a.async((function(a){while(1)switch(a.prev=a.next){case 0:return t={headers:{"Content-Type":"multipart/form-data"}},a.next=3,w.a.awrap(this.$axios.patch("/pages/api/update_custom_pricing_factor_change_request/".concat(this.requestData.id,"/"),e,t));case 3:case"end":return a.stop()}}),null,this)},showSuccessNotification:function(){this.$q.notify({message:"Zlecenie zmiany ceny lub kodu producenta zostało utworzone",color:"positive",icon:"check"})},showErrorNotification:function(e){this.$q.notify({message:"Nie udało się utworzyć zlecenia zmiany ceny lub kodu producenta:\n".concat(e.message),color:"negative",icon:"warning"})},validateDateFromWithOtherRequest:function(e,t){var a=this,n=t.requests.reduce((function(e,t){return t.status===u["d"].NEW&&t.id!==a.dataToUpdate.id&&e.push(t),e}),[]);return!(n.length>0&&n.some((function(t){return e<=t.date_from})))}}},R=k,N=(a("4d66"),Object(m["a"])(R,x,O,!1,null,"2e396a48",null)),S=N.exports;N.options.components=Object.assign({QForm:f["j"],QCard:f["d"],QInput:f["m"],QSelect:f["w"],QFile:f["i"],QIcon:f["k"],QBtn:f["b"]},N.options.components||{});var P=a("2fa3"),U={name:"MaterialsManagementView",components:{CreateRequestChangeForm:T,RequestChangeInfoTooltip:_,BaseTable:r["a"],UpdateRequestChangeForm:S},data:function(){return{listApiUrl:"/pages/api/custom_pricing_factors/",csvExportApiUrl:"/pages/api/custom_pricing_factors/export_csv/",loading:!1,selected:[],codename:"",manufacturerCode:"",status:"",is_not_active:!1,showCreateRequestDialog:!1,showUpdateRequestDialog:!1,requestToEdit:null,relatedDataForRequestEdit:null,statusOptions:[{label:"Nowe zlecenie",value:u["d"].NEW,color:"primary"},{label:"Zaakceptowane",value:u["d"].ACCEPTED,color:"green"},{label:"Odrzucone",value:u["d"].REJECTED,color:"red"},{label:"Do poprawy",value:u["d"].CHANGES_REQUESTED,color:"orange"},{label:"Wszystkie statusy",value:null,color:"white"}],CUSTOM_PRICING_FACTOR_REQUEST_STATUS:u["d"]}},computed:{filters:function(){return{codename:this.codename,manufacturer_code:this.manufacturerCode,status:this.status.value,is_not_active:this.is_not_active}},columns:function(){return[{name:"codename",label:"Codename",align:"left",field:function(e){return e.codename}},{name:"name",label:"Polska nazwa",align:"center",field:function(e){return e.name},style:"max-width: 200px; white-space: normal;"},{name:"manufacturer_code",label:"Kod producenta",align:"center",field:function(e){return e.manufacturer_code}},{label:"Jednostka",name:"unit",align:"center",field:function(e){return e.unit}},{label:"Cena",name:"price",align:"center",field:function(e){return e.price}},{label:"Loss factor",name:"loss_factor",align:"center",field:function(e){return e.loss_factor}},{label:"Data od",name:"date_from",align:"center",field:function(e){return e.date_from}},{label:"Data do",name:"date_to",align:"center",field:function(e){return e.date_to}},{label:"Zlecenia zmiany / zaktualizowano",name:"change_requests",align:"center"}]}},methods:{updateSelected:function(e){this.selected=e},updateLoading:function(e){this.loading=e},filterData:function(){this.$refs.tableRef.getData()},openCreateRequestDialog:function(){this.showCreateRequestDialog=!0},openUpdateRequestDialog:function(e,t){this.showUpdateRequestDialog=!0,this.requestToEdit=e,this.relatedDataForRequestEdit=t,console.log("dupa",e,t)},handleCloseCreateRequestDialog:function(){this.showCreateRequestDialog=!1,this.$refs.tableRef.getData(),this.$refs.tableRef.resetSelected()},handleCloseUpdateRequestDialog:function(){this.showUpdateRequestDialog=!1,this.requestToEdit=null,this.relatedDataForRequestEdit=null,this.$refs.tableRef.getData(),this.$refs.tableRef.resetSelected()},downloadCSV:function(){var e=this,t=this.selected.map((function(e){return e.id}));this.$axios({method:"POST",url:this.csvExportApiUrl,data:{ids:t},responseType:"blob"}).then((function(e){Object(P["a"])(e)})).catch((function(t){return e.$q.notify({message:t.message,type:"negative"})}))}}},F=U,j=Object(m["a"])(F,n,o,!1,null,null,null);t["default"]=j.exports;j.options.components=Object.assign({QPage:f["s"],QInput:f["m"],QSelect:f["w"],QItem:f["n"],QItemSection:f["p"],QItemLabel:f["o"],QBadge:f["a"],QCheckbox:f["e"],QBtn:f["b"],QTd:f["y"],QDialog:f["h"],QCard:f["d"]},j.options.components||{})},4334:function(e,t,a){"use strict";var n=a("2609"),o=a.n(n);o.a},"4c6b":function(e,t,a){"use strict";a.d(t,"g",(function(){return n})),a.d(t,"e",(function(){return o})),a.d(t,"a",(function(){return r})),a.d(t,"c",(function(){return s})),a.d(t,"b",(function(){return i})),a.d(t,"d",(function(){return c})),a.d(t,"f",(function(){return l}));var n=[{label:"DT",value:1},{label:"TNT",value:2},{label:"UPS",value:3},{label:"DPD",value:4},{label:"FEDEX",value:5}],o=[{label:"Styczeń",value:1},{label:"Luty",value:2},{label:"Marzec",value:3},{label:"Kwiecień",value:4},{label:"Maj",value:5},{label:"Czerwiec",value:6},{label:"Lipiec",value:7},{label:"Sierpień",value:8},{label:"Wrzesień",value:9},{label:"Październik",value:10},{label:"Listopad",value:11},{label:"Grudzień",value:12}],r={INITIAL:0,DOWNLOADED:1,RECALCULATED_AFTER_DOWNLOAD:2},s={NOT_SET:0,STANDARD:1,EXTENDED:2,CUSTOM:3,DRAWERS:4,DRAWERS_EXTENDED:5,COMPLAINTS:10},i={ABORTED:0,NEW:1,IN_PRODUCTION:2,SENT_TO_CUSTOMER:3},c={NEW:0,ACCEPTED:1,REJECTED:2,CHANGES_REQUESTED:3},l={ABORTED:0,NEW:1,UTILIZATION:2,UTILIZATION_DONE:3,ASSIGNED_TO_PRODUCTION:4,IN_PRODUCTION:5,SENT_TO_CUSTOMER:6,SENT_TO_WAREHOUSE:7,QUALITY_BLOCKER:8,INTERNAL_USAGE:9,TO_BE_SHIPPED:10,QUALITY_CONTROL:11,ABORTED_DONE:12,SHELFMARKET:13,SHELFMARKET_DONE:14,DELIVERED_TO_CUSTOMER:15,CM_UTILIZATION_DONE:16,TO_BE_SHIPPED_AWIZATION:17,INTERNAL_SHIPMENT:18}},"4d66":function(e,t,a){"use strict";var n=a("3125"),o=a.n(n);o.a},bb75:function(e,t,a){"use strict";var n=a("eeb2"),o=a.n(n);o.a},eeb2:function(e,t,a){}}]);