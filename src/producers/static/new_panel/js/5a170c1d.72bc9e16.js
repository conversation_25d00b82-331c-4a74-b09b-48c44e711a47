(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["5a170c1d"],{"2fa3":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return s})),a.d(t,"c",(function(){return r}));a("4917");var n=function(e){var t=window.URL.createObjectURL(new Blob([e.data])),a=document.createElement("a");a.href=t;var n=e.headers["content-disposition"],s=n.match(/filename=(.+)/);a.setAttribute("download",s[1]),document.body.appendChild(a),a.click(),a.remove(),window.URL.revokeObjectURL(t)},s=function(e){var t=document.createElement("a");t.href=e,t.click(),t.remove()},r=function(e){var t=e.completed,a=e.all;if(0===a)return"100% (0/0)";var n=(t/a*100).toFixed(2);return"".concat(n,"% (").concat(t,"/").concat(a,")")}},"3f31":function(e,t,a){"use strict";var n=a("ee29"),s=a.n(n);s.a},"4c6b":function(e,t,a){"use strict";a.d(t,"g",(function(){return n})),a.d(t,"e",(function(){return s})),a.d(t,"a",(function(){return r})),a.d(t,"c",(function(){return o})),a.d(t,"b",(function(){return i})),a.d(t,"d",(function(){return l})),a.d(t,"f",(function(){return c}));var n=[{label:"DT",value:1},{label:"TNT",value:2},{label:"UPS",value:3},{label:"DPD",value:4},{label:"FEDEX",value:5}],s=[{label:"Styczeń",value:1},{label:"Luty",value:2},{label:"Marzec",value:3},{label:"Kwiecień",value:4},{label:"Maj",value:5},{label:"Czerwiec",value:6},{label:"Lipiec",value:7},{label:"Sierpień",value:8},{label:"Wrzesień",value:9},{label:"Październik",value:10},{label:"Listopad",value:11},{label:"Grudzień",value:12}],r={INITIAL:0,DOWNLOADED:1,RECALCULATED_AFTER_DOWNLOAD:2},o={NOT_SET:0,STANDARD:1,EXTENDED:2,CUSTOM:3,DRAWERS:4,DRAWERS_EXTENDED:5,COMPLAINTS:10},i={ABORTED:0,NEW:1,IN_PRODUCTION:2,SENT_TO_CUSTOMER:3},l={NEW:0,ACCEPTED:1,REJECTED:2,CHANGES_REQUESTED:3},c={ABORTED:0,NEW:1,UTILIZATION:2,UTILIZATION_DONE:3,ASSIGNED_TO_PRODUCTION:4,IN_PRODUCTION:5,SENT_TO_CUSTOMER:6,SENT_TO_WAREHOUSE:7,QUALITY_BLOCKER:8,INTERNAL_USAGE:9,TO_BE_SHIPPED:10,QUALITY_CONTROL:11,ABORTED_DONE:12,SHELFMARKET:13,SHELFMARKET_DONE:14,DELIVERED_TO_CUSTOMER:15,CM_UTILIZATION_DONE:16,TO_BE_SHIPPED_AWIZATION:17,INTERNAL_SHIPMENT:18}},ae54:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("q-page",{staticClass:"q-pa-lg"},[a("div",{staticClass:"q-pa-md"},[a("div",{staticClass:"row"},[a("q-input",{staticClass:"q-pa-sm col-1",attrs:{label:"Szafka",filled:""},model:{value:e.product,callback:function(t){e.product=t},expression:"product"}}),a("q-input",{staticClass:"q-pa-sm col-1",attrs:{label:"Nr transportu",filled:""},model:{value:e.transport,callback:function(t){e.transport=t},expression:"transport"}}),a("q-input",{staticClass:"q-pa-sm col-1",attrs:{label:"Notatki",filled:""},model:{value:e.notes,callback:function(t){e.notes=t},expression:"notes"}}),a("q-select",{staticClass:"q-pa-sm col-1",attrs:{label:"Miesiąc",filled:"",options:e.monthsOptions,clearable:""},model:{value:e.created_at_month,callback:function(t){e.created_at_month=t},expression:"created_at_month"}}),a("q-select",{staticClass:"q-pa-sm col-1",attrs:{label:"Rok",filled:"",options:e.getYearOptions(),clearable:""},model:{value:e.created_at_year,callback:function(t){e.created_at_year=t},expression:"created_at_year"}}),a("q-input",{staticClass:"q-pa-sm col-1",attrs:{label:"Data załadunku",filled:"",clearable:"",mask:"####-##-##"},scopedSlots:e._u([{key:"append",fn:function(){return[a("q-icon",{staticClass:"cursor-pointer",attrs:{name:"event"}},[a("q-popup-proxy",{attrs:{cover:"","transition-show":"scale","transition-hide":"scale"}},[a("q-date",{attrs:{mask:"YYYY-MM-DD","today-btn":""},model:{value:e.load_date,callback:function(t){e.load_date=t},expression:"load_date"}})],1)],1)]},proxy:!0}]),model:{value:e.load_date,callback:function(t){e.load_date=t},expression:"load_date"}}),a("q-select",{staticClass:"q-pa-sm col-1",attrs:{label:"Typ transportu",filled:"",options:e.transportTypeOptions,clearable:""},model:{value:e.transport_type,callback:function(t){e.transport_type=t},expression:"transport_type"}}),a("q-checkbox",{staticClass:"q-pa-sm",attrs:{label:"nadchodzące awizacje",filled:""},model:{value:e.is_estimated_delivery_date,callback:function(t){e.is_estimated_delivery_date=t},expression:"is_estimated_delivery_date"}}),a("div",{staticClass:"q-pa-sm"},[a("q-btn",{staticClass:"q-pa-sm",attrs:{label:"Szukaj",type:"searchActive",color:"primary"},on:{click:function(t){return e.search()}}})],1)],1),a("div",[a("q-checkbox",{attrs:{label:"Wydane awizacje"},on:{change:function(t){return e.search()}},model:{value:e.released,callback:function(t){e.released=t},expression:"released"}})],1)]),a("div",{staticClass:"row q-pa-md"},[a("q-table",e._b({staticClass:"sticky-header-table",attrs:{title:"Awizacja","row-key":"id",selected:e.selected,pagination:e.pagination,"loading-label":"Pobieranie danych...","rows-per-page-label":"Ilość na stronie:",selection:"multiple",flat:"",bordered:"",separator:"cell"},on:{"update:selected":function(t){e.selected=t},"update:pagination":function(t){e.pagination=t},request:e.reloadTable},scopedSlots:e._u([{key:"body-cell",fn:function(t){return[a("q-td",{class:{"cell-released":t.row.released_at,"cell-changed":!t.row.are_changes_confirmed},attrs:{props:t}},[e._v("\n          "+e._s(t.value)+"\n        ")])]}},{key:"body-cell-relatedItems",fn:function(t){return[a("q-td",e._b({},"q-td",{props:t},!1),[a("q-btn-dropdown",e._b({attrs:{flat:"",color:"primary",size:"md","no-caps":""}},"q-btn-dropdown",{label:t.row.get_related_items.slice(0,10)+"..."},!1),[a("q-list",e._l(e.getRelatedItemsArray(t.row.get_related_items),(function(t){return a("q-item",{key:t},[a("q-item-section",[a("q-item-label",[e._v(e._s(t))])],1)],1)})),1)],1)],1)]}},{key:"body-cell-producer_notes",fn:function(t){return[a("q-td",e._b({key:"producer_notes",staticClass:"producer-notes-cell q-gutter-sm q-pa-xs"},"q-td",{props:t},!1),[e._v("\n          "+e._s(t.row.producer_notes)+"\n          "),a("EditTransportReleaseNote",{attrs:{row:t.row}})],1)]}},{key:"body-cell-logisticLabelUrl",fn:function(t){return[a("q-td",e._b({staticClass:"table-details q-gutter-sm producer-notes-cell"},"q-td",{props:t},!1),["Dt"!==t.row.transport_type||t.row.logistic_label_url?a("q-btn",{staticClass:"q-ma-xs",attrs:{flat:"",dense:"",color:"primary",label:"etykieta"},on:{click:function(){return e.getLabels(t.row.id)}}}):e._e()],1)]}},{key:"body-cell-additionalDocUrl",fn:function(t){return[a("q-td",e._b({staticClass:"table-details q-gutter-sm"},"q-td",{props:t},!1),["Fedex"!==t.row.transport_type?a("q-btn",{staticClass:"q-ma-xs",attrs:{flat:"",dense:"",color:"primary",label:"dokument"},on:{click:function(){return e.getAdditionalDocument(t.row.id)}}}):e._e()],1)]}},{key:"body-cell-documents",fn:function(t){return[a("q-td",e._b({staticClass:"table-details q-gutter-sm"},"q-td",{props:t},!1),[a("q-btn",{staticClass:"q-ma-xs",attrs:{flat:"",dense:"",color:"primary",label:"zip"},on:{click:function(){return e.getDocumentsZip(t.row.id)}}})],1)]}},{key:"body-cell-attachments_zip",fn:function(t){return[a("q-td",e._b({staticClass:"table-details q-gutter-sm"},"q-td",{props:t},!1),[t.row.attachments_exists?a("q-btn",{staticClass:"q-ma-xs",attrs:{flat:"",dense:"",color:"primary",label:"zip"},on:{click:function(){return e.getAttachmentsZip(t.row.id)}}}):e._e()],1)]}},{key:"body-cell-were_files_downloaded",fn:function(t){return[a("q-td",e._b({staticClass:"table-details "},"q-td",{props:t},!1),[t.row.were_files_downloaded?a("q-icon",{staticClass:"text-green",attrs:{size:"md",name:"done"}}):e._e()],1)]}},{key:"body-cell-actions",fn:function(t){return[a("q-td",e._b({staticClass:"table-details q-gutter-sm"},"q-td",{props:t},!1),[a("q-btn",e._b({attrs:{color:"primary",label:"Przyjęcie awizacji"},on:{click:function(a){return e.releaseTransport(t.row.id)}}},"q-btn",{disabled:t.row.released_at&&t.row.are_changes_confirmed},!1)),a("q-btn",e._b({attrs:{color:"primary",label:"Towar wyjechał"},on:{click:function(a){return e.confirmTransportRelease(t.row.id)}}},"q-btn",{disabled:t.row.release_confirmed_at},!1)),a("a",{staticClass:"with-underscore",attrs:{href:"/pages/logistic_manifest/"+t.row.id}},[a("q-btn",{attrs:{color:"primary",label:"Logistic manifest"}})],1),e.isInexProducer()?a("a",e._b({},"a",{href:e.getCsvUrlForRow(t.row.id)},!1),[a("q-btn",{attrs:{color:"secondary",label:"Lista logistyczna CSV"}})],1):e._e()],1)]}}])},"q-table",{data:e.transportReleases,columns:e.columns,loading:e.loading,rowsPerPageOptions:[10,25,50]},!1))],1)])},s=[],r=(a("8e6e"),a("8a81"),a("ac6a"),a("cadf"),a("06db"),a("456d"),a("7514"),a("a481"),a("28a5"),a("c47a")),o=a.n(r),i=a("4c6b"),l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("q-popup-edit",{attrs:{buttons:"","label-set":"Zapisz i wyślij mail","label-cancel":"Zamknij"},on:{"before-show":function(t){return e.cloneRow(e.row)},save:function(t){return e.updateRow(e.row,"producer_notes",e.clonedRow.producer_notes)}},model:{value:e.clonedRow.producer_notes,callback:function(t){e.$set(e.clonedRow,"producer_notes",t)},expression:"clonedRow.producer_notes"}},[a("q-input",{attrs:{dense:"",autofocus:"",hint:"WAŻNE: po każdym edytowaniu i zapisaniu notatki zostaje wysłany mail do logistyki"},model:{value:e.clonedRow.producer_notes,callback:function(t){e.$set(e.clonedRow,"producer_notes",t)},expression:"clonedRow.producer_notes"}})],1)},c=[],d=a("967e"),p=a.n(d);a("96cf");function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function _(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(a,!0).forEach((function(t){o()(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(a).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var m={name:"EditTransportReleaseNote",props:{row:{type:Object,required:!0}},data:function(){return{clonedRow:{}}},methods:{updateRow:function(e,t,a){e[t]=a,this.saveNote()},cloneRow:function(e){this.clonedRow=_({},e)},saveNote:function(){return p.a.async((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,p.a.awrap(this.$axios({method:"POST",url:"".concat(window.LOGISTIC_URL,"/api/v1/transport_release/").concat(this.row.id,"/save_note_and_send_mail"),headers:{Authorization:"Token ".concat(window.USER_TOKEN)},data:{producer_notes:this.row.producer_notes}}));case 3:this.$q.notify({message:"Notatka została zapisana pomyślnie",type:"positive"}),e.next=9;break;case 6:e.prev=6,e.t0=e["catch"](0),this.$q.notify({message:"Podczas zapisu wystąpił błąd",type:"negative"});case 9:case"end":return e.stop()}}),null,this,[[0,6]])}}},f=m,b=a("2877"),h=a("fe09"),g=Object(b["a"])(f,l,c,!1,null,null,null),y=g.exports;g.options.components=Object.assign({QPopupEdit:h["u"],QInput:h["m"]},g.options.components||{});var w=[{name:"createdAt",align:"center",label:"Data stworzenia",classes:"small-padding",headerClasses:"small-padding",field:function(e){var t=e.created_at;return t}},{name:"transportNumber",align:"center",classes:"elipsis, small-padding",headerClasses:"small-padding",label:"Nr transportu",field:function(e){var t=e.transport_number;return t}},{name:"transportType",align:"center",classes:"small-padding",headerClasses:"small-padding",label:"Typ",field:function(e){var t=e.transport_type;return t}},{name:"releasedAt",align:"center",classes:"small-padding",headerClasses:"small-padding",label:"Data potwierdzenia",field:function(e){var t=e.released_at;return t}},{name:"loadDate",align:"center",classes:"small-padding",headerClasses:"small-padding",label:"Data zał.",field:function(e){var t=e.load_date;return t}},{name:"loadHours",align:"center",classes:"small-padding",headerClasses:"small-padding",label:"Godzina",field:function(e){var t=e.load_hours;return t}},{name:"courierDetails",align:"center",label:"Kurier",style:"whiteSpace: normal",classes:"elipsis",headerClasses:"small-padding",field:function(e){var t=e.get_courier_details;return t}},{name:"relatedItems",align:"center",classes:"small-padding",headerClasses:"small-padding",label:"Szafki",field:function(e){var t=e.get_related_items;return t}},{name:"notes",align:"center",label:"Notatki",classes:"elipsis, notes-column",headerClasses:"small-padding, notes-column",field:function(e){var t=e.notes;return t}},{name:"suggested_pallet_quantity",align:"center",label:"Sugerowana ilość palet",classes:"elipsis, notes-column",headerClasses:"small-padding, notes-column",field:function(e){var t=e.suggested_pallet_quantity;return t}},{name:"producer_notes",align:"center",label:"Notatki producenta",classes:"elipsis, notes-column",headerClasses:"small-padding, notes-column",field:function(e){var t=e.producer_notes;return t}},{name:"products_in_production",align:"center",label:"Regały w produkcji",classes:"small-padding",headerClasses:"small-padding",field:function(e){var t=e.products_in_production;return t.join(", ")}},{name:"packageNumber",classes:"small-padding",align:"center",label:"Paczki",headerClasses:"small-padding",field:function(e){var t=e.get_package_number;return t}},{name:"logisticLabelUrl",align:"center",headerClasses:"small-padding",classes:"small-padding",label:"Etykieta"},{name:"additionalDocUrl",align:"center",classes:"small-padding",headerClasses:"small-padding",label:"Dokumenty"},{name:"documents",align:"center",classes:"small-padding",headerClasses:"small-padding",label:"Zip"},{name:"attachments_zip",align:"center",classes:"small-padding",headerClasses:"small-padding",label:"Zał."},{name:"were_files_downloaded",align:"center",classes:"small-padding",headerClasses:"small-padding",label:"Pobrano pliki"},{name:"actions",align:"center",classes:"small-padding",headerClasses:"small-padding",label:"Akcje"}],v=a("2fa3");function q(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function O(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?q(a,!0).forEach((function(t){o()(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):q(a).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var T={name:"TransportReleaseView",components:{EditTransportReleaseNote:y},props:{producerName:{type:String,required:!1,default:""}},data:function(){return{transportReleases:[],pagination:{sortBy:"desc",descending:!1,page:1,rowsPerPage:50,rowsNumber:10},papa_ppe:11,product:"",transport:"",notes:"",created_at_month:"",created_at_year:"",load_date:"",transport_type:"",is_estimated_delivery_date:!1,released:!1,loading:!0,selected:[],transportTypeOptions:i["g"],monthsOptions:i["e"],columns:w}},created:function(){this.getReleases(this.pagination)},methods:{reloadTable:function(e){this.getReleases(e.pagination);var t=e.pagination,a=t.page,n=t.rowsPerPage,s=t.sortBy,r=t.descending;this.pagination.page=a,this.pagination.rowsPerPage=n,this.pagination.sortBy=s,this.pagination.descending=r},getReleases:function(e){var t=this;this.loading=!0,this.$axios.get("".concat(window.LOGISTIC_URL,"/api/v1/transport_release"),{params:O({},this.$route.query,{page_number:e.page,page_size:e.rowsPerPage}),headers:{Authorization:"Token ".concat(window.USER_TOKEN)}}).then((function(e){t.transportReleases=e.data.results,t.pagination.rowsNumber=e.data.count,t.loading=!1,t.loadQueryParams()})).catch((function(){t.$q.notify({message:"Błąd pobierania danych. Przeładuj stronę.",type:"negative"})}))},getYearOptions:function(){for(var e=(new Date).getFullYear(),t=2021,a=[],n=t;n<=e;n++)a.push(n);return a},releaseTransport:function(e){var t=this;this.$axios.patch("".concat(window.LOGISTIC_URL,"/api/v1/transport_release/").concat(e,"/set_released_at_date"),{},{headers:{Authorization:"Token ".concat(window.USER_TOKEN)}}).then((function(){t.$q.notify({message:"Awizacja zakończona pomyślnie",type:"positive"}),t.getReleases(t.pagination)})).catch((function(){t.$q.notify({message:"Awizacja nieudana",type:"negative"})}))},confirmTransportRelease:function(e){var t=this;this.$axios.patch("".concat(window.LOGISTIC_URL,"/api/v1/transport_release/").concat(e,"/set_release_confirmed_at_date"),{},{headers:{Authorization:"Token ".concat(window.USER_TOKEN)}}).then((function(){t.$q.notify({message:"Potwierdzenie wypuszczenia towaru",type:"positive"}),t.getReleases(t.pagination)})).catch((function(){t.$q.notify({message:"Potwierdzenie nieudane",type:"negative"})}))},downloadFileFromApi:function(e){return this.$axios({method:"GET",url:e,headers:{Authorization:"Token ".concat(window.USER_TOKEN)},responseType:"blob"}).then((function(e){Object(v["a"])(e)}))},logisticManifest:function(e){var t=this;this.$axios.patch("".concat(window.LOGISTIC_URL,"/api/v1/transport_release/").concat(e,"/set_released_at_date"),{},{headers:{Authorization:"Token ".concat(window.USER_TOKEN)}}).then((function(){t.$q.notify({message:"Awizacja zakończona pomyślnie",type:"positive"}),t.getReleases(t.pagination)})).catch((function(){t.$q.notify({message:"Awizacja nieudana",type:"negative"})}))},getDocumentsZip:function(e){var t=this;this.downloadFileFromApi("".concat(window.LOGISTIC_URL,"/api/v1/transport_release/").concat(e,"/docs_zip")).then((function(){t.$q.notify({message:"OK",type:"positive"})})).catch((function(){t.$q.notify({message:"Błąd!",type:"negative"})}))},getAttachmentsZip:function(e){this.downloadFileFromApi("".concat(window.LOGISTIC_URL,"/api/v1/transport_release/").concat(e,"/attachments_zip"))},getAdditionalDocument:function(e){this.downloadFileFromApi("".concat(window.LOGISTIC_URL,"/api/v1/transport_release/").concat(e,"/additional_document"))},getLabels:function(e){this.downloadFileFromApi("".concat(window.LOGISTIC_URL,"/api/v1/transport_release/").concat(e,"/labels"))},isInexProducer:function(){return this.producerName&&"INEX"===this.producerName.toUpperCase()},getCsvUrlForRow:function(e){return"".concat(window.LOGISTIC_URL,"/api/v1/transport_release/").concat(e,"/get_inex_logistic_list_csv")},getRelatedItemsArray:function(e){return e.split(", ")},search:function(){var e={};this.product&&(e.product=this.product.trim()),this.transport&&(e.transport=this.transport.trim()),this.notes&&(e.notes=this.notes.trim()),this.created_at_month&&(e.created_at_month=this.created_at_month.value),this.created_at_year&&(e.created_at_year=this.created_at_year),this.load_date&&(e.load_date=this.load_date),this.transport_type&&(e.transport_type=this.transport_type.value),this.is_estimated_delivery_date&&(e.is_estimated_delivery_date=this.is_estimated_delivery_date),this.released&&(e.released=!0),this.$router.replace({query:e}),this.getReleases(this.pagination)},loadQueryParams:function(){var e=this;this.product=this.$route.query.product||this.product,this.transport=this.$route.query.transport||this.transport,this.notes=this.$route.query.notes||this.notes,this.created_at_month=this.monthsOptions.find((function(t){return t.value===e.$route.query.created_at_month}))||this.created_at_month,this.created_at_year=this.$route.query.created_at_year||this.created_at_year,this.load_date=this.$route.query.load_date||this.load_date,this.transport_type=this.transportTypeOptions.find((function(t){return t.value===e.$route.query.transport_type}))||this.transport_type,this.is_estimated_delivery_date=this.$route.query.is_estimated_delivery_date||this.is_estimated_delivery_date,this.released=!!this.$route.query.released}}},E=T,C=(a("3f31"),Object(b["a"])(E,n,s,!1,null,"67692264",null));t["default"]=C.exports;C.options.components=Object.assign({QPage:h["s"],QInput:h["m"],QSelect:h["w"],QIcon:h["k"],QPopupProxy:h["v"],QDate:h["g"],QCheckbox:h["e"],QBtn:h["b"],QTable:h["x"],QTd:h["y"],QBtnDropdown:h["c"],QList:h["r"],QItem:h["n"],QItemSection:h["p"],QItemLabel:h["o"]},C.options.components||{})},ee29:function(e,t,a){}}]);