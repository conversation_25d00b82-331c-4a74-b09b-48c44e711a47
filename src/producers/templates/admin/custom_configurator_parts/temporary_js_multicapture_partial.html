<script>

    // Main Multicapture object for storing all the settings.
    var multi = {

        // -1  Loop stopped on demand.
        status: 0,      //  0  <PERSON> is turned OFF / Completed.
                        //  1  Loop is running.
                        //  2  Loop is in the process and waits till the screenshot is done.

        action: null,   // Global variable for callback function ( e.g. Screenshot ) being called after each iteration of multicapture loop.

        screenshotFakeComplete: false, // For testing purposes - when changed to true, the main multicapture loop will continue without waiting for screenshot to be done.

        combinations: [], // List of all the combinations, generated after mutlicapture is started.

        iterationCounter: 0,
        iterationsNumber: 0,

        data: {
            density: [],
            width: [],
            height: [],
            //  depth: [],
            material: [],
            delay: null,
            action: function () {
                console.log('Callback function fires.');
            },
            background: null
        },

        // sliders[ sliderNumbers ].noUiSlider in configurator.
        sliderNumbers: {
            density: 0,
            width: 1,
            height: 3,
            //   depth: 4
        },

        parametersArr: [
            'density',
            'width',
            'height',
            'materials',

        ],

        parameters: {

            density: {

                id: 0, // used for accessing methods of global variable sliders[ id 0-4 ].noUiSlider...
                start: 1,
                end: 5,
                step: 1,
                values: [],
                iteration: 0
            },

            width: {

                id: 1,
                start: 1,
                end: 5,
                step: 1,
                values: [],
                iteration: 0
            },

            height: {

                id: 3,
                start: 1,
                end: 5,
                step: 1,
                values: [],
                iteration: 0
            },

            materials: {
                values: [],
                iteration: 0
            }

        },


        fn: {

            screenshotCompleted() {
                multi.status = 1;
            },

            fakeComplete(action) {
                if (action == 'on') multi.screenshotFakeComplete = true;
                if (action == 'off') multi.screenshotFakeComplete = false;
            },

            fakeCompleteToggle() {
                if (multi.screenshotFakeComplete) {
                    multi.screenshotFakeComplete = false;
                } else {
                    multi.screenshotFakeComplete = true;
                }
            },

            multicaptureModeToggle() {

                var body = $('body');
                var section = $('.multicapture__section');
                var button = $('.multicapture-switch');

                if (typeof multi.action == 'undefined' || multi.action == null) {
                    body.addClass('multicapture-mode');
                    section.slideDown();
                    button.addClass('active');

                    multi.action = multi.data.action;

                } else {
                    body.removeClass('multicapture-mode');
                    section.slideUp();
                    button.removeClass('active');
                    multi.action = null;
                }
            },

            // Disables sliders while loop is in progress.
            toggleRangeSlidersInteraction() {

                var section = $('.multicapture__section');

                if (multi.status > 0) {
                    section.addClass('is-in-progress');
                } else {
                    section.removeClass('is-in-progress');
                }
            },

            getMulticaptureObjectData() {


            },

            setMulticaptureObjectData() {

                var ranges = ['density', 'width', 'height' /*, 'depth'*/];
                for (var i = 0; i < ranges.length; i++) {
                    multi.data[ranges[i]] = multi.fn.getRangeData(ranges[i]);
                    multi.parameters[ranges[i]].start = multi.data[ranges[i]][0];
                    multi.parameters[ranges[i]].end = multi.data[ranges[i]][1];
                    multi.parameters[ranges[i]].step = multi.data[ranges[i]][2];
                }

                multi.data.material = multi.fn.getRangeMaterial();

                var parameters = ['delay', 'action', 'background'];
                for (var j = 0; j < parameters.length; j++) {
                    multi.data[parameters[j]] = multi.fn.getRangeParameter(parameters[j]);
                }
            },


            getRangeData(name) {

                var $rangeSelector = $('.multicapture__param--' + name);
                var resultsArr = [];

                var values = $rangeSelector.find('.js-multicapture__value');
                for (var j = 0; j < values.length; j++) {
                    resultsArr[j] = parseInt(values.eq(j).val(), 10);
                }

                return resultsArr;
            },

            setRangeData(name, values) {

                var $rangeSelector = $('.multicapture__param--' + name);
                var slider = $rangeSelector.find('.js-multicapture__slider');
                var $step = $rangeSelector.find('.js-multicapture-value--step');
                var newSliderRange = [values[0], values[1]];

                slider[0].noUiSlider.set(newSliderRange);
                $step.val(values[2] > 0 ? values[2] : 1);
            },

            setAllRanges(densityArr, widthArr, heightArr, materialArr) {
                multi.fn.setRangeData('density', densityArr);
                multi.fn.setRangeData('width', widthArr);
                multi.fn.setRangeData('height', heightArr);
                multi.fn.setRangeMaterial(materialArr);
            },

            nextShelf() {

                if (multi.iterationCounter >= multi.iterationsNumber) {
                    multi.status = ( -1 );
                    return false;
                }

                multi.status = 1;
                return true;
            },

            stopper() {
                multi.status = (-1);
                multi.fn.toggleRangeSlidersInteraction();
            },

            getRangeMaterial() {

                var $multiColorsChecked = $('.multicapture-material:checked');
                var resultArr = $multiColorsChecked.map(function () {
                    return parseInt($(this).val(), 10);
                }).get();
                return resultArr;
            },

            setRangeMaterial(values) {

                if (values.indexOf(0) > ( -1 ) || values.indexOf('white') > ( -1 )) {
                    $('.multicapture-material-white').attr('checked', 'checked')
                }
                if (values.indexOf(1) > ( -1 ) || values.indexOf('black') > ( -1 )) {
                    $('.multicapture-material-black').attr('checked', 'checked')
                }
                if (values.indexOf(3) > ( -1 ) || values.indexOf('gray') > ( -1 )) {
                    $('.multicapture-material-gray').attr('checked', 'checked')
                }

            },

            getRangeParameter(name) {

                var parameter = $('.multicapture__param--' + name);
                var parameterValue = parameter.find('.js-multicapture__value').val();
                var result = parseInt(parameterValue, 10);
                return result;
            },

            setRangeParameter(name, value) {

                var parameter = $('.multicapture__param--' + name);

                if (typeof value == 'number') {
                    value = parseInt(value, 10);
                }
                if (value == 'screenshot' || value == 'Screenshot' || value == 'screen') {
                    value = 0;
                }
                var parameterInput = parameter.find('.js-multicapture__value');
                parameterInput.val(value);
            },

            // - ustawianie wysokosci / ilosci rzedow - ivy.setHeight(4);
            //sliders[3].noUiSlider.set(4)
            // - ustawianie koloru / materialu - ivy.setColor( 0)

            updateWidthSlider(valueMin, valueMax, extraLabelVisible) {
                sliders[1].noUiSlider.updateOptions({
                    range: {
                        'min': valueMin,
                        'max': valueMax
                    }
                });
                var label = $('.custom-gui__label2');
                extraLabelVisible ? label.removeClass('force-hidden') : label.addClass('force-hidden');
            },

            // updateWidthSlider( 70, 450, 0) - before multicapture starts.
            // updateWidthSlider( 70, 240, 1) - after mutlicapture ends.

            calculateSliderPositions(rangeName) {
                var range = multi.parameters[rangeName];
                var arr = [];
                for (var i = range.start; i <= range.end; i = i + range.step) {
                    arr.push(i);
                }
                return arr;
            },

            populateAllValues() {
                const paramList = multi.parametersArr.filter( item => item !== 'materials')
                paramList.forEach(rangeName => {
                    multi.parameters[rangeName].values = multi.fn.calculateSliderPositions(rangeName)
                });
                multi.parameters.materials.values = multi.fn.getRangeMaterial();
            },

            getAllValuesArray() {
                let paramsArr = multi.parametersArr;
                let parameters = multi.parameters;
                let allValuesArr = [];

                paramsArr.forEach((item) => {
                    allValuesArr.push(parameters[item].values);
                });

                return allValuesArr;
            },

            getAllCombinations(arraysToCombine) {
                var divisors = [];
                for (var i = arraysToCombine.length - 1; i >= 0; i--) {
                    divisors[i] = divisors[i + 1] ? divisors[i + 1] * arraysToCombine[i + 1].length : 1;
                }

                function getPermutation(n, arraysToCombine) {
                    var result = [],
                        curArray;
                    for (var i = 0; i < arraysToCombine.length; i++) {
                        curArray = arraysToCombine[i];
                        result.push(curArray[Math.floor(n / divisors[i]) % curArray.length]);
                    }
                    return result;
                }

                var numPerms = arraysToCombine[0].length;
                for (var i = 1; i < arraysToCombine.length; i++) {
                    numPerms *= arraysToCombine[i].length;
                }

                var combinations = [];
                for (var i = 0; i < numPerms; i++) {
                    combinations.push(getPermutation(i, arraysToCombine));
                }
                return combinations;
            },

            setShelfConfiguration(shelfDataArr) {
                const [material, width, height, density] = shelfDataArr;
                const slidersData = multi.parameters;
                const currentMaterial = ivy.material;
                const currentWidth = ivy.width / 10;
                const currentHeightRows = ivy.rows;
                const currentDensity = ivy.getProperty();

                if (currentMaterial !== material) {
                    ivy.setColor(material);
                }

                if (currentWidth != width) {
                    sliders[slidersData.width.id].noUiSlider.set(width);
                }

                if (currentHeightRows != height) {
                    sliders[slidersData.height.id].noUiSlider.set(height);
                }

                if (currentDensity != density) {
                    sliders[slidersData.density.id].noUiSlider.set(density);
                }
            },

            cycleThroughAllCombos(combinationsArr, delay, actionParameter, callback) {
                //let currentCount = multi.iterationCounter;
                let waitForCompletedCount = 0;

                //const combinationsNumber = multi.iterationsNumber;
                const waitTime = 15; // seconds.

                delay = parseInt(delay, 10);

                loop();

                function loop() {

                    if (multi.status > 0 && multi.iterationCounter < multi.iterationsNumber) {
                        console.log('...Loop started');

                        setTimeout(function () {

                            if (multi.status < 0) {
                                console.log('========== Loop Stopped on demand.');
                                multi.fn.toggleRangeSlidersInteraction();
                                return
                            }

                            console.log('Loop Iteration # ' + ( multi.iterationCounter ));
                            //console.log( 'iterable', combinationsArr[ currentCount ] );
                            multi.fn.setShelfConfiguration(combinationsArr[multi.iterationCounter]);

                            multi.iterationCounter += 1;

                            //Calling selected action ( e.g. screenshot ) with selected parameter ( e.q. solid gray background )
                            multi.action(actionParameter);

                            // Setting flag to '2'' means it waits for the screenshot to complete
                            // After screenshot is completed, status is turned back to '1'.
                            multi.status = 2;

                            var checkIfCompleted = setInterval(function () {

                                if (multi.iterationCounter >= multi.iterationsNumber && ( multi.status == 1 || multi.screenshotFakeComplete == true )) {
                                    waitForCompletedCount = 0;
                                    clearInterval(checkIfCompleted);
                                    console.log('========== Loop fully completed.');
                                }

                                if (multi.status == 1 || multi.screenshotFakeComplete == true) {
                                    waitForCompletedCount = 0;
                                    clearInterval(checkIfCompleted);
                                    loop();
                                }

                                if (waitForCompletedCount >= waitTime) {
                                    console.log('========== Waiting time exceeded (' + waitTime + ' seconds). Aborting multicapture.');
                                    clearInterval(checkIfCompleted);
                                    multi.fn.toggleRangeSlidersInteraction();
                                    return false;
                                }

                                waitForCompletedCount = waitForCompletedCount + 1;

                                if (waitForCompletedCount % 2 == 0) {
                                    console.log('Waiting for screenshot to be completed. ', waitForCompletedCount + 'sec.');
                                }

                                if (waitForCompletedCount == 7) {
                                    console.log('For testing purposes click the button just below "GO!", that reads: "Screenshot fake complete toggle"');
                                }

                            }, 500);

                        }, delay);

                    } else if (multi.status < 0) {
                        console.log('========== Loop Stopped on demand.');
                        multi.fn.toggleRangeSlidersInteraction();
                        return;
                    } else {
                        console.log('========== Loop fully completed.');
                        multi.fn.toggleRangeSlidersInteraction();

                        return;
                    }
                }

            },

            starter() {

                if (!multi.fn.getRangeMaterial().length) {
                    alert('Please choose colors in "Material Range" first.');
                    return;
                }
                multi.status = 1;
                multi.fn.toggleRangeSlidersInteraction();
                multi.fn.setMulticaptureObjectData();
                multi.fn.populateAllValues();
                multi.combinations = multi.fn.getAllCombinations(multi.fn.getAllValuesArray());
                multi.iterationsNumber = multi.combinations.length;
                multi.iterationCounter = 0;

                multi.fn.updateWidthSlider(70, 450, 0); // Extends the width slider to cover the full spectrum - only for looping time.
                multi.fn.cycleThroughAllCombos(multi.combinations, multi.fn.getRangeParameter('delay'), multi.fn.getRangeParameter('background')) // Main Looping function.
            }


        },

        listeners: {

            create: function () {

                // Temporary, multicaptureMode always enabled on the first run {
                $(document).ready(function () {
                    multi.fn.multicaptureModeToggle();
                });

                // Switches on the multicapture-mode.
                $('.multicapture-switch').on('click', function () {
                    multi.fn.multicaptureModeToggle();
                });

                // Starts the multicapture-loop process.
                $('.multicapture-starter').on('click', function () {
                    multi.fn.starter();
                });


                // Stops capturing on demand.
                $('.multicapture-stopper').on('click', function () {
                    multi.fn.stopper();
                });

                $('.multicapture__value--color-box input').change(function () {
                    if ($(this).is(':checked')) {
                        $(this).closest('.checkmark-small--left').addClass('checkmark-small');
                    } else {
                        $(this).closest('.checkmark-small--left').removeClass('checkmark-small');
                    }
                });

                $('.multicapture-fake-complete').on('click', function () {
                    $(this).toggleClass('full-opacity');
                    multi.fn.fakeCompleteToggle();

                });

            }

        },

        controls: {

            create: function () {

                // Sliders mechanics.

                // Density

                var multicaptureSliderDensity = document.getElementsByClassName('js-multicapture__slider')[0];
                noUiSlider.create(multicaptureSliderDensity, {
                    start: [40, 60],
                    connect: true,
                    behaviour: 'drag',
                    range: {
                        'min': 0,
                        'max': 100
                    },
                    format: {
                        to: function (value) {
                            return parseInt(value, 10);
                        },
                        from: function (value) {
                            return parseInt(value, 10);
                        }
                    },
                    cssPrefix: 'mUi-'
                });

                var densityValue = document.getElementsByClassName('js-multicapture-value--density');
                multicaptureSliderDensity.noUiSlider.on('update', function (values, handle) {
                    var value = values[handle];
                    if (handle) {
                        densityValue[1].value = Math.round(value);
                    } else {
                        densityValue[0].value = Math.round(value);
                    }
                });
                densityValue[0].addEventListener('change', function () {
                    multicaptureSliderDensity.noUiSlider.set([this.value, null]);
                });
                densityValue[1].addEventListener('change', function () {
                    multicaptureSliderDensity.noUiSlider.set([null, this.value]);
                });

                // Width

                var multicaptureSliderWidth = document.getElementsByClassName('js-multicapture__slider')[1];
                noUiSlider.create(multicaptureSliderWidth, {
                    start: [90, 110],
                    connect: true,
                    behaviour: 'drag',
                    range: {
                        'min': 70,
                        'max': 450
                    },
                    cssPrefix: 'mUi-'
                });

                var widthValue = document.getElementsByClassName('js-multicapture-value--width');
                multicaptureSliderWidth.noUiSlider.on('update', function (values, handle) {
                    var value = values[handle];
                    if (handle) {
                        widthValue[1].value = Math.round(value);
                    } else {
                        widthValue[0].value = Math.round(value);
                    }
                });
                widthValue[0].addEventListener('change', function () {
                    multicaptureSliderWidth.noUiSlider.set([this.value, null]);
                });
                widthValue[1].addEventListener('change', function () {
                    multicaptureSliderWidth.noUiSlider.set([null, this.value]);
                });


                // Height

                var multicaptureSliderHeight = document.getElementsByClassName('js-multicapture__slider')[2];
                noUiSlider.create(multicaptureSliderHeight, {
                    start: [4, 6],
                    step: 1,
                    connect: true,
                    behaviour: 'drag',
                    range: {
                        'min': 1,
                        'max': 8
                    },
                    cssPrefix: 'mUi-'
                });

                var heightValue = document.getElementsByClassName('js-multicapture-value--height');
                multicaptureSliderHeight.noUiSlider.on('update', function (values, handle) {
                    var value = values[handle];
                    if (handle) {
                        heightValue[1].value = Math.round(value);
                    } else {
                        heightValue[0].value = Math.round(value);
                    }
                });
                heightValue[0].addEventListener('change', function () {
                    multicaptureSliderHeight.noUiSlider.set([this.value, null]);
                });
                heightValue[1].addEventListener('change', function () {
                    multicaptureSliderHeight.noUiSlider.set([null, this.value]);
                });

                // Depth

                /*

                var multicaptureSliderDepth = document.getElementsByClassName('js-multicapture__slider')[3];
                noUiSlider.create(multicaptureSliderDepth, {
                    start: [32, 32],
                    connect: true,
                    behaviour: 'drag',
                    range: {
                        'min': 20,
                        'max': 80
                    },
                    cssPrefix: 'mUi-'
                });

                var depthValue = document.getElementsByClassName('js-multicapture-value--depth');
                multicaptureSliderDepth.noUiSlider.on('update', function (values, handle) {
                    var value = values[handle];
                    if (handle) {
                        depthValue[1].value = Math.round(value);
                    } else {
                        depthValue[0].value = Math.round(value);
                    }
                });
                depthValue[0].addEventListener('change', function () {
                    multicaptureSliderDepth.noUiSlider.set([this.value, null]);
                });
                depthValue[1].addEventListener('change', function () {
                    multicaptureSliderDepth.noUiSlider.set([null, this.value]);
                });

                */

            }

        }

    };

    (function multicaptureInit() {
        multi.listeners.create();
        multi.controls.create();
    })();




</script>