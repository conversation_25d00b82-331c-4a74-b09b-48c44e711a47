{% load producers_extra %}
{% load static %}
<!DOCTYPE html>
<html lang="en" >
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <title>CSTM producer panel</title>
    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">
    <link rel="stylesheet" href="{% static 'css/bootcards-desktop.css' %}">
    <link href="{% static 'css/heroic-features.css' %}" rel="stylesheet">

    <script src="//ajax.googleapis.com/ajax/libs/jquery/2.1.0/jquery.min.js"></script>
    <script type="text/javascript" src="{% static 'js/bootstrap.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/bootcards.min.js' %}"></script>

    <script>
        $(document).ready(function(){
        bootcards.init( {
          offCanvasBackdrop : true,
          offCanvasHideOnMainClick : true,
          enableTabletPortraitMode : true,
          disableRubberBanding : true,
          disableBreakoutSelector : 'a.no-break-out'
                });
                })
    </script>
    {% block extra_head %}

    {% endblock %}
</head>

<body>

{% block menu %}
    {% show_producer_menu %}
{% endblock menu %}

    <div class="container">
    {% if messages %}
        <ul class="messages">
            {% for message in messages %}
                <div class="alert alert-info alert-dismissible" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    {{ message }}
                </div>
            {% endfor %}
        </ul>
    {% endif %}





    {% block content %}

    {% endblock %}

    <footer>
        <div class="row">
            <div class="col-lg-12">
                <p>Copyright &copy; CSTM {% now "Y" %}</p>
            </div>
        </div>
    </footer>
</div>

</body>
</html>
