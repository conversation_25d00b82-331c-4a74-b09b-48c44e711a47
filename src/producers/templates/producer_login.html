{% extends "producer_base.html" %}

{% block content %}

   <div class="row">
        <div class="col-md-3">
            <h3>Login</h3>
        </div>
    </div>

    <div class="inner cover col-md-3 hero-feature">
        <form method="post" action="#" accept-charset="UTF-8">
            {% csrf_token %}
            {% if form.errors %}
                <div class="alert alert-danger ng-hide">
                    Incorrect Username or Password!
                </div>
            {% endif %}

            <div class="form-group">
                <input class="form-control" placeholder="Username" type="text" name="username" id="id_username" >
            </div>
            <div class="form-group">
                <input class="form-control" placeholder="Password" type="password" name="password" id="id_password" >
            </div>
            <input type="hidden" name="next" value="/pages/producer_status/" />
            <button class="btn-info btn" type="submit">Login</button>
        </form>
    </div>

{% endblock %}