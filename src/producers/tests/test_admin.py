import datetime

from django.utils import timezone

import pytest

from freezegun import freeze_time

from admin_customization.sites import SitePlus
from producers.admin import ProductAdmin
from producers.choices import ProductStatus
from producers.models import Product


@pytest.mark.django_db
class TestProductAdmin:
    def setup(self):
        self.admin_site = SitePlus()

    def test_estimated_delivery_time_log_should_return_empty_when_no_estimated_delivery_logs(  # noqa E501
        self, order_factory, product_factory
    ):
        order_no_estimated_delivery_time_log = order_factory(
            estimated_delivery_time=None, estimated_delivery_time_log=[]
        )
        product = product_factory(
            status=ProductStatus.NEW,
            order=order_no_estimated_delivery_time_log,
        )

        product_admin = ProductAdmin(Product, self.admin_site)

        estimated_delivery_time_log_html = product_admin.estimated_delivery_time_log(
            product
        )
        assert estimated_delivery_time_log_html == '-'

    @freeze_time(datetime.datetime(2020, 4, 16, 11, tzinfo=datetime.UTC))
    def test_estimated_delivery_time_log_should_return_formatted_when_estimated_delivery_logs(  # noqa E501
        self, order_factory, product_factory
    ):
        second_estimated_delivery_time = timezone.now()
        first_estimated_delivery_time = timezone.now() - datetime.timedelta(days=1)
        order_with_estimated_delivery_time_logs = order_factory(
            estimated_delivery_time=second_estimated_delivery_time,
            estimated_delivery_time_log=[
                second_estimated_delivery_time.isoformat(),
                first_estimated_delivery_time.isoformat(),
            ],
        )
        product = product_factory(
            status=ProductStatus.NEW,
            order=order_with_estimated_delivery_time_logs,
        )

        product_admin = ProductAdmin(Product, self.admin_site)

        estimated_delivery_time_log_html = product_admin.estimated_delivery_time_log(
            product
        )
        assert (
            estimated_delivery_time_log_html
            == '<div>2020-04-16</div>\n<div>2020-04-15</div>'
        )
