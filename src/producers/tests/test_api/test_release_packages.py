from unittest import mock

from django.conf import settings
from django.urls import reverse

import pytest

from rest_framework import status

from producers.choices import ProductStatus


@pytest.mark.django_db
def test_post_release_package_with_token(api_client):
    url = reverse('release-package-list')
    data = {
        'package': 'package',
        'product_id': 123,
        'released_at': '2021-01-01T00:00:00Z',
    }
    response = api_client.post(url, data=data)
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


@pytest.mark.django_db
def test_post_release_package_success(
    api_client,
    manufacturer_inex_with_product,
):
    product, token = manufacturer_inex_with_product
    url = reverse('release-package-list')
    data = {
        'package': 1,
        'product': product.id,
        'released_at': '2021-05-01T00:00:00Z',
    }
    response = api_client.post(
        url, data=data, format='json', HTTP_AUTHORIZATION=f'Token {token}'
    )
    assert response.status_code == status.HTTP_201_CREATED


@pytest.mark.django_db
@mock.patch('producers.models.Product.get_packaging_quantity')
def test_post_release_last_package(
    get_packaging_quantity_mock,
    api_client,
    manufacturer_inex_with_product,
    manufacturer_released_package_factory,
    user_factory,
):
    user_factory(username=settings.CELERY_SUPERUSER_USERNAME)
    product, token = manufacturer_inex_with_product
    get_packaging_quantity_mock.return_value = 2
    manufacturer_released_package_factory(
        product=product,
        package=1,
        manufacturer=product.manufactor,
        released_at='2021-01-01T00:00:00Z',
    )

    url = reverse('release-package-list')
    data = {
        'package': 2,
        'product': product.id,
        'released_at': '2021-01-01T00:00:00Z',
    }
    response = api_client.post(
        url, data=data, format='json', HTTP_AUTHORIZATION=f'Token {token}'
    )
    assert response.status_code == status.HTTP_201_CREATED

    product.refresh_from_db()
    assert product.status == ProductStatus.TO_BE_SHIPPED.value


@pytest.mark.django_db
@mock.patch('producers.models.Product.get_packaging_quantity')
def test_post_release_last_package_for_product_with_wrong_status(
    get_packaging_quantity_mock,
    api_client,
    manufacturer_inex_with_product,
    manufacturer_released_package_factory,
    user_factory,
):
    user_factory(username=settings.CELERY_SUPERUSER_USERNAME)
    product, token = manufacturer_inex_with_product
    product.status = ProductStatus.QUALITY_CONTROL.value
    product.save()
    get_packaging_quantity_mock.return_value = 2
    manufacturer_released_package_factory(
        product=product,
        package=1,
        manufacturer=product.manufactor,
        released_at='2021-01-01T00:00:00Z',
    )

    url = reverse('release-package-list')
    data = {
        'package': 2,
        'product': product.id,
        'released_at': '2021-01-01T00:00:00Z',
    }
    response = api_client.post(
        url, data=data, format='json', HTTP_AUTHORIZATION=f'Token {token}'
    )
    assert response.status_code == status.HTTP_201_CREATED

    product.refresh_from_db()
    assert product.status == ProductStatus.QUALITY_CONTROL
