from producers.gh.moduly_glowne.ivy_elements import ElementProduction

SAMPLE_W_ELEMENT = {
    'y_index_bottom': 0,
    'material_usage': {
        'service_T1_DTR_element-back': 1,
        'material_ABS_white_16-04': 1.4984000000000002,
        'material_chipboard_melamine-white_12': 0.13317856,
        'service_T1_DTR_banding': 1.4984000000000002,
        'fitting_connector_black_spring-pin': 4,
    },
    'x_domain': [1800.0, 48600.0],
    'row_depth': 1200.0,
    'surname': 'W1',
    'elem_type': 'B',
    'material_usage_nesting': {
        'service_T1_DTR_element-back': 1,
        'material_ABS_white_16-04': 1.4984000000000002,
        'material_chipboard_melamine-white_12': 0.13317856,
        'service_T1_DTR_banding': 1.4984000000000002,
        'fitting_connector_black_spring-pin': 4,
    },
    'cost': 19.658946212,
    'main_codename': 'material_chipboard_melamine-white_12',
    'weight': 1.26563,
    'name': 'B_01.08',
    'y_domain': [1800.0, 30000.0],
    'package_info': {
        'z_domain': [171.0, 183.0],
        'pack_id': 3,
        'pack_section': 1,
        'y_domain': [5, 287],
        'pack_level': 0,
        'x_domain': [152, 620],
        'pack_section_item': 0,
    },
    'module': 1,
    'components': [
        {
            'z_nesting_offset': 0,
            'x_nesting_offset': 100,
            'z_domain': [200, 1400],
            'y_domain': [1820.0, 29980.0],
            'x_domain': [1820.0, 48580.0],
            'y_nesting_offset': 100,
            'material_codename': 'material_chipboard_melamine-white_12',
            'main': True,
            'type': 'back',
        }
    ],
    'fittings': None,
    'name_packaging': 10,
    'material_usage_weights': {
        'service_T1_DTR_element-back': 1,
        'material_ABS_white_16-04': 1.4984000000000002,
        'material_chipboard_melamine-white_12': 0.13167616000000001,
        'service_T1_DTR_banding': 1.4984000000000002,
        'fitting_connector_black_spring-pin': 4,
    },
    'y_index': 1,
    'z_domain': [200, 1400],
}


# TODO: Delete it after all elements labels will be downloaded from PS
class TestLabelsNames:
    def test_element_matrix(self):
        """Test elements naming x shelf type."""
        for shelf_type, label_text in {0: 'T 1', 1: 'T 2', 2: 'T 1'}.items():
            elem = ElementProduction(
                serialized_obj=SAMPLE_W_ELEMENT,
                id_manufactor=1,
                id_production=123,
                shelf_type=shelf_type,
            )
            barcode, barcode_label = elem.get_element_label_name(
                elem.surname,
                elem.id_production,
                elem.id_manufactor,
                elem.shelf_type,
            )

            assert label_text in barcode_label
            assert elem.surname in barcode
