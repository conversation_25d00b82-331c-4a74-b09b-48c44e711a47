import datetime
import random

from django.urls import reverse

import pytest

from producers.api.views import ManufacturerProductStatusViewSet
from producers.models import Product
from producers.tests.factories import ManufacturerReleasedPackageFactory


@pytest.mark.django_db
def test_manufactor_get_only_his_products(
    api_client, manufacturer_drewtur_with_product, manufacturer_inex_with_product
):
    product_dtr, token = manufacturer_drewtur_with_product
    product_dtr.status = random.choice(
        ManufacturerProductStatusViewSet.permitted_statuses
    )
    product_dtr.save(update_fields=['status'])
    product_inex, _ = manufacturer_inex_with_product
    product_inex.status = random.choice(
        ManufacturerProductStatusViewSet.permitted_statuses
    )
    product_inex.save(update_fields=['status'])

    assert Product.objects.count() == 2
    response = api_client.get(
        reverse('products-status-list'),
        format='json',
        HTTP_AUTHORIZATION=f'Token {token.key}',
    )
    assert len(response.json()) == 1 and product_dtr.id == response.json()[0]['id']


@pytest.mark.django_db
def test_product_details(api_client, manufacturer_drewtur_with_product):
    product, token = manufacturer_drewtur_with_product
    product.status = random.choice(ManufacturerProductStatusViewSet.permitted_statuses)
    product.save(update_fields=['status'])
    ManufacturerReleasedPackageFactory(
        product=product,
        package=1,
        released_at=datetime.datetime.now(),
    )
    ManufacturerReleasedPackageFactory(
        product=product,
        package=3,
        released_at=datetime.datetime.now(),
    )
    response = api_client.get(
        reverse('products-status-detail', args=(product.id,)),
        format='json',
        HTTP_AUTHORIZATION=f'Token {token.key}',
    )
    expected = {
        'id': product.id,
        'status': product.status,
        'accepted_packages': ['1', '3'],
    }
    assert response.json()['id'] == expected['id']
    assert response.json()['status'] == expected['status']
    assert sorted(response.json()['accepted_packages']) == sorted(
        expected['accepted_packages']
    )
