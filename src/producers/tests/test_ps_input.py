import pytest

from producers.production_system_utils.client import ProductionSystemClient


class TestPsClientCheckExceptionHandling:
    """Test whether suppress errors works as intended."""

    def test_suppress_errors_on(self):
        """With suppress errors on, client always returns a production JSON
        with error message stored under `errors`."""
        with ProductionSystemClient(suppress_errors=True) as client:
            exception = ValueError('Test exception')
            result = client._handle_exception(exception)
            assert 'errors' in result
            assert exception.args[0] in result['errors'][0]

    def test_suppress_errors_off(self):
        """With suppress errors off, an exception should be raised."""
        with ProductionSystemClient(suppress_errors=False) as client:
            exception = ValueError('Test exception')
            with pytest.raises(ValueError):
                client._handle_exception(exception)
