import datetime
import re

from django.contrib.auth.models import User
from django.utils import timezone

import pytest
import requests
import requests_mock

from requests.exceptions import HTTPError
from rest_framework import status

from gallery.tests.factories import JettyFactory
from orders.tests.factories import OrderItemFactory
from producers import (
    errors,
    models,
    services,
)
from producers.choices import (
    BatchType,
    ProductStatus,
)
from producers.models import Manufactor
from producers.services.match_usage import MatchUsageWithMaterialsHelper
from producers.tests.utils import ProductsBatchingTestMixin
from producers.utils import MebleplStatusCode

DATETIME_NOW = datetime.datetime(2020, 4, 16, 11, tzinfo=datetime.UTC)


@pytest.mark.django_db
class TestProductBatchService(ProductsBatchingTestMixin):
    service_class = services.ProductBatchService

    @pytest.fixture()
    def products_without_explicit_material(self, product_details_jetty):
        yield self._create_products(
            product_details_jetty=product_details_jetty,
            order_item=None,
        )

    @pytest.fixture()
    def products_with_explicit_material(self, product_details_jetty):
        yield self._create_products(
            product_details_jetty=product_details_jetty,
            order_item=OrderItemFactory(
                order_item=JettyFactory(material=0, shelf_type=0)
            ),
        )

    def test_create_creates_expected_product_batch(
        self,
        products_with_explicit_material,
        mocker,
    ):
        mocker.patch('producers.internal_api.events.ProductRefreshEvent.execute')
        mocker.patch('producers.product_updater.ProductStatusUpdater.change_status')
        mocker.patch(
            'producers.physical_version_adjustment.PhysicalVersionAdjustment.apply_to'
        )
        batch_type = BatchType.STANDARD
        for product in products_with_explicit_material:
            product.refresh_from_db()
        assert models.ProductBatch.objects.all().count() == 0
        self.service_class().create(
            products_with_explicit_material,
            self.manufactor,
            batch_type=batch_type,
        )
        assert models.ProductBatch.objects.all().count() == 1
        product_batch = models.ProductBatch.objects.all().first()
        assert product_batch.manufactor == self.manufactor
        assert product_batch.product_type == self.product_type
        assert product_batch.batch_type == batch_type
        assert [
            product.id for product in product_batch.batch_items.all().order_by('id')
        ] == [product.id for product in products_with_explicit_material]

    def test_create_updates_batched_products(
        self, products_with_explicit_material, mocker
    ):
        mocker.patch('producers.internal_api.events.ProductRefreshEvent.execute')
        mocker.patch(
            'producers.product_updater.get_owner_or_default_user',
            return_value=User.objects.first(),
        )
        mocker.patch(
            'producers.physical_version_adjustment.PhysicalVersionAdjustment.apply_to'
        )
        assert models.ProductBatch.objects.all().count() == 0
        self.service_class().create(
            products_with_explicit_material,
            self.manufactor,
        )
        for product in products_with_explicit_material:
            product.refresh_from_db()
        assert models.ProductBatch.objects.all().count() == 1
        product_batch = models.ProductBatch.objects.all().first()
        for product in products_with_explicit_material:
            assert product.batch == product_batch
            assert product.manufactor == self.manufactor
            assert product.status == ProductStatus.ASSIGNED_TO_PRODUCTION

    @pytest.mark.django_db(transaction=True)
    def test_create_regenerates_batch_and_batched_products_files(
        self,
        products_with_explicit_material,
        mocker,
    ):
        mocker.patch('producers.internal_api.events.ProductRefreshEvent.execute')
        mocker.patch('producers.product_updater.ProductStatusUpdater.change_status')
        mocker.patch(
            'producers.physical_version_adjustment.PhysicalVersionAdjustment.apply_to'
        )
        assert models.ProductBatch.objects.all().count() == 0
        self.service_class().create(
            products_with_explicit_material,
            self.manufactor,
        )
        assert models.ProductBatch.objects.all().count() == 1
        calls_number = {mock_name: self.product_count for mock_name in self.mocks}
        calls_number['ProductBatch.generate_all_files'] = 1
        for mock_name, mocked_method in self.mocks.items():
            mocked_method.assert_called()
            assert mocked_method.call_count == calls_number[mock_name]

    def test_auto_fill_material_description_order_item(
        self,
        products_with_explicit_material,
        mocker,
    ):
        mocker.patch('producers.internal_api.events.ProductRefreshEvent.execute')
        mocker.patch('producers.product_updater.ProductStatusUpdater.change_status')
        mocker.patch(
            'producers.physical_version_adjustment.PhysicalVersionAdjustment.apply_to'
        )

        batch = self.service_class().create(
            products_with_explicit_material,
            self.manufactor,
        )
        assert batch.material_description == 'White laminate Egger'


@pytest.mark.django_db
class TestMebleplApiService:
    @pytest.fixture(autouse=True)
    def setup_test(self, settings, product_factory, product_batch_factory):
        settings.MEBLEPL_API_URL = 'https://example.com'
        self.service = services.MebleplApiService()
        self.product = product_factory(
            updated_at=datetime.datetime(2020, 6, 10),
            batch=product_batch_factory(created_at=datetime.datetime(2022, 12, 10)),
        )
        self.url = re.compile(settings.MEBLEPL_API_URL + '/zamowienie/.*')

    def test_get_decoded_resource_id(self):
        assert self.service.get_decoded_resource_id(
            product_id=self.product.id
        ) == '{}/{}/P/TYLKO'.format(self.product.id, str(timezone.now().year)[-2:])

    def test_make_get_request__ok(self):
        with requests_mock.Mocker(session=self.service.session) as session_mock:
            session_mock.get(
                self.url,
                json={'status': MebleplStatusCode.READY_TO_BE_SHIPPED},
                status_code=status.HTTP_200_OK,
            )
            assert self.service.make_get_request(
                '/zamowienie', self.service.get_decoded_resource_id(self.product.id)
            ) == {'status': MebleplStatusCode.READY_TO_BE_SHIPPED}

    def test_make_get_request__bad(self, mocker):
        self.service.session = mocker.MagicMock(
            get=mocker.MagicMock(side_effect=requests.ConnectionError)
        )

        with pytest.raises(errors.Unavailable):
            self.service.make_get_request(
                '/zamowienie', self.service.get_decoded_resource_id(self.product.id)
            )

    def test_get_order_status__ok_response(self, mocker):
        self.service.make_get_request = mocker.MagicMock(
            return_value={
                'status': MebleplStatusCode.READY_TO_BE_SHIPPED,
                'opis': 'test',
            },
            status_code=status.HTTP_200_OK,
        )

        assert self.service.get_order_status(
            self.product.id
        ) == services.MebleplOrderResponse(
            status=MebleplStatusCode.READY_TO_BE_SHIPPED, description='test'
        )

    def test_get_order_status__bad_response(self, mocker):
        response_mock = mocker.MagicMock(
            status_code=status.HTTP_401_UNAUTHORIZED,
            headers={'content-type': 'application/json'},
        )
        self.service.make_get_request = mocker.MagicMock(
            side_effect=HTTPError(response=response_mock)
        )

        with pytest.raises(errors.MebleplAPIError):
            self.service.get_order_status(self.product.id)


class TestMatchUsageWithMaterialsHelper:
    def test_should_usage_be_increased_by_loss_factor_should_return_true_when_name_fitting_and_not_drewtur(  # noqa: E501
        self,
    ):
        is_usage_increased_by_loss_factor = (
            MatchUsageWithMaterialsHelper.should_usage_be_increased_by_loss_factor(
                'material_data',
                'fitting_handle_grey',
                Manufactor.MEBLE_PL,
            )
        )

        assert is_usage_increased_by_loss_factor

    def test_should_usage_be_increased_by_loss_factor_should_return_false_when_drewtur_and_name_fitting_handle(  # noqa: E501
        self,
    ):
        is_usage_increased_by_loss_factor = (
            MatchUsageWithMaterialsHelper.should_usage_be_increased_by_loss_factor(
                '',
                'fitting_handle_grey',
                Manufactor.DREWTUR,
            )
        )

        assert not is_usage_increased_by_loss_factor

    def test_should_usage_be_increased_by_loss_factor_should_return_true_when_material_data_and_name_material(  # noqa: E501
        self,
    ):
        is_usage_increased_by_loss_factor = (
            MatchUsageWithMaterialsHelper.should_usage_be_increased_by_loss_factor(
                'material_data',
                'material_',
                Manufactor.MEBLE_PL,
            )
        )

        assert is_usage_increased_by_loss_factor

    def test_should_usage_be_increased_by_loss_factor_should_return_true_when_material_data_and_name_fitting(  # noqa: E501
        self,
    ):
        is_usage_increased_by_loss_factor = (
            MatchUsageWithMaterialsHelper.should_usage_be_increased_by_loss_factor(
                'material_data',
                'fitting_',
                Manufactor.MEBLE_PL,
            )
        )

        assert is_usage_increased_by_loss_factor

    def test_should_usage_be_increased_by_loss_factor_should_return_false_when_material_data_and_name_diff(  # noqa: E501
        self,
    ):
        is_usage_increased_by_loss_factor = (
            MatchUsageWithMaterialsHelper.should_usage_be_increased_by_loss_factor(
                'material_data',
                'neiter_material_nor_fitting',
                Manufactor.MEBLE_PL,
            )
        )

        assert not is_usage_increased_by_loss_factor

    def test_should_usage_be_increased_by_loss_factor_should_return_true_when_type_semiproducts_data_and_name_in_list(  # noqa: E501
        self,
    ):
        is_usage_increased_by_loss_factor = (
            MatchUsageWithMaterialsHelper.should_usage_be_increased_by_loss_factor(
                'semiproducts_data',
                'semiproduct_blank_ash_A',
                Manufactor.MEBLE_PL,
            )
        )

        assert is_usage_increased_by_loss_factor

    def test_should_usage_be_increased_by_loss_factor_should_return_false_when_type_semiproducts_data_and_name_not_in_list(  # noqa: E501
        self,
    ):
        is_usage_increased_by_loss_factor = (
            MatchUsageWithMaterialsHelper.should_usage_be_increased_by_loss_factor(
                'semiproducts_data',
                'not_in_add_loss_factor',
                Manufactor.MEBLE_PL,
            )
        )

        assert not is_usage_increased_by_loss_factor

    def test_should_usage_be_increased_by_loss_factor_should_return_true_when_type_packaging_data(  # noqa: E501
        self,
    ):
        is_usage_increased_by_loss_factor = (
            MatchUsageWithMaterialsHelper.should_usage_be_increased_by_loss_factor(
                'packaging_data', '', Manufactor.MEBLE_PL
            )
        )

        assert is_usage_increased_by_loss_factor
