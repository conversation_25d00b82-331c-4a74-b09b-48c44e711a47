import datetime

from unittest.mock import patch

from django.utils import timezone

import pytest
import requests

from freezegun import freeze_time

from producers import models
from producers.choices import (
    BatchType,
    ProductStatus,
)
from producers.production_system_utils.client import ps_client_metrics
from producers.utils import (
    format_order_estimated_delivery_time_log,
    is_any_batch_complaint,
)


class TestPsClientMetricsDecorator:
    @patch('producers.production_system_utils.client.time')
    @patch('producers.production_system_utils.client.metrics_client')
    def test_metric_status_no_exception(self, metrics_client, time):
        @ps_client_metrics
        def function():
            return

        function()
        args, kwargs = metrics_client().timing.call_args

        assert time.time.call_count == 2
        assert metrics_client().timing.called_once()
        assert ['method_name:function', 'status:success'] == kwargs['tags']

    @patch('producers.production_system_utils.client.time')
    @patch('producers.production_system_utils.client.metrics_client')
    def test_metric_status_response_http_error(self, metrics_client, time):
        @ps_client_metrics
        def function():
            raise requests.HTTPError

        with pytest.raises(requests.HTTPError):
            function()
        args, kwargs = metrics_client().timing.call_args

        assert time.time.call_count == 2
        assert metrics_client().timig.called_once()
        assert ['method_name:function', 'status:response_http_error'] == kwargs['tags']

    @patch('producers.production_system_utils.client.time')
    @patch('producers.production_system_utils.client.metrics_client')
    def test_metric_status_exception(self, metrics_client, time):
        @ps_client_metrics
        def function():
            raise Exception

        with pytest.raises(Exception):
            function()
        args, kwargs = metrics_client().timing.call_args

        assert time.time.call_count == 2
        assert metrics_client().timig.called_once()
        assert ['method_name:function', 'status:error'] == kwargs['tags']


@pytest.mark.django_db
class TestFormatUtils:
    def test_format_order_estimated_delivery_time_log_should_return_empty_when_no_estimated_delivery_logs(  # noqa E501
        self, order_factory, product_factory
    ):
        order_no_estimated_delivery_time_log = order_factory(
            estimated_delivery_time=None, estimated_delivery_time_log=[]
        )
        product = product_factory(
            status=ProductStatus.NEW, order=order_no_estimated_delivery_time_log
        )

        estimated_delivery_time_log_html = format_order_estimated_delivery_time_log(
            product
        )
        assert estimated_delivery_time_log_html == '-'

    @freeze_time(datetime.datetime(2020, 4, 16, 11, tzinfo=datetime.UTC))
    def test_format_order_estimated_delivery_time_log_should_return_formatted_when_estimated_delivery_logs(  # noqa E501
        self, order_factory, product_factory
    ):
        second_estimated_delivery_time = timezone.now()
        first_estimated_delivery_time = timezone.now() - datetime.timedelta(days=1)
        order_with_estimated_delivery_time_logs = order_factory(
            estimated_delivery_time=second_estimated_delivery_time,
            estimated_delivery_time_log=[
                second_estimated_delivery_time.isoformat(),
                first_estimated_delivery_time.isoformat(),
            ],
        )
        product = product_factory(
            status=ProductStatus.NEW, order=order_with_estimated_delivery_time_logs
        )

        estimated_delivery_time_log = format_order_estimated_delivery_time_log(product)
        assert estimated_delivery_time_log == '2020-04-16;2020-04-15'


@pytest.mark.django_db
class TestIsAnyBatchComplaint:
    def test_every_batch_is_not_complaint(self, product_batch_factory):
        batches = product_batch_factory.create_batch(
            10,
            batch_type=BatchType.STANDARD,
        )
        assert not is_any_batch_complaint(batches)

    def test_every_batch_is_complaint(self, product_batch_factory):
        batches = product_batch_factory.create_batch(
            10,
            batch_type=BatchType.COMPLAINTS,
        )
        assert is_any_batch_complaint(batches)

    def test_batches_with_complaint_and_other_types(self, product_batch_factory):
        product_batch_factory.create_batch(
            1,
            batch_type=BatchType.COMPLAINTS,
        )
        product_batch_factory.create_batch(
            5,
            batch_type=BatchType.STANDARD,
        )
        product_batch_factory.create_batch(
            2,
            batch_type=BatchType.CUSTOM,
        )
        product_batch_factory.create_batch(
            2,
            batch_type=BatchType.DRAWERS,
        )
        batches = models.ProductBatch.objects.all()
        assert is_any_batch_complaint(batches)
