import datetime

from unittest.mock import patch

from django.contrib.auth.models import User
from django.urls import reverse

import pytest

from rest_framework import status

from producers.api import admin_views
from producers.choices import BatchType
from producers.models import (
    Manufactor,
    ProductBatch,
)
from producers.tests.factories import ManufactorFactory
from producers.tests.utils import ProductsBatchingTestMixin
from user_profile.choices import UserType
from user_profile.tests.factories import UserFactory

DATETIME_NOW = datetime.datetime(2020, 4, 16, 11, tzinfo=datetime.UTC)


@pytest.mark.django_db
class TestBatchingCreateAPIView(ProductsBatchingTestMixin):
    view_class = admin_views.BatchingCreateView
    view_name = 'admin:rest_production_batch_add'
    url = reverse(view_name)

    @pytest.fixture()
    def admin_client(self, api_client):
        api_client.force_authenticate(self.admin_user)
        return api_client

    @pytest.fixture(autouse=True)
    def _save_products(self, product_details_jetty_factory):
        self.products = self._create_products()
        for product in self.products:
            product_details_jetty_factory(product=product)

    @patch('producers.api.admin_views.create_product_batch_task.delay')
    def test_post_should_return_400_when_empty_items(
        self, mock_create_product_batch_task_delay, admin_client
    ):
        response = admin_client.post(
            self.url,
            data={
                'producer_id': self.manufactor.id,
                'items': [],
                'batch_type': 'STANDARD',
            },
            format='json',
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        mock_create_product_batch_task_delay.assert_not_called()

    @patch('producers.api.admin_views.create_product_batch_task.delay')
    def test_post_should_return_200_and_call_create_product_batch_task_with_batch_type_not_set_when_unknown(
        self, mock_create_product_batch_task_delay, admin_client
    ):

        assert not ProductBatch.objects.exists()
        response = admin_client.post(
            self.url,
            data={
                'producer_id': self.manufactor.id,
                'items': [product.id for product in self.products],
                'batch_type': 'UNKNOWN',
            },
            format='json',
        )
        assert response.status_code == status.HTTP_200_OK
        mock_create_product_batch_task_delay.assert_called_with(
            product_ids=[product.id for product in self.products],
            producer_id=self.manufactor.id,
            probably_batch_type=BatchType.NOT_SET,
        )

    @patch('producers.api.admin_views.create_product_batch_task.delay')
    def test_post_should_return_200_and_call_create_product_batch_task_when_batch_type_standard(
        self, mock_create_product_batch_task_delay, admin_client
    ):

        assert not ProductBatch.objects.exists()
        response = admin_client.post(
            self.url,
            data={
                'producer_id': self.manufactor.id,
                'items': [product.id for product in self.products],
                'batch_type': 'STANDARD',
            },
            format='json',
        )
        assert response.status_code == status.HTTP_200_OK
        mock_create_product_batch_task_delay.assert_called_with(
            product_ids=[product.id for product in self.products],
            producer_id=self.manufactor.id,
            probably_batch_type=BatchType.STANDARD,
        )

    @patch('producers.api.admin_views.create_product_batch_task.delay')
    def test_post_should_return_200_and_call_create_product_batch_task_when_batch_type_not_set(
        self, mock_create_product_batch_task_delay, admin_client
    ):
        assert not ProductBatch.objects.exists()
        response = admin_client.post(
            self.url,
            data={
                'producer_id': self.manufactor.id,
                'items': [product.id for product in self.products],
                'batch_type': 'NOT_SET',
            },
            format='json',
        )
        assert response.status_code == status.HTTP_200_OK
        mock_create_product_batch_task_delay.assert_called_with(
            product_ids=[product.id for product in self.products],
            producer_id=self.manufactor.id,
            probably_batch_type=BatchType.NOT_SET,
        )


@pytest.fixture()
def producer() -> User:
    user = UserFactory(profile__user_type=UserType.PRODUCER)
    ManufactorFactory(owner=user, active_production=True)
    return user


@pytest.mark.django_db
class TestStatusBatchPageView:
    def test_logged_in_producer_can_get_status(self, client, producer):
        client.force_login(producer)
        url = reverse('producer_batches')

        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK

    def test_not_logged_in_user_is_redirected_to_login_page(self, client):
        url = reverse('producer_batches')

        response = client.get(url)

        assert response.status_code == status.HTTP_302_FOUND


@pytest.mark.django_db
class TestStatusPageView:
    def test_logged_in_producer_is_redirected_to_producer_batches(
        self, client, producer
    ):
        url = reverse('producer_status')
        client.force_login(producer)

        response = client.get(url)

        assert response.status_code == status.HTTP_302_FOUND
        assert response.url == reverse('producer_batches')

    def test_logged_in_not_producer_is_redirected_to_logout_page(self, client):
        url = reverse('producer_status')
        user = UserFactory()
        client.force_login(user)

        response = client.get(url)

        assert response.status_code == status.HTTP_302_FOUND
        assert response.url == reverse('producer_logout')

    def test_not_logged_in_user_is_redirected_to_login_page(self, client):
        url = reverse('producer_status')

        response = client.get(url)

        assert response.status_code == status.HTTP_302_FOUND
        assert response.url.startswith(reverse('producer_login'))


@pytest.mark.django_db
class TestCncConnectionsForBatchView:
    def test_cnc_connections_for_batch_view_not_authorized(
        self, api_client, user_admin
    ):
        url = reverse('cnc_connections')
        response = api_client.get(url, data={}, format='json')
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_cnc_connections_for_batch_view_authorization_and_response(
        self, mocker, api_client, user_factory, product_batch_factory
    ):
        user_admin = user_factory(is_admin=True)
        batches_list = product_batch_factory.create_batch(
            2, manufactor__name=Manufactor.DREWTUR
        )
        batch_ids = [batch.id for batch in batches_list]
        batch_from_to = f'B{min(batch_ids)}_B{max(batch_ids)}'
        shelf_type = 'Type01'
        mocker.patch(
            'producers.reports.batch_files_getter.'
            'GenerateProducersFiles.get_files_list',
            return_value=[],
        )
        mocker.patch(
            'producers.models.ProductBatch.get_batch_type',
            return_value=shelf_type,
        )
        api_client.force_login(user_admin)
        url = reverse('cnc_connections')
        response = api_client.get(
            url,
            data={'batch_ids': ','.join(map(str, batch_ids))},
            format='json',
        )
        assert response.status_code == status.HTTP_200_OK
        assert response['content-type'] == 'application/zip'
        assert (
            response['Content-Disposition']
            == f'attachment; filename={shelf_type}_{batch_from_to}_cnc.zip'
        )


@pytest.mark.django_db
class TestPackagingFilesForBatchView:
    def test_packaging_files_for_batch_view_not_authorized(
        self, api_client, user_admin
    ):
        url = reverse('packaging_files')
        response = api_client.get(url, data={}, format='json')
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_packaging_files_for_batch_view_authorization_and_response(
        self,
        mocker,
        api_client,
        user_factory,
        product_batch_factory,
    ):
        user_admin = user_factory(is_admin=True)
        batches_list = product_batch_factory.create_batch(
            2, manufactor__name=Manufactor.DREWTUR
        )
        batch_ids = [batch.id for batch in batches_list]
        batch_from_to = f'B{min(batch_ids)}_B{max(batch_ids)}'
        shelf_type = 'Type01'
        mocker.patch(
            'producers.reports.batch_files_getter.'
            'GenerateProducersFiles.get_files_list',
            return_value=[],
        )
        mocker.patch(
            'producers.models.ProductBatch.get_batch_type',
            return_value=shelf_type,
        )
        api_client.force_login(user_admin)
        url = reverse('packaging_files')
        response = api_client.get(
            url,
            data={'batch_ids': ','.join(map(str, batch_ids))},
            format='json',
        )
        assert response.status_code == status.HTTP_200_OK
        assert response['content-type'] == 'application/zip'
        assert (
            response['Content-Disposition']
            == f'attachment; filename={shelf_type}_{batch_from_to}_pakowanie.zip'
        )


@pytest.mark.django_db
class TestProductionFilesForBatchView:
    def test_production_files_for_batch_view_not_authorized(
        self, api_client, user_admin
    ):
        url = reverse('production_files')
        response = api_client.get(url, data={}, format='json')
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_production_files_for_batch_view_authorization_and_response(
        self, mocker, api_client, user_factory, product_batch_factory
    ):
        user_admin = user_factory(is_admin=True)
        batches_list = product_batch_factory.create_batch(
            2, manufactor__name=Manufactor.DREWTUR
        )
        batch_ids = [batch.id for batch in batches_list]
        batch_from_to = f'B{min(batch_ids)}_B{max(batch_ids)}'
        shelf_type = 'Type01'
        mocker.patch(
            'producers.reports.batch_files_getter.'
            'GenerateProducersFiles.get_files_list',
            return_value=[],
        )
        mocker.patch(
            'producers.models.ProductBatch.get_batch_type',
            return_value=shelf_type,
        )

        api_client.force_login(user_admin)
        url = reverse('production_files')
        response = api_client.get(
            url,
            data={'batch_ids': ','.join(map(str, batch_ids))},
            format='json',
        )
        assert response.status_code == status.HTTP_200_OK
        assert response['content-type'] == 'application/zip'
        assert (
            response['Content-Disposition']
            == f'attachment; filename={shelf_type}_{batch_from_to}_pliki.zip'
        )
