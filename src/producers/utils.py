import enum
import math
import os
import random
import string

from datetime import (
    datetime,
    timedelta,
)
from typing import TYPE_CHECKING
from zipfile import (
    ZipFile,
    is_zipfile,
)

from django.conf import settings
from django.utils import translation
from django.utils.deconstruct import deconstructible

from custom.enums import (
    LanguageEnum,
    ShelfType,
)
from custom.utils.emails import send_html_mail
from gallery.enums import ShelfPatternEnum
from producers.choices import (
    BatchType,
    ProductStatus,
    QualityPriorityChoices,
    QualityResultChoices,
)
from production_margins.enums import PricingFactorItemStatus
from production_margins.models import PricingFactorItem

if TYPE_CHECKING:
    from producers.models import Product


LOGISTIC_ZONES = {
    'S0': ['poland'],
    'S1': ['czech', 'lithuania', 'slovakia'],
    'S2': [
        'austria',
        'belgium',
        'denmark',
        'hungary',
        'italy',
        'luxembourg',
        'netherlands',
        'san_marino',
        'vatican',
    ],
    'S3': [
        'bulgaria',
        'croatia',
        'estonia',
        'finland',
        'greece',
        'ireland',
        'latvia',
        'portugal',
        'romania',
        'slovenia',
        'spain',
        'sweden',
        'liechtenstein',
    ],
    'S4': ['norway', 'russia', 'turkey', 'ukraine'],
    'S5': [
        'albania',
        'andorra',
        'armenia',
        'azerbaijan',
        'belarus',
        'bosnia_and_herzegovina',
        'cyprus',
        'montenegro',
        'gibraltar',
        'georgia',
        'canary_islands',
        'iceland',
        'kosovo',
        'macedonia',
        'malta',
        'moldova',
        'monaco',
        'serbia',
        'faroe_islands',
    ],
    'S6': ['brazil', 'canada', 'united_states'],
    'S7': [
        'china',
        'philippines',
        'hong_kong',
        'indonesia',
        'japan',
        'south_korea',
        'malaysia',
        'pakistan',
        'singapore',
        'thailand',
        'taiwan',
        'vietnam',
        'united_arab_emirates',
    ],
    'S8': [
        'australia',
        'egypt',
        'iraq',
        'iran',
        'israel',
        'morocco',
        'mexico',
        'new_zealand',
        'south_africa',
    ],
    'S9': [],  # Pozostałe kraje
    'S_CH': ['switzerland'],
    'S_DE': ['germany'],
    'S_UK': ['united_kingdom'],
    'S_FR': ['france'],
}


@deconstructible
class RandomizedUploadTo(object):
    def __init__(self, original_upload_to=''):
        self.original_upload_to = original_upload_to

    def __call__(self, instance, filename):
        random_string = ''.join(
            random.SystemRandom().choice(string.ascii_uppercase + string.digits)
            for _ in range(6)
        )
        dirname = datetime.now().strftime(str(self.original_upload_to))
        return os.path.join(dirname, random_string, filename)


class MebleplStatusCode(enum.IntEnum):
    IN_PROGRESS = 12
    READY_TO_BE_SHIPPED = 13


def format_isoformat_date_time_to_date(date_time):
    return datetime.fromisoformat(date_time).strftime('%Y-%m-%d')


def format_order_estimated_delivery_time_log(product):
    if not product.order.estimated_delivery_time_log:
        return '-'

    estimated_delivery_dates = (
        format_isoformat_date_time_to_date(delivery_time)
        for delivery_time in product.order.estimated_delivery_time_log
    )
    return ';'.join(estimated_delivery_dates)


def get_product_data_for_production_timeline_export(product, quality_panel=False):
    translation.activate(LanguageEnum.EN)
    product_logistic_order = product.get_logistic_order()

    if quality_panel:
        product_complaints = product.reproduction_complaints.all()
        product_review = product.order.review_set.first()
        return [
            product.cached_shelf_type,
            product.id,
            product.order_id,
            'Yes' if product.quality_control_needed else 'No',
            product.get_cached_physical_product_version_display(),
            ShelfPatternEnum(product.cached_dna_style).label,
            product.manufactor.name if product.manufactor else '-',
            product.get_status_display(),
            product.get_source_priority_display(),
            product.get_delivery_priority_display(),
            product.get_priority_display_with_switched_on_hold(),
            product.order.get_customer_as_string(with_html=False),
            product.order.country,
            product.batch.material_description if product.batch else 'No batch',
            str(
                product.order_item.order_item.get_material_description()
                .get('colour', {})
                .get('name', '')
            ),
            product.cached_depth,
            product.order.order_notes,
            str(product.get_m2()).replace('.', ','),
            product.get_packaging_quantity(),
            product.order.estimated_delivery_time.strftime('%Y-%m-%d')
            if product.order.estimated_delivery_time
            else '-',
            product_logistic_order.carrier if product_logistic_order else '-',
            ', '.join(
                map(
                    str,
                    list(
                        product_complaints.values_list(
                            'assembly_team_intervention', flat=True
                        )
                    ),
                )
            ),
            product.reproduction_complaints.count(),
            ', '.join(
                map(
                    str,
                    list(product.reproduction_products.values_list('id', flat=True)),
                ),
            ),
            (
                'Yes'
                if product and product.order_item and product.order_item.free_return
                else 'No'
            ),
            product_review.score if product_review else 'No review',
            QualityPriorityChoices(product.quality_priority).label
            if product.quality_priority
            and product.quality_priority in QualityPriorityChoices
            else '-',
            product.quality_notes,
            product.quality_date,
            QualityResultChoices(product.quality_result).label
            if product.quality_result and product.quality_result in QualityResultChoices
            else '-',
            get_reported_date(product),
        ]

    return [
        product.cached_shelf_type,
        product.id,
        product.order_id,
        product.batch_id if product.batch else 'No batch',
        product.get_cached_physical_product_version_display(),
        ShelfPatternEnum(product.cached_dna_style).label,
        product.manufactor.name if product.manufactor else '-',
        product.get_status_display(),
        product.get_source_priority_display(),
        product.get_delivery_priority_display(),
        product.get_priority_display_with_switched_on_hold(),
        product.order.get_customer_as_string(with_html=False),
        product.order.country,
        product.batch.material_description if product.batch else 'No batch',
        str(
            product.order_item.order_item.get_material_description()
            .get('colour', {})
            .get('name', '')
        ),
        product.cached_depth,
        product.cached_height,
        product.cached_width,
        product.order.order_notes,
        str(product.get_m2()).replace('.', ','),
        product.get_elements_quantity(),
        product.get_packaging_quantity(),
        get_paid_at_date_for_product(product),
        product.order.estimated_delivery_time.strftime('%Y-%m-%d')
        if product.order.estimated_delivery_time
        else '-',
        product.estimated_production_date.strftime('%Y-%m-%d')
        if product.estimated_production_date
        else '-',
        product.batch.get_batch_type_display() if product.batch else 'No batch',
        product.batch.created_at.strftime('%Y-%m-%d %H:%M')
        if product.batch
        else 'No batch',
        'Yes' if product.has_doors else '-',
        'Yes' if product.has_drawers else '-',
        'Yes' if product.is_extended else '-',
        'Yes' if product.cached_has_top_or_bottom_storage else '-',
        'Yes'
        if getattr(product.order_item.order_item, 'has_top_storage', None)
        else '-',
        'Yes'
        if getattr(product.order_item.order_item, 'has_bottom_storage', None)
        else '-',
        'Yes' if product.has_plinth else '-',
        'Yes' if product.has_lighting else '-',
        'Yes' if product.has_plus_feature else '-',
        'Yes' if product.is_desk else '-',
        format_order_estimated_delivery_time_log(product),
        product.order.promo_text,
        'Yes' if product.order.returning_client else 'No',
        product.get_reproduction_date(),
        product.order_item.price_net,
        product.latest_status_history[0].changed_at.strftime('%Y-%m-%d %H:%M')
        if product.latest_status_history
        else '-',
        product_logistic_order.delivered_date.strftime('%Y-%m-%d')
        if product_logistic_order and product_logistic_order.delivered_date
        else '-',
        f'{product.order.postal_code} {product.order.city}',
        round(product.get_weight_brutto(), 2),
        product.order.owner.profile.language,
        product.order.email,
        get_reported_date(product),
        product.order_item.order_item.furniture_category.value,
    ]


def get_reported_date(product: 'Product') -> str:
    complaint = product.reproduction_complaints.first()
    if complaint and complaint.reported_date:
        return complaint.reported_date.strftime('%Y-%m-%d')
    return '-'


def get_paid_at_date_for_product(product):
    if product.order.is_complaint():
        return product.created_at.strftime('%Y-%m-%d')
    if product.order.paid_at:
        return product.order.paid_at.strftime('%Y-%m-%d')
    return '-'


def generate_production_timeline_with_labels(
    document_request_id, queryset, export_source
):
    from producers.tasks import generate_products_timeline_with_labels  # circular

    product_ids = []
    if export_source in ['productbatch', 'productbatchcomplaint']:
        product_ids = queryset.values_list('batch_items__id', flat=True).distinct()
    if export_source in ['product', 'productcomplaint']:
        product_ids = queryset.values_list('id', flat=True)

    generate_products_timeline_with_labels.delay(document_request_id, list(product_ids))


def get_shelf_type_from_product(product):
    try:
        if (
            product is not None
            and product.order_item is not None
            and product.order_item.order_item is not None
            and product.order_item.order_item.shelf_type == ShelfType.TYPE02
        ):
            return ShelfType.TYPE02.translated_name
        else:
            return ShelfType.TYPE01.translated_name
    except AttributeError:
        return 'Other'


def add_file_to_zip(file, zipfile, directory=''):
    if file and file.storage.exists(file.name):
        filename = os.path.basename(file.name)
        if directory:
            filename = f'{directory}/{filename}'
        file.seek(0)
        zipfile.writestr(filename, file.read())
        file.seek(0)


def add_bytes_to_zip(bytes_obj, filename, zipfile, directory=''):
    if directory:
        filename = os.path.join(directory, filename)
    zipfile.writestr(
        filename,
        bytes_obj,
    )


def unzip_file_to_another_zip(source_zip, target_zip, path_in_target_zip):
    with ZipFile(source_zip, 'r') as source_zip_file:
        for filename in source_zip_file.namelist():
            file = source_zip_file.read(filename)
            target_zip.writestr(
                path_in_target_zip + filename,
                file,
            )


def add_unzipped_file_to_zip(file, target_zip, path_in_target_zip):
    if file and is_zipfile(file):
        unzip_file_to_another_zip(file, target_zip, path_in_target_zip)
    else:
        add_file_to_zip(file, target_zip, path_in_target_zip)


def is_any_batch_complaint(batches):
    return any(batch.batch_type == BatchType.COMPLAINTS for batch in batches)


def get_headers_for_generate_products_timeline_with_labels(quality_panel=False):
    if quality_panel:
        return [
            'Shelf type',
            'Product ID',
            'Order ID',
            'Quality control',
            'Physical product version',
            'DNA',
            'Manufactor',
            'Status',
            'Source priority',
            'Delivery priority',
            'Priority',
            'Customer',
            'Country',
            'Material description',
            'Material color',
            'Depth',
            'Notes',
            'Area and circuit',
            'Numbers of packages',
            'Estimated Production Date',
            'Carrier',
            'Complaint Assembly Service',
            'Number of complaints',
            'Reproduction Product',
            'Free return',
            'Review score',
            'QC Priority',
            'QC Notes',
            'QC date',
            'QC result',
            'Complaint reported date',
        ]
    return [
        'Shelf type',
        'Product ID',
        'Order ID',
        'Batch ID',
        'Physical product version',
        'DNA',
        'Manufactor',
        'Status',
        'Source priority',
        'Delivery priority',
        'Priority',
        'Customer',
        'Country',
        'Material description',
        'Material color',
        'Depth',
        'Height',
        'Width',
        'Notes',
        'Area and circuit',
        'Elements amount',
        'Packs amount',
        'Paid at',
        'Order Estimated Production Date',
        'Product Estimated Production Date',
        'Batch type',
        'Batch created at',
        'Has doors',
        'Has drawers',
        'Is extended',
        'Has storage',
        'Has top storage',
        'Has bottom storage',
        'Has plinth',
        'Has lighting',
        'Has + features',
        'Desk?',
        'Estimated delivery time log',
        'Promo code',
        'Returning client',
        'Reproduction date',
        'Net price',
        'To be shipped date',
        'Delivered date',
        'Postal Code / City',
        'Shelf weight',
        'Owner Language',
        'Owner Email',
        'Complaint reported date',
        'Furniture category',
    ]


def get_display_front_view_link(product):
    try:
        return product.details.front_view.url
    except ValueError:
        return ''


def round_down_if_item(value, code_name, unit):
    if (
        unit not in ['pc', 'szt']
        or code_name.startswith('packaging')
        or code_name.startswith('service')
        or code_name.startswith('semiproduct')
    ):
        return value
    return math.floor(value)


def get_previous_quarter_info():
    today = datetime.now()
    current_month = today.month
    current_year = today.year
    previous_quarter = (current_month - 1) // 3

    if current_month in {1, 2, 3}:
        start_date = datetime(current_year - 1, 10, 1, 0, 0, 0)
        end_date = datetime(current_year - 1, 12, 31, 23, 59, 59)
    else:
        start_date = datetime(current_year, previous_quarter * 3 - 2, 1, 0, 0, 0)
        end_date = datetime(
            current_year, previous_quarter * 3 + 1, 1, 0, 0, 0
        ) - timedelta(seconds=1)

    return previous_quarter, start_date, end_date


def send_quality_release_emails(
    product_status: ProductStatus, product_ids: list[int], batch_id: int
) -> None:
    template = (
        'mail_notify_quality_control_released.html'
        if product_status == ProductStatus.QUALITY_CONTROL
        else 'mail_notify_quality_blocker_released.html'
    )
    email_data = {
        'batch_id': batch_id,
        'product_ids': product_ids,
    }
    for _, email_address in settings.QUALITY_CONTROL_EMAILS:
        send_html_mail(
            template_name=template,
            subject=(
                f'Producer changed {ProductStatus(product_status).label} '
                f'Needed for products '
                f'{", ".join([str(id) for id in product_ids])}'
            ),
            context=email_data,
            to=email_address,
        )


class BaseArchiveCodenameRemoval:
    """
    Base class to remove codenames that have status ARCHIVED in PricingFactorItem
    """

    @staticmethod
    def get_archived_codenames():
        return list(
            PricingFactorItem.objects.filter(
                status=PricingFactorItemStatus.ARCHIVED.value
            ).values_list('codename', flat=True)
        )

    def remove_from_data_frame(self, df):
        raise NotImplementedError()

    @staticmethod
    def is_zero_usage(column):
        return all([usage == 0 for usage in column])
