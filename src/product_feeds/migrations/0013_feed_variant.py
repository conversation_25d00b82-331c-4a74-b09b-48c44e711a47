# Generated by Django 3.1.8 on 2021-04-20 18:01

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


def create_feed_variants(apps, schema_editor):
    Feed = apps.get_model('product_feeds', 'Feed')
    FeedVariant = apps.get_model('product_feeds', 'FeedVariant')
    for feed in Feed.objects.all():
        FeedVariant.objects.create(
            feed=feed,
            currency=feed.currency,
            country=feed.country,
        )


class Migration(migrations.Migration):

    dependencies = [
        ('regions', '0002_add_country_region_vat'),
        ('product_feeds', '0012_django31_jsonfield'),
    ]

    operations = [
        migrations.AddField(
            model_name='feed',
            name='active',
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name='FeedVariant',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'file',
                    models.FileField(editable=False, null=True, upload_to='feeds'),
                ),
                ('file_updated_at', models.DateTimeField(editable=False, null=True)),
                (
                    'country',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='regions.country',
                    ),
                ),
                (
                    'currency',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='regions.currency',
                    ),
                ),
                (
                    'feed',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='variants',
                        to='product_feeds.feed',
                    ),
                ),
            ],
            options={
                'unique_together': {('currency', 'country', 'feed')},
            },
        ),
        migrations.RunPython(
            create_feed_variants,
            migrations.RunPython.noop,
            elidable=False,
        ),
    ]
