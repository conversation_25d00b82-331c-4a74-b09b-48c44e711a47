# Generated by Django 3.1.13 on 2021-08-29 22:58

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('product_feeds', '0018_merge_watty_migration'),
    ]

    operations = [
        migrations.AddField(
            model_name='feedimageconfig',
            name='external_renderer_options',
            field=models.TextField(
                blank=True,
                help_text='(Experimental) Additional setup for external renderer',
                null=True,
            ),
        ),
        migrations.AddField(
            model_name='feedimageconfig',
            name='open_compartments',
            field=models.BooleanField(default=False, help_text='Open doors/drawers'),
        ),
        migrations.AddField(
            model_name='feedimageconfig',
            name='uses_external_renderer',
            field=models.BooleanField(
                default=False,
                help_text='(Experimental) Uses external ShelfView renderer',
            ),
        ),
    ]
