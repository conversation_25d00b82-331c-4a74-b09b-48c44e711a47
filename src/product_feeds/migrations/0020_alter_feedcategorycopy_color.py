# Generated by Django 3.2.8 on 2021-10-22 16:09

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('product_feeds', '0019_add_image_config_for_external_and_wardrobe_renderer'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='feedcategorycopy',
            name='color',
            field=models.IntegerField(
                choices=[
                    (0, 'T1 - White'),
                    (1, 'T1 - Black'),
                    (3, 'T1 - <PERSON>'),
                    (4, 'T1 - Aubergine'),
                    (5, 'T1 - Ash/natural'),
                    (6, 'T1 - Red'),
                    (7, 'T1 - Yellow'),
                    (8, 'T1 - Pink'),
                    (10, 'T2 - White'),
                    (11, 'T2 - <PERSON>/Terracotta'),
                    (12, 'T2 - Midnight blue'),
                    (13, 'T2 - Sand/Bez'),
                    (14, 'T2 - <PERSON>/Z<PERSON><PERSON>'),
                    (16, 'T2 - <PERSON><PERSON>'),
                    (17, 'T2 - <PERSON> <PERSON>'),
                    (18, 'T2 - <PERSON>'),
                    (19, 'T2 - <PERSON>'),
                    (20, 'T1V - <PERSON>'),
                    (21, 'T1V - <PERSON>'),
                    (30, 'T3 - <PERSON>'),
                    (31, 'T3 - <PERSON><PERSON>'),
                    (32, 'T3 - <PERSON><PERSON><PERSON>e'),
                    (33, 'T3 - <PERSON><PERSON> <PERSON>'),
                ]
            ),
        ),
    ]
