from django.conf import settings
from django.db import models

from rest_framework import serializers

from custom.enums import ShelfType
from gallery.models import Jetty
from product_feeds.exceptions import SkippableSerializationError
from product_feeds.models import FeedItemImage


class FeedItemImageSerializer(serializers.ModelSerializer):
    item_id = serializers.IntegerField(source='item')
    config_id = serializers.IntegerField(source='config')

    class Meta(object):
        model = FeedItemImage
        fields = ('item_id', 'config_id')

    def get_unique_together_validators(self):
        """
        Overriding method to disable unique together checks
        """
        return []


class SkippableListSerializer(serializers.ListSerializer):
    """List serializer with option to skip instances."""

    def to_representation(self, data):
        """
        List of object instances -> List of dicts of primitive datatypes.
        """
        # Dealing with nested relationships, data can be a Manager,
        # so, first get a queryset from the Manager if needed
        iterable = data.all() if isinstance(data, models.Manager) else data

        representations = []
        for item in iterable:
            try:
                representations.append(self.child.to_representation(item))
            except SkippableSerializationError:
                self.process_skipped(item)

        return representations

    def process_skipped(self, item):
        """In case you need to take some action for skipped instance override this."""


class FacebookProductFeedItemRequestSerializer(serializers.ModelSerializer):
    availability = serializers.SerializerMethodField()
    condition = serializers.SerializerMethodField()
    currency = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()
    image_url = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    price = serializers.SerializerMethodField()
    url = serializers.SerializerMethodField()
    retailer_id = serializers.IntegerField(source='id')
    method = serializers.SerializerMethodField()
    category = serializers.SerializerMethodField()
    brand = serializers.SerializerMethodField()
    color = serializers.SerializerMethodField()
    size = serializers.SerializerMethodField()
    custom_label_0 = serializers.SerializerMethodField()
    custom_label_1 = serializers.SerializerMethodField()

    class Meta:
        model = Jetty
        list_serializer_class = SkippableListSerializer
        fields = (
            'availability',
            'condition',
            'currency',
            'description',
            'image_url',
            'name',
            'price',
            'url',
            'retailer_id',
            'method',
            'category',
            'brand',
            'color',
            'size',
            'custom_label_0',
            'custom_label_1',
        )

    def get_color(self, instance):
        return (
            ShelfType(instance.shelf_type)
            .colors(instance.material)
            .name.replace('_', ' ')
            .title()
        )

    def get_availability(self, instance):
        return 'in stock'

    def get_condition(self, instance):
        return 'new'

    def get_currency(self, instance):
        currency = self.context.get('currency')
        return currency.code if currency else 'EUR'

    def get_name(self, instance):
        shelf_type = ShelfType(instance.shelf_type).name
        color = self.get_color(instance)
        return f'{shelf_type} {color} - Tylko'

    def get_description(self, instance):
        return 'Designer, custom-made furnitures.'

    def get_price(self, instance):
        region = self.context.get('region')
        currency = self.context.get('currency')
        price = instance.get_regionalized_price(region, currency=currency)
        return round(price * 100)

    def get_url(self, instance):
        region = self.context.get('region')
        return (
            f'{settings.SITE_URL}{instance.get_absolute_url()}'
            + f'?origin=fb_dpa&forced_region={region.name}'
        )

    def get_image_url(self, instance):
        if not instance.preview:
            raise SkippableSerializationError()
        return f'{instance.preview.url}'

    def get_method(self, instance):
        """Method for facebook batch requests."""
        return 'CREATE'

    def get_category(self, instance):
        """Facebook Furniture Shelf category"""
        return '464'

    def get_brand(self, instance):
        return 'Tylko'

    def get_size(self, instance):
        return instance.get_dimensions().formatted_str(
            with_html_space=False,
            include_cm=True,
        )

    def get_custom_label_0(self, instance):
        return instance.furniture_category

    def get_custom_label_1(self, instance):
        return self.get_color(instance)
