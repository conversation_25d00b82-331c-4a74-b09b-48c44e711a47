# Generated by Django 3.2.12 on 2022-07-26 10:10

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('promotions', '0009_promo_labels_grid_fields'),
    ]

    operations = [
        migrations.AddField(
            model_name='promotionpicture',
            name='content_type',
            field=models.ForeignKey(
                blank=True,
                limit_choices_to=models.Q(
                    models.Q(('app_label', 'gallery'), ('model', 'jetty')),
                    models.Q(('app_label', 'gallery'), ('model', 'watty')),
                    _connector='OR',
                ),
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to='contenttypes.contenttype',
                verbose_name='Furniture type',
            ),
        ),
        migrations.AddField(
            model_name='promotionpicture',
            name='object_id',
            field=models.PositiveIntegerField(
                blank=True,
                help_text=(
                    'Furniture should be a preset.'
                    ' Otherwise it will not be visible to everyone.'
                ),
                null=True,
                verbose_name='Furniture ID',
            ),
        ),
    ]
