# Generated by Django 3.2.16 on 2023-04-16 10:45

from django.db import migrations

from custom.enums import LanguageEnum


def set_new_copy_fields(apps, schema_editor):
    VoucherCopyConfig = apps.get_model('promotions', 'VoucherCopyConfig')
    for voucher_copy_config in VoucherCopyConfig.objects.all():
        for lang in [
            LanguageEnum.EN,
            LanguageEnum.DE,
            LanguageEnum.FR,
            LanguageEnum.ES,
            LanguageEnum.NL,
        ]:
            VoucherCopyConfig.objects.create(
                name=voucher_copy_config.name,
                promotion=voucher_copy_config.promotion,
                voucher=voucher_copy_config.voucher,
                language=lang,
                copy_promo_alert_popup_paragraph_1=getattr(
                    voucher_copy_config,
                    f'copy_{lang}_promo_alert_popup_paragraph_1',
                ),
                copy_promo_alert_popup_paragraph_2=getattr(
                    voucher_copy_config,
                    f'copy_{lang}_promo_alert_popup_paragraph_2',
                ),
                copy_promo_alert_popup_paragraph_3=getattr(
                    voucher_copy_config,
                    f'copy_{lang}_promo_alert_popup_paragraph_3',
                ),
                copy_promo_ribbon_left_text=getattr(
                    voucher_copy_config,
                    f'copy_{lang}_promo_ribbon_left_text',
                ),
                copy_promo_ribbon_right_text=getattr(
                    voucher_copy_config,
                    f'copy_{lang}_promo_ribbon_right_text',
                ),
                copy_promo_ribbon_left_red=getattr(
                    voucher_copy_config,
                    f'copy_{lang}_promo_ribbon_left_red',
                ),
                copy_promo_ribbon_right_red=getattr(
                    voucher_copy_config,
                    f'copy_{lang}_promo_ribbon_right_red',
                ),
            )
        voucher_copy_config.delete()


def set_old_copy_fields(apps, schema_editor):
    VoucherCopyConfig = apps.get_model('promotions', 'VoucherCopyConfig')

    distinct_entries = VoucherCopyConfig.objects.distinct(
        'name', 'voucher', 'promotion'
    )
    for voucher_copy_config in distinct_entries:
        for language in [
            LanguageEnum.EN,
            LanguageEnum.DE,
            LanguageEnum.FR,
            LanguageEnum.ES,
            LanguageEnum.NL,
        ]:
            voucher_copy_config_with_lang = VoucherCopyConfig.objects.filter(
                name=voucher_copy_config.name,
                voucher=voucher_copy_config.voucher,
                promotion=voucher_copy_config.promotion,
                language=language,
            ).last()
            if voucher_copy_config_with_lang:
                set_voucher_copy_lang_fields(
                    voucher_copy_config,
                    voucher_copy_config_with_lang,
                    language,
                )
            voucher_copy_config_with_lang.delete()

        voucher_copy_config.save()

    for voucher_copy_config in VoucherCopyConfig.objects.all():
        if voucher_copy_config not in distinct_entries:
            voucher_copy_config.delete()


def set_voucher_copy_lang_fields(
    voucher_copy_config,
    voucher_copy_config_with_lang,
    language,
):
    setattr(
        voucher_copy_config,
        f'copy_{language}_promo_alert_popup_paragraph_1',
        voucher_copy_config_with_lang.copy_promo_alert_popup_paragraph_1,
    )
    setattr(
        voucher_copy_config,
        f'copy_{language}_promo_alert_popup_paragraph_2',
        voucher_copy_config_with_lang.copy_promo_alert_popup_paragraph_2,
    )
    setattr(
        voucher_copy_config,
        f'copy_{language}_promo_alert_popup_paragraph_3',
        voucher_copy_config_with_lang.copy_promo_alert_popup_paragraph_3,
    )
    setattr(
        voucher_copy_config,
        f'copy_{language}_promo_ribbon_left_text',
        voucher_copy_config_with_lang.copy_promo_ribbon_left_text,
    )
    setattr(
        voucher_copy_config,
        f'copy_{language}_promo_ribbon_right_text',
        voucher_copy_config_with_lang.copy_promo_ribbon_right_text,
    )
    setattr(
        voucher_copy_config,
        f'copy_{language}_promo_ribbon_left_red',
        voucher_copy_config_with_lang.copy_promo_ribbon_left_red,
    )
    setattr(
        voucher_copy_config,
        f'copy_{language}_promo_ribbon_right_red',
        voucher_copy_config_with_lang.copy_promo_ribbon_right_red,
    )


class Migration(migrations.Migration):
    dependencies = [
        ('promotions', '0016_migrate_promotion_config_copy_data'),
    ]

    operations = [
        migrations.RunPython(
            set_new_copy_fields,
            set_old_copy_fields,
            elidable=True,
        ),
    ]
