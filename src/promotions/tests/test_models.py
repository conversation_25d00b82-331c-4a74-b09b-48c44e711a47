from datetime import (
    datetime,
    timedelta,
)

from django.core.exceptions import ValidationError

import pytest

NOW = datetime(2025, 6, 11, 10, 0, 0)


@pytest.mark.django_db
class TestPromotion:
    @pytest.mark.parametrize(
        ('start_date', 'end_date'),
        [
            (NOW, NOW - timedelta(days=2)),
            (NOW, NOW - timedelta(hours=2)),
        ],
    )
    def test_promotion_with_end_date_before_start_date_raises_validation_error(
        self, start_date, end_date, promotion_factory
    ):
        with pytest.raises(ValidationError) as excinfo:
            promotion_factory(
                start_date=start_date,
                end_date=end_date,
            )
        assert 'end date cannot be before start date' in str(excinfo.value).lower()
