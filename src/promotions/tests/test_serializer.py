import pytest

from promotions.models import (
    Promotion,
    PromotionConfig,
    PromotionConfigCopy,
    RibbonLine,
)
from promotions.serializers import PromotionImportExportSerializer
from vouchers.models import (
    ItemDiscount,
    Voucher,
    VoucherGroup,
    VoucherRegionEntry,
)


@pytest.mark.django_db
class TestPromotionImportExportSerializer:
    @pytest.fixture
    def full_promotion(
        self,
        promotion_factory,
        voucher_factory,
        voucher_group_factory,
        voucher_region_entry_factory,
        item_discount,
        promotion_config_factory,
        promotion_config_copy_factory,
        ribbon_line_factory,
        region,
    ):
        voucher_group = voucher_group_factory()
        voucher_group.region.add(region)

        voucher = voucher_factory(group=voucher_group)
        voucher_region_entry_factory(voucher=voucher)
        voucher.discounts.add(item_discount)

        promotion = promotion_factory(promo_code=voucher)

        promotion_config = promotion_config_factory(promotion=promotion)
        config_copy = promotion_config_copy_factory(config=promotion_config)
        ribbon_line_factory(config_copy=config_copy)

        return promotion

    @staticmethod
    def _clean_promotion_related_objects():
        Promotion.objects.all().delete()
        PromotionConfig.objects.all().delete()
        Voucher.objects.all().delete()
        VoucherGroup.objects.all().delete()
        VoucherRegionEntry.objects.all().delete()
        ItemDiscount.objects.all().delete()
        PromotionConfigCopy.objects.all().delete()
        RibbonLine.objects.all().delete()

    def test_export_and_import_promotion_creates_all_related_entries(
        self,
        full_promotion,
    ):
        export_data = PromotionImportExportSerializer(full_promotion).data
        self._clean_promotion_related_objects()

        serializer = PromotionImportExportSerializer(data=export_data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        promotion = Promotion.objects.last()
        assert promotion
        assert promotion.promo_code
        assert promotion.promo_code.group
        assert promotion.promo_code.discounts.exists()
        assert promotion.promo_code.region_entries.exists()
        assert promotion.configs.exists()
        assert promotion.configs.first().copies.exists()
        assert promotion.configs.first().copies.first().ribbon_lines.exists()
