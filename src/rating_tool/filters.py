from django.contrib import admin
from django.db.models import Q

from custom.filters import BaseMultipleChoiceListFilter
from rating_tool.models import CAT_ALL


class MultipleChoiceListFilter(BaseMultipleChoiceListFilter):
    title = 'Filters'
    parameter_name = 'filters'
    template = 'admin/filter_category_filters.html'

    def lookups(self, request, model_admin):
        return [
            ('f_drawers', 'Drawers'),
            ('f_doors', 'Doors'),
            ('f_open', 'Open'),
            ('r_small', 'Small'),
            ('r_medium', 'Medium'),
            ('r_large', 'Large'),
        ]

    def queryset(self, request, queryset):
        if request.GET.get(self.parameter_name):
            for filter_descr in request.GET[self.parameter_name].split(','):
                queryset = queryset.filter(filter_descr__icontains=filter_descr)
        return queryset


class MainCategoryFilter(admin.SimpleListFilter):
    title = 'Main Category'
    parameter_name = 'main_category'

    def lookups(self, request, model_admin):
        return [('yes', 'Only main categories')]

    def queryset(self, request, queryset):
        if self.value() == 'yes':
            return queryset.filter(
                filter_descr__in=[
                    'cat_all',
                    'cat_1',
                    'cat_2',
                    'cat_3',
                    'cat_4',
                    'cat_5',
                    'cat_6',
                    'cat_7',
                    'cat_9',
                    'cat_10',
                ]
            )


class NonEmptyBoardFilter(admin.SimpleListFilter):
    title = 'Non empty board filter'
    parameter_name = 'empty_boards'

    def lookups(self, request, model_admin):
        return [('yes', 'Show only empty boards')]

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        if self.value() == 'yes':
            query = Q(order__isnull=True) | Q(order__iexact='{}')
            return queryset.filter(query)
        return queryset


class FilterCategoryFilter(admin.SimpleListFilter):
    title = 'Filter Category'
    parameter_name = 'filter_category'

    def lookups(self, request, model_admin):
        return [
            (CAT_ALL, 'No category'),
            ('cat_1', 'Bookcase'),
            ('cat_2', 'Shoerack'),
            ('cat_3', 'Sideboard'),
            ('cat_4', 'Tvstand'),
            ('cat_5', 'Wallstorage'),
            ('cat_6', 'Chest of drawers'),
            ('cat_7', 'Vinyls'),
            ('cat_9', 'Bedside Table'),
            ('cat_10', 'Desk'),
        ]

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        return queryset.filter(filter_descr__icontains=self.value())


class DefaultJettiesFilter(admin.SimpleListFilter):
    title = 'Furniture features'

    # Parameter for the filter that will be used in the URL query.
    parameter_name = 'empty_furnitures'

    def lookups(self, request, model_admin):
        return (('true', 'Empty'),)

    def queryset(self, request, queryset):
        if self.value() == 'true':
            return queryset.filter(
                Q(jetty__width=0, jetty__height=0, weight=0)
                | Q(watty__width=0, watty__height=0, weight=0)
            )
