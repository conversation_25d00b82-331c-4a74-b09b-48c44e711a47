# Generated by Django 3.1.6 on 2021-02-26 10:30

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('rating_tool', '0004_remove_rating_models'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='jettyboard',
            name='analytics_data',
            field=models.JSO<PERSON>ield(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='jettyboard',
            name='order',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='jettycategory',
            name='analytics_data',
            field=models.<PERSON><PERSON><PERSON>ield(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='jettycategory',
            name='base_shelfs_data',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='jettycategory',
            name='boards_data',
            field=models.<PERSON><PERSON><PERSON>ield(blank=True, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON>ield(
            model_name='jettycategory',
            name='rating_data',
            field=models.<PERSON><PERSON><PERSON><PERSON>(blank=True, null=True),
        ),
    ]
