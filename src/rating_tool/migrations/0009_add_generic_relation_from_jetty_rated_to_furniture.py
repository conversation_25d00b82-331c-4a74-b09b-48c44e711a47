# Generated by Django 3.1.7 on 2021-04-12 14:42

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('rating_tool', '0008_remove_unnecessary_fields_from_jetty_rated'),
    ]

    operations = [
        migrations.AddField(
            model_name='jettyrated',
            name='content_type',
            field=models.ForeignKey(
                limit_choices_to=models.Q(
                    models.Q(('app_label', 'gallery'), ('model', 'jetty')),
                    models.Q(('app_label', 'gallery'), ('model', 'watty')),
                    _connector='OR',
                ),
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='contenttypes.contenttype',
            ),
        ),
        migrations.AddField(
            model_name='jettyrated',
            name='furniture_id',
            field=models.PositiveIntegerField(null=True),
        ),
        migrations.AlterField(
            model_name='jettyrated',
            name='jetty',
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='gallery.Jetty',
            ),
        ),
    ]
