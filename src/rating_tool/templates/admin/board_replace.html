{% extends "admin/base_site.html" %}
{% load i18n l10n %}
{% load admin_urls %}

{% block breadcrumbs %}
    <div class="breadcrumbs">
        <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
        &rsaquo; <a href="{% url 'admin:app_list' app_label=app_label %}">{{ app_label|capfirst|escape }}</a>
        &rsaquo; <a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
        &rsaquo; {{ name }}
    </div>
{% endblock %}

{% block content %}
    <h1>Replace furniture on board {{ board_pk }}</h1>
    {% if order_data %}
        {% for item in order_data %}
            <p>{{ item.id }} {{ item.furniture_type }}</p>
        {% endfor %}
    {% endif %}
    <br>
    <form action="" method="post" enctype="multipart/form-data">
        {% csrf_token %}
        {{ form.as_p }}
        <input type="hidden" name="action" value="{{ admin_action }}" />
        <input type="submit" name="apply" value="Replace" />
        <input type="hidden" name="select_across" value="{{ select_across }}" />
    </form>

{% endblock %}
