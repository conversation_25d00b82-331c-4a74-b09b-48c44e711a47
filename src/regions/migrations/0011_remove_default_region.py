from django.db import migrations


def remove_region_default(apps, schema_editor):
    Region = apps.get_model('regions', 'Region')
    Region.objects.filter(name='_default').delete()


class Migration(migrations.Migration):

    dependencies = [
        ('regions', '0010_remove_region_rate_for_default_region'),
    ]

    operations = [
        migrations.RunPython(
            remove_region_default,
            migrations.RunPython.noop,
            elidable=True,
        ),
    ]
