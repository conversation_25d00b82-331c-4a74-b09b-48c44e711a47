from django.db.models import (
    IntegerChoices,
    TextChoices,
)


class RenderTaskStatuses(IntegerChoices):
    TO_DO = 0
    IN_PROGRESS = 1
    DONE = 2
    FAILED = 3


class RenderTaskTypes(IntegerChoices):
    GRID = 0
    SAVE_FOR_LATER = 1
    CATALOGUE = 2
    FEED = 3


class WebglRenderTaskType(TextChoices):
    FEEDS = 'feeds'
    PREVIEW = 'preview'
    RECALCULATE_GEOMETRY = 'recalculate_geometry'
