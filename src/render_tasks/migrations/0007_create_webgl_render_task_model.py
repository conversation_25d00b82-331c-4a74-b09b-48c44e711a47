# Generated by Django 4.1.9 on 2024-01-12 15:20

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('render_tasks', '0006_rendertask_task_type_add_feed_value'),
    ]

    operations = [
        migrations.CreateModel(
            name='WebglRenderTask',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'status',
                    models.IntegerField(
                        choices=[
                            (0, 'To Do'),
                            (1, 'In Progress'),
                            (2, 'Done'),
                            (3, 'Failed'),
                        ],
                        default=0,
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('consumer', models.CharField(blank=True, default='', max_length=128)),
                ('object_id', models.PositiveIntegerField()),
                (
                    'task_type',
                    models.IntegerField(
                        choices=[(1, 'Catalogue'), (2, 'Gallery'), (3, 'Recalculate')]
                    ),
                ),
                (
                    'content_type',
                    models.ForeignKey(
                        limit_choices_to=models.Q(
                            models.Q(('app_label', 'gallery'), ('model', 'jetty')),
                            models.Q(('app_label', 'gallery'), ('model', 'watty')),
                            _connector='OR',
                        ),
                        on_delete=django.db.models.deletion.CASCADE,
                        to='contenttypes.contenttype',
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
