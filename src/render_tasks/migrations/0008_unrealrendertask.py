# Generated by Django 4.1.9 on 2024-01-26 16:54

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('render_tasks', '0007_create_webgl_render_task_model'),
    ]

    operations = [
        migrations.CreateModel(
            name='UnrealRenderTask',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'status',
                    models.IntegerField(
                        choices=[
                            (0, 'To Do'),
                            (1, 'In Progress'),
                            (2, 'Done'),
                            (3, 'Failed'),
                        ],
                        default=0,
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('consumer', models.CharField(blank=True, default='', max_length=128)),
                ('furniture_json', models.JSONField()),
                (
                    'image',
                    models.ImageField(
                        blank=True, null=True, upload_to='unreal_render_tasks'
                    ),
                ),
                ('image_config', models.JSONField(blank=True, null=True)),
                ('furniture_id', models.PositiveIntegerField()),
                ('initiator_id', models.PositiveIntegerField()),
                (
                    'furniture_content_type',
                    models.ForeignKey(
                        limit_choices_to=models.Q(
                            models.Q(('app_label', 'gallery'), ('model', 'jetty')),
                            models.Q(('app_label', 'gallery'), ('model', 'watty')),
                            _connector='OR',
                        ),
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='unreal_render_tasks_furniture',
                        to='contenttypes.contenttype',
                    ),
                ),
                (
                    'initiator_content_type',
                    models.ForeignKey(
                        limit_choices_to=models.Q(
                            models.Q(
                                ('app_label', 'catalogue'), ('model', 'CatalogueEntry')
                            ),
                            models.Q(('app_label', 'feeds'), ('model', 'FeedImage')),
                            models.Q(
                                ('app_label', 'gallery'), ('model', 'FurnitureImage')
                            ),
                            _connector='OR',
                        ),
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='unreal_render_tasks_initiator',
                        to='contenttypes.contenttype',
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
