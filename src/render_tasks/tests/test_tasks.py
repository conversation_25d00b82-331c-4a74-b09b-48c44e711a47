import re

from unittest.mock import patch

import pytest

from custom.enums import (
    Furniture,
    ShelfType,
)
from gallery.enums import FurnitureImageType
from gallery.models import FurnitureImage
from render_tasks.models import UnrealRenderTask
from render_tasks.tasks import order_unreal_preview


@pytest.mark.django_db
class TestOrderUnrealPreview:
    @pytest.mark.parametrize(
        ('furniture_type', 'factory_name'),
        [
            (Furniture.jetty, 'jetty_factory'),
            (Furniture.watty, 'watty_factory'),
            (Furniture.sotty, 'sotty_factory'),
        ],
    )
    def test_order_unreal_preview(self, furniture_type, factory_name, request):
        furniture_instance = request.getfixturevalue(factory_name)()

        # Call the task
        order_unreal_preview(
            furniture_type=Furniture(furniture_type),
            furniture_id=furniture_instance.id,
        )

        # Verify that a FurnitureImage was created
        furniture_image = FurnitureImage.objects.get(
            furniture_object_id=furniture_instance.id,
            furniture_content_type__model=furniture_type.value,
            type=FurnitureImageType.UNREAL_STUDIO,
        )
        assert furniture_image is not None

        # Verify that an UnrealRenderTask was created
        unreal_task = UnrealRenderTask.objects.get(
            furniture_id=furniture_instance.id,
            furniture_content_type__model=furniture_type.value,
            initiator_id=furniture_image.id,
            initiator_content_type__model='furnitureimage',
        )
        assert unreal_task is not None
        assert 'interior' in unreal_task.image_config

    def test_order_unreal_preview_for_nonexistent_furniture(self):
        """Test that order_unreal_preview handles nonexistent furniture gracefully."""
        # Call the task with a nonexistent furniture ID
        order_unreal_preview(furniture_type=Furniture.sotty, furniture_id=999999)

        # Verify that no FurnitureImage or UnrealRenderTask was created
        assert not FurnitureImage.objects.filter(
            furniture_object_id=999999,
            furniture_content_type__model='sotty',
        ).exists()

        assert not UnrealRenderTask.objects.filter(
            furniture_id=999999,
            furniture_content_type__model='sotty',
        ).exists()

    def test_order_unreal_preview_for_sample_box(self):
        """Test that order_unreal_preview raises ValueError for sample_box."""
        with pytest.raises(
            ValueError, match=re.escape('Are you nuts? No unreal images for samples.')
        ):
            order_unreal_preview(furniture_type=Furniture.sample_box, furniture_id=1)

    def test_order_unreal_preview_for_special_shelf_types(self, watty_factory):
        """Test that order_unreal_preview calls configurator preview for special shelf types."""
        # Create a Watty with a special shelf type
        watty = watty_factory(shelf_type=ShelfType.TYPE23)

        # Mock the functions to avoid duplicate calls
        with patch(
            'render_tasks.tasks.generate_unreal_studio_for_preview'
        ) as mock_generate_studio, patch(
            'render_tasks.tasks.generate_configurator_preview_image'
        ) as mock_generate_configurator:
            # Reset any previous calls
            mock_generate_studio.reset_mock()
            mock_generate_configurator.reset_mock()

            # Call the task
            order_unreal_preview(furniture_type=Furniture.watty, furniture_id=watty.id)

            # Verify that both functions were called
            assert mock_generate_studio.call_count == 1
            assert mock_generate_configurator.call_count == 1
            mock_generate_studio.assert_called_with(item=watty)
            mock_generate_configurator.assert_called_with(item=watty)

    def test_order_unreal_preview_for_regular_shelf_types(self, watty_factory):
        """Test that order_unreal_preview doesn't call configurator preview for regular shelf types."""
        # Create a Watty with a regular shelf type
        watty = watty_factory(shelf_type=ShelfType.TYPE03)

        # Mock the functions to avoid duplicate calls
        with patch(
            'render_tasks.tasks.generate_unreal_studio_for_preview'
        ) as mock_generate_studio, patch(
            'render_tasks.tasks.generate_configurator_preview_image'
        ) as mock_generate_configurator:
            # Reset any previous calls
            mock_generate_studio.reset_mock()
            mock_generate_configurator.reset_mock()

            # Call the task
            order_unreal_preview(furniture_type=Furniture.watty, furniture_id=watty.id)

            # Verify that only generate_unreal_studio_for_preview was called
            assert mock_generate_studio.call_count == 1
            assert mock_generate_configurator.call_count == 0
            mock_generate_studio.assert_called_with(item=watty)
            mock_generate_configurator.assert_not_called()
