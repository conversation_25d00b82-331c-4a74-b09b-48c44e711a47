from django.urls import path

from dj_rest_auth.views import (
    LogoutView,
    PasswordChangeView,
)

from rest_auth.views import (
    LoginView,
    PasswordResetConfirmView,
    RegisterView,
)
from user_profile.views import ForgottenPasswordInit

urlpatterns = [
    path('logout/', LogoutView.as_view(), name='logout'),
    path('login/', LoginView.as_view(), name='login'),
    path('password/change/', PasswordChangeView.as_view(), name='password-change'),
    path('register/', RegisterView.as_view(), name='register'),
    path(
        'password/reset/',
        ForgottenPasswordInit.as_view(),
        name='password-reset-init',
    ),
    path(
        'password/reset/confirm/',
        PasswordResetConfirmView.as_view(),
        name='password-reset-confirm',
    ),
]
