# Generated by Django 3.1.12 on 2021-08-03 09:31

from django.db import (
    migrations,
    models,
)

import reviews.enums


class Migration(migrations.Migration):

    dependencies = [
        ('reviews', '0010_add_type_03_to_shelf_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='review',
            name='categories',
            field=models.IntegerField(
                choices=[
                    (0, 'CATEGORY NONE'),
                    (1, 'CATEGORY SHOERACK'),
                    (2, 'CATEGORY TVSHELF'),
                    (3, 'CATEGORY SIDEBOARD'),
                    (4, 'CATEGORY BOOKCASE'),
                    (5, 'CATEGORY WALLSTORAGE'),
                    (6, 'CATEGORY CHEST OF DRAWERS'),
                    (7, 'CATEGORY WARDROBE'),
                ],
                default=reviews.enums.ReviewCategoryEnum['CATEGORY_NONE'],
            ),
        ),
    ]
