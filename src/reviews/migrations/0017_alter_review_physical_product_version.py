# Generated by Django 3.2.9 on 2022-02-23 09:39

from django.db import (
    migrations,
    models,
)

import custom.enums


class Migration(migrations.Migration):

    dependencies = [
        ('reviews', '0016_alter_review_physical_product_version'),
    ]

    operations = [
        migrations.AlterField(
            model_name='review',
            name='physical_product_version',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, 'TREX'),
                    (2, 'RAPTOR'),
                    (3, 'DIPLO'),
                    (4, 'PTERO'),
                    (5, 'BAMBI'),
                ],
                default=custom.enums.PhysicalProductVersion['TREX'],
                help_text=(
                    'TREX - old shelves, '
                    + 'RAPTOR - 2020 Sideboard+ T02, '
                    + 'DIPLO - early 2021, '
                    + 'PTERO - drawers 3.0, '
                    + 'BAMBI - chipboard supports T01'
                ),
            ),
        ),
    ]
