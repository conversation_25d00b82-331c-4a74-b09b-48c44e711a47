# Generated by Django 3.2.12 on 2022-07-26 12:10

from django.db import (
    migrations,
    models,
)

import reviews.enums


class Migration(migrations.Migration):

    dependencies = [
        ('reviews', '0023_alter_review_physical_product_version'),
    ]

    operations = [
        migrations.AlterField(
            model_name='review',
            name='categories',
            field=models.IntegerField(
                choices=[
                    (0, 'CATEGORY NONE'),
                    (1, 'CATEGORY SHOERACK'),
                    (2, 'CATEGORY TVSHELF'),
                    (3, 'CATEGORY SIDEBOARD'),
                    (4, 'CATEGORY BOOKCASE'),
                    (5, 'CATEGORY WALLSTORAGE'),
                    (6, 'CATEGORY CHEST OF DRAWERS'),
                    (7, 'CATEGORY VINYL STORAGE'),
                    (8, 'CATEGORY WARDROBE'),
                    (9, 'CATEGORY BEDSIDE TABLE'),
                    (10, 'CATEGORY DESK'),
                ],
                default=reviews.enums.ReviewCategoryEnum['CATEGORY_NONE'],
            ),
        ),
    ]
