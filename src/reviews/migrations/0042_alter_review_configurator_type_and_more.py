# Generated by Django 4.1.13 on 2024-12-02 14:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('reviews', '0041_add_norwegian_language'),
    ]

    operations = [
        migrations.AlterField(
            model_name='review',
            name='configurator_type',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, 'ROW'), (2, 'COLUMN'), (3, 'MIXED_ROW_COLUMN'), (4, 'SOFA')], help_text=('MIXED - Wardrobe, COLUMN - column configurator (i.e. for S+), ROW - everything else,',), null=True),
        ),
        migrations.AlterField(
            model_name='review',
            name='shelf_type',
            field=models.IntegerField(blank=True, choices=[(0, 'TYPE01'), (1, 'TYPE02'), (2, 'VENEER TYPE01'), (3, 'TYPE03'), (4, 'TYPE13'), (5, 'VENEER TYPE13'), (6, 'TYPE23'), (7, 'TYPE24'), (8, 'TYPE25'), (10, 'SOFA TYPE01')], null=True),
        ),
    ]
