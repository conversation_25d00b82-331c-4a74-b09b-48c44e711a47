# Generated by Django 4.1.13 on 2025-04-09 10:58

from django.db import (
    migrations,
    models,
)

import reviews.enums


class Migration(migrations.Migration):

    dependencies = [
        ('reviews', '0043_alter_review_categories'),
    ]

    operations = [
        migrations.AlterField(
            model_name='review',
            name='categories',
            field=models.IntegerField(
                choices=[
                    (0, 'CATEGORY NONE'),
                    (1, 'CATEGORY SHOERACK'),
                    (2, 'CATEGORY TVSHELF'),
                    (3, 'CATEGORY SIDEBOARD'),
                    (4, 'CATEGORY BOOKCASE'),
                    (5, 'CATEGORY WALLSTORAGE'),
                    (6, 'CATEGORY CHEST OF DRAWERS'),
                    (7, 'CATEGORY VINYL STORAGE'),
                    (8, 'CATEGORY WARDROBE'),
                    (9, 'CATEGORY BEDSIDE TABLE'),
                    (10, 'CATEGORY DESK'),
                    (11, 'CATEGORY SOFA'),
                    (12, 'CATEGORY DRESSING TABLE'),
                ],
                default=reviews.enums.ReviewCategoryEnum['CATEGORY_NONE'],
            ),
        ),
    ]
