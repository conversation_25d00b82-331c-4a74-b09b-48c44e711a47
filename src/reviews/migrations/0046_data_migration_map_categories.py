# Generated by Django 4.2.23 on 2025-07-25 11:30

from django.db import migrations

from reviews.enums import REVIEW_CATEGORY_TO_FURNITURE_CATEGORY


def map_categories(app, schema_editor):
    Review = app.get_model('reviews', 'Review')
    for review in Review.objects.all():
        try:
            furniture_category = REVIEW_CATEGORY_TO_FURNITURE_CATEGORY[
                review.categories
            ]
        except KeyError:
            furniture_category = None
        review.category = furniture_category
        review.save(update_fields=['category'])


class Migration(migrations.Migration):

    dependencies = [
        ('reviews', '0045_review_category'),
    ]

    operations = [
        migrations.RunPython(
            map_categories,
            migrations.RunPython.noop,
            elidable=True,
        ),
    ]
