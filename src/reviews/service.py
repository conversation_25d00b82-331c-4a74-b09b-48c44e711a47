import logging

from django.conf import settings

import requests

from reviews.models import Review

logger = logging.getLogger('cstm')


class ReviewSlackService:
    def __init__(self, item: Review):
        self.item: Review = item

    def send_slack_notification(self):
        try:
            slack_notification = self.get_slack_notification_body()
            requests.post(settings.SLACK_CS_WEBHOOK_URL, json=slack_notification)
        except Exception as e:
            logger.exception(
                'Unable to send slack review notification due to error: %s', e
            )

    def get_slack_notification_body(self) -> dict:
        review_url = f'https://tylko.com/admin/reviews/review/{self.item.id}/change'
        review_tool = f'https://tylko.com/admin/review_tool/?id={self.item.id}#/'
        star_score = int(self.item.score) * ':star:'
        attachments = [
            {'pretext': self.item.title, 'text': self.item.description},
            {'title': 'Review url', 'title_link': review_url},
            {'title': 'Review tool', 'title_link': review_tool},
        ]
        return {'text': star_score, 'attachments': attachments}
