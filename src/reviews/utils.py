from django.db.models import Q

from gallery.enums import SellableItemContentTypes
from orders.enums import OrderStatus
from orders.models import Order
from reviews.models import Review


def match_review_with_order(review: Review) -> None:
    """Matches the review with an order using the email given in the review form"""
    order = (
        Order.objects.filter(
            Q(email=review.email) | Q(owner__email=review.email),
            status=OrderStatus.DELIVERED,
        )
        .prefetch_related('items')
        .first()
    )

    if not order:
        return

    items = order.items.filter(
        content_type__model__in=SellableItemContentTypes.get_furniture_choices()
    )
    if not items.exists():
        return

    matched_item = None
    for item in items:
        if review.category == item.order_item.furniture_category:
            matched_item = item
            break

    if not matched_item:
        matched_item = items.first()

    review.order = order
    fill_review_data_from_order_item(review, matched_item)
    review.save()


def fill_review_data_from_order_item(review, item):
    """Fills the review with data from the order item previously matched to that review.
    Args:
        review: Review object
        item: OrderItem object
    Returns:
        None
    """
    review.furniture_type = item.order_item.furniture_type
    review.shelf_type = item.order_item.shelf_type
    review.physical_product_version = item.order_item.physical_product_version
