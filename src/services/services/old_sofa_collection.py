from decimal import Decimal

from gallery.constants import THREE_MODULES_OR_MORE
from regions.services.limitations import LimitationService
from services.constants import OLD_SOFA_COLLECTION_PRICE_MAPPER
from services.enums import AdditionalServiceKind
from services.errors import OldSofaCollectionValidationError
from services.models import AdditionalService
from services.services.base import BaseAdditionalService


class OldSofaCollectionService(BaseAdditionalService):
    """Dedicated to adding and removing additional service called OldSofaCollection.
    Checks all conditions, dictates price.
    """

    service_kind = AdditionalServiceKind.OLD_SOFA_COLLECTION
    error_class = OldSofaCollectionValidationError

    def calculate_price(self) -> Decimal:
        # Service price depends on newly ordered sofa size
        return OLD_SOFA_COLLECTION_PRICE_MAPPER.get(
            self.cart.get_all_sofas_modules_number(),
            OLD_SOFA_COLLECTION_PRICE_MAPPER[THREE_MODULES_OR_MORE],
        )

    def _check_conditions_add(self) -> None:
        limitation_service = LimitationService(region=self.cart.region)
        if not limitation_service.is_old_sofa_collection_available:
            raise self.error_class(
                'Old sofa collection is not available in this region'
            )
        if not self.cart.has_legit_sotty:
            raise self.error_class('Cart does not have legit sofa')
        if self.cart.has_old_sofa_collection:
            raise self.error_class('Cart already has old sofa collection')

    def _get_service_from_cart(self) -> AdditionalService:
        return self.cart.get_old_sofa_collection_service()
