from decimal import Decimal

from custom.enums import Furniture
from gallery.constants import WHITE_GLOVES_DELIVERY_PRICE_MAP
from regions.services.limitations import LimitationService
from services.enums import AdditionalServiceKind
from services.errors import WhiteGlovesDeliveryValidationError
from services.models import AdditionalService
from services.services.base import BaseAdditionalService


class WhiteGlovesDeliveryService(BaseAdditionalService):
    """
    Dedicated to adding and removing additional service called WhiteGlovesDelivery.
    Checks all conditions, dictates price.
    Currently, it only works per entire cart/order, not per single item
    """

    service_kind = AdditionalServiceKind.WHITE_GLOVES_DELIVERY
    error_class = WhiteGlovesDeliveryValidationError

    def calculate_price(self) -> Decimal:
        return (
            WHITE_GLOVES_DELIVERY_PRICE_MAP[Furniture.sotty]
            * self.cart.get_total_legit_sotty_items()
        )

    def _check_conditions_add(self) -> None:
        limitation_service = LimitationService(region=self.cart.region)
        if not limitation_service.is_white_gloves_delivery_available:
            raise WhiteGlovesDeliveryValidationError(
                'White Gloves Delivery is not available in this region'
            )
        if not self.cart.has_legit_sotty:
            raise WhiteGlovesDeliveryValidationError(
                'Cart does not have legit sofa to deliver'
            )
        if self.cart.has_white_gloves_delivery:
            raise WhiteGlovesDeliveryValidationError(
                'Cart already has white gloves delivery'
            )

    def _get_service_from_cart(self) -> AdditionalService:
        return self.cart.get_white_gloves_delivery_service()
