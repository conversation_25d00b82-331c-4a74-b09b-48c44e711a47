from django.contrib import admin
from django.contrib.admin.options import StackedInline

from shortener.models import (
    RedirectedUserInfo,
    ShortUrl,
)


class RedirectedUserInfoInline(StackedInline):
    readonly_fields = (
        'short_url',
        'actual_url',
        'time',
        'ip',
        'referer',
        'language',
        'user_agent',
        'user',
    )
    fields = (
        'short_url',
        'actual_url',
        'time',
        'ip',
        'referer',
        'language',
        'user_agent',
        'user',
    )
    model = RedirectedUserInfo
    extra = 0
    can_delete = False
    raw_id_fields = ('short_url', 'user')


class ShortUrlAdmin(admin.ModelAdmin):
    list_display = (
        'name',
        'url',
        'redirect_type',
    )
    list_display_links = ('name',)
    list_filter = ('redirect_type',)
    search_fields = ('name', 'description', 'url')
    ordering = ('-id',)


class RedirectedUserInfoAdmin(admin.ModelAdmin):
    date_hierarchy = 'time'
    list_filter = (
        'short_url',
        'language',
        'installed_app',
    )
    list_display = (
        'id',
        'actual_url',
        'time',
        'ip',
        'referer',
        'language',
        'user_agent',
    )
    raw_id_fields = ('short_url', 'user')

    def has_add_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


admin.site.register(ShortUrl, ShortUrlAdmin)
admin.site.register(RedirectedUserInfo, RedirectedUserInfoAdmin)
