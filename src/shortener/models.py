import logging

from django.conf import settings
from django.db import models

from shortener.choices import RedirectChoices

logger = logging.getLogger('cstm')


class ShortUrl(models.Model):
    name = models.CharField(max_length=64, db_index=True, unique=True)
    description = models.CharField(max_length=128)
    url = models.CharField(max_length=1024)
    redirect_type = models.PositiveSmallIntegerField(choices=RedirectChoices.choices)
    url_mobile = models.CharField(max_length=1024, blank=True, null=True)
    url_android = models.CharField(max_length=1024, blank=True, null=True)
    url_ios = models.CharField(max_length=1024, blank=True, null=True)

    def __str__(self):
        return 'ShortUrl[name={} url={} redirect={}]'.format(
            self.name,
            self.url,
            self.redirect_type,
        )


class RedirectedUserInfo(models.Model):
    short_url = models.ForeignKey(
        ShortUrl,
        on_delete=models.CASCADE,
    )
    actual_url = models.CharField(max_length=1024)
    param_network = models.CharField(max_length=512, blank=True, null=True)
    param_campaign = models.CharField(max_length=512, blank=True, null=True)
    param_adgroup = models.CharField(max_length=512, blank=True, null=True)
    param_term = models.CharField(max_length=512, blank=True, null=True)
    time = models.DateTimeField()
    ip = models.CharField(max_length=45)
    referer = models.CharField(max_length=1024, blank=True, null=True)
    language = models.CharField(max_length=4, blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    installed_app = models.BooleanField(default=False, db_index=True)

    def __str__(self):
        return 'RedirectedUserInfo[url={} time={} ip={} short_url={}]'.format(
            self.actual_url,
            self.time,
            self.ip,
            self.short_url,
        )
