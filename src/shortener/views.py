import logging
import urllib.error
import urllib.parse
import urllib.request

from django.http.response import (
    HttpResponsePermanentRedirect,
    HttpResponseRedirect,
)
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import get_language_from_request
from django.views.generic.base import View

from custom.metrics import metrics_client
from custom.utils.strings import sanitize_incoming_string
from shortener.choices import RedirectChoices
from shortener.models import (
    RedirectedUserInfo,
    ShortUrl,
)

logger = logging.getLogger('cstm')


class ShortUrlRedirectView(View):
    def get(self, request, *args, **kwargs):
        url_name = self.kwargs.get('url_name')
        if not url_name:
            return HttpResponsePermanentRedirect(reverse('front-homepage'))

        try:
            short_url = ShortUrl.objects.get(name=url_name)
        except (ShortUrl.DoesNotExist, ShortUrl.MultipleObjectsReturned):
            metrics_client().increment(
                'backend.catchall', 1, tags=['what:404', 'where:ShortUrlRedirectView']
            )
            return HttpResponsePermanentRedirect(reverse('front-homepage'))

        redirect_url = short_url.url

        if request.user_agent.is_mobile or request.user_agent.is_tablet:
            if (
                request.user_agent.os.family.lower() == 'android'
                and short_url.url_android
            ):
                redirect_url = short_url.url_android
            elif request.user_agent.os.family.lower() == 'ios' and short_url.url_ios:
                redirect_url = short_url.url_ios
            elif short_url.url_mobile:
                redirect_url = short_url.url_mobile

        url_query = urllib.parse.urlsplit(request.get_full_path()).query
        redirect_url_split = urllib.parse.urlsplit(redirect_url)
        redirect_url_query = redirect_url_split.query
        if len(redirect_url_query) > 0:
            given_params = {
                qk: qv[0] for qk, qv in list(urllib.parse.parse_qs(url_query).items())
            }
            redirect_params = {
                qk: qv[0]
                for qk, qv in list(urllib.parse.parse_qs(redirect_url_query).items())
            }
            for param, param_value in list(given_params.items()):
                if param in redirect_params:
                    redirect_params[param] = param_value.encode('utf-8')
            redirect_url = '{}://{}{}?{}'.format(
                redirect_url_split.scheme,
                redirect_url_split.netloc,
                redirect_url_split.path,
                urllib.parse.urlencode(redirect_params),
            )

        info_fields = {
            'short_url': short_url,
            'actual_url': redirect_url,
            'time': timezone.now(),
            'ip': request.META.get(
                'HTTP_X_REAL_IP', request.META.get('REMOTE_ADDR', None)
            ),
            'referer': sanitize_incoming_string(request.META.get('HTTP_REFERER', None)),
            'language': get_language_from_request(request),
            'user_agent': sanitize_incoming_string(request.user_agent.ua_string),
            'user': request.user if request.user.is_authenticated else None,
        }
        info_fields['referer'] = (
            info_fields['referer'][:1023]
            if info_fields['referer'] is not None
            else info_fields['referer']
        )
        for param in [
            f.name
            for f in RedirectedUserInfo._meta.get_fields()
            if f.name.startswith('param_')
        ]:
            info_fields[param] = sanitize_incoming_string(
                request.GET.get('_'.join(param.split('_')[1:]), None)
            )

        try:
            RedirectedUserInfo.objects.create(**info_fields)
        except Exception:
            logger.exception(
                'Error while saving RedirectedUserInfo short_url=%s info_fields=%s',
                url_name,
                info_fields,
            )

        metrics_client().increment(
            'backend.shortener.hit',
            1,
            tags=['name:{}'.format(short_url.name)],
        )

        if short_url.redirect_type == RedirectChoices.PERMANENT:
            return HttpResponsePermanentRedirect(redirect_url)
        else:
            return HttpResponseRedirect(redirect_url)
