# Generated by Django 4.1.13 on 2025-04-23 15:48

import django.contrib.postgres.fields
import django.core.validators
import django.db.models.deletion

from django.db import (
    migrations,
    models,
)

import showrooms.enums


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('regions', '0015_remove_region_is_fast_track_available_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ShowRoom',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                (
                    'latitude',
                    models.DecimalField(
                        blank=True,
                        decimal_places=6,
                        help_text='Latitude in decimal degrees. Example: 37.774929',
                        max_digits=9,
                        null=True,
                    ),
                ),
                (
                    'longitude',
                    models.DecimalField(
                        blank=True,
                        decimal_places=6,
                        help_text='Longitude in decimal degrees. Example: -122.419416',
                        max_digits=9,
                        null=True,
                    ),
                ),
                ('postal_code', models.CharField(max_length=20)),
                (
                    'city',
                    models.CharField(
                        blank=True, max_length=256, null=True, verbose_name='city'
                    ),
                ),
                ('street_address', models.CharField(max_length=255)),
                (
                    'phone',
                    models.CharField(
                        blank=True,
                        max_length=30,
                        null=True,
                        verbose_name='phone number',
                    ),
                ),
                (
                    'phone_prefix',
                    models.CharField(
                        blank=True, max_length=4, null=True, verbose_name='phone prefix'
                    ),
                ),
                (
                    'email',
                    models.CharField(
                        blank=True, max_length=256, null=True, verbose_name='email'
                    ),
                ),
                ('name', models.CharField(max_length=255)),
                ('link', models.URLField(max_length=2048, null=True)),
                (
                    'display_image',
                    models.ImageField(
                        upload_to='showrooms/images/',
                        validators=[
                            django.core.validators.FileExtensionValidator(
                                allowed_extensions=['jpg', 'jpeg', 'png', 'webp']
                            )
                        ],
                    ),
                ),
                ('mon_open_time', models.TimeField(blank=True, null=True)),
                ('mon_close_time', models.TimeField(blank=True, null=True)),
                ('tue_open_time', models.TimeField(blank=True, null=True)),
                ('tue_close_time', models.TimeField(blank=True, null=True)),
                ('wed_open_time', models.TimeField(blank=True, null=True)),
                ('wed_close_time', models.TimeField(blank=True, null=True)),
                ('thu_open_time', models.TimeField(blank=True, null=True)),
                ('thu_close_time', models.TimeField(blank=True, null=True)),
                ('fri_open_time', models.TimeField(blank=True, null=True)),
                ('fri_close_time', models.TimeField(blank=True, null=True)),
                ('sat_open_time', models.TimeField(blank=True, null=True)),
                ('sat_close_time', models.TimeField(blank=True, null=True)),
                ('sun_open_time', models.TimeField(blank=True, null=True)),
                ('sun_close_time', models.TimeField(blank=True, null=True)),
                (
                    'showroom_items',
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(
                            choices=[
                                ('edge', 'Edge'),
                                ('tone', 'Tone'),
                                ('original_classic', 'Original Classic'),
                                ('original_modern', 'Original Modern'),
                                ('smooth_sofa', 'Smooth Sofa'),
                            ],
                            max_length=255,
                        ),
                        default=showrooms.enums.get_default_show_room_items,
                        size=None,
                    ),
                ),
                (
                    'region',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to='regions.region'
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
