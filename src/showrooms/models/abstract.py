from django.db import models


class GeoLocationAbstract(models.Model):
    latitude = models.DecimalField(
        max_digits=9,
        decimal_places=6,
        blank=True,
        null=True,
        help_text='Latitude in decimal degrees. Example: 37.774929',
    )
    longitude = models.DecimalField(
        max_digits=9,
        decimal_places=6,
        blank=True,
        null=True,
        help_text='Longitude in decimal degrees. Example: -122.419416',
    )

    class Meta:
        abstract = True


class AddressAbstract(models.Model):
    postal_code = models.CharField(max_length=20)
    city = models.CharField('city', max_length=256, null=True, blank=True)
    street_address = models.CharField(max_length=255)

    phone = models.CharField('phone number', max_length=30, null=True, blank=True)
    phone_prefix = models.CharField('phone prefix', max_length=4, null=True, blank=True)
    email = models.CharField('email', max_length=256, null=True, blank=True)

    class Meta:
        abstract = True
