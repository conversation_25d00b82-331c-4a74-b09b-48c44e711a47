from django.contrib import admin
from django.utils.html import format_html

from gallery.admin import item_preview
from skus.models import (
    Sku,
    SkuCategory,
    SkuImage,
    SkuVariant,
)


class SkuVariantInline(admin.TabularInline):
    model = SkuVariant
    extra = 0
    fields = ('name', 'translation_key', 'price', 'weight')


class SkuImageInline(admin.TabularInline):
    model = SkuImage
    extra = 1
    fields = ('image_preview', 'image', 'sort_order')
    readonly_fields = ('image_preview',)

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width: 100px; max-height: 100px;" />',
                obj.image.url,
            )
        return '-'

    image_preview.short_description = 'Preview'


class SkuCategoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'created_at', 'updated_at')
    search_fields = ('name',)
    ordering = ('name',)


class SkuAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'sku_category', 'created_at', 'updated_at')
    list_filter = ('sku_category',)
    search_fields = ('name',)
    autocomplete_fields = ('sku_category',)
    ordering = ('name',)
    inlines = [SkuVariantInline]


class SkuVariantAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        item_preview,
        'sku',
        'external_id',
        'name',
        'price',
        'weight',
        'created_at',
        'updated_at',
    )
    list_filter = ('sku',)
    search_fields = ('external_id', 'name')
    autocomplete_fields = ('sku',)
    ordering = ('sku', 'name')
    readonly_fields = ('external_id',)
    inlines = [SkuImageInline]


admin.site.register(SkuVariant, SkuVariantAdmin)
admin.site.register(Sku, SkuAdmin)
admin.site.register(SkuCategory, SkuCategoryAdmin)
