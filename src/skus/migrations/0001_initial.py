# Generated by Django 4.2.23 on 2025-07-22 14:57

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)

import skus.models.abstracts


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='Sku',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                ('name', models.CharField(max_length=250)),
                (
                    'status',
                    models.CharField(
                        choices=[('draft', 'Draft'), ('active', 'Active')],
                        default='draft',
                        max_length=36,
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated_at',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SkuCategory',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                ('name', models.CharField(max_length=250)),
            ],
            options={
                'get_latest_by': 'updated_at',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SkuVariant',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                (
                    'custom_order',
                    models.BooleanField(
                        default=False, help_text='Item has been customized manually'
                    ),
                ),
                ('description', models.TextField(blank=True, null=True)),
                ('deleted', models.BooleanField(db_index=True, default=False)),
                (
                    'created_platform',
                    models.IntegerField(
                        choices=[
                            (0, 'web desktop'),
                            (1, 'web mobile'),
                            (2, 'iphone'),
                            (3, 'ipad'),
                            (4, 'android'),
                            (5, 'android tablet'),
                            (8, 'new ios app'),
                            (6, 'api'),
                            (15, 'unknown source'),
                            (16, 'internal order/complaint'),
                            (99, 'new row configurator'),
                        ],
                        default=15,
                    ),
                ),
                (
                    'external_id',
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ('name', models.CharField(max_length=255)),
                ('translation_key', models.CharField(max_length=255)),
                ('weight', models.FloatField(blank=True, null=True)),
                ('price', models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    'sku',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='variants',
                        to='skus.sku',
                    ),
                ),
            ],
            bases=(
                models.Model,
                skus.models.abstracts.SkuCartAbstract,
                skus.models.abstracts.SkuCheckoutAbstract,
            ),
        ),
        migrations.CreateModel(
            name='SkuImage',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('image', models.ImageField(upload_to='sku_products')),
                ('sort_order', models.IntegerField(null=True)),
                (
                    'sku_variant',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='images',
                        to='skus.skuvariant',
                    ),
                ),
            ],
            options={
                'ordering': ['sort_order'],
            },
        ),
        migrations.AddField(
            model_name='sku',
            name='sku_category',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='products',
                to='skus.skucategory',
            ),
        ),
        migrations.AddConstraint(
            model_name='skuvariant',
            constraint=models.UniqueConstraint(
                condition=models.Q(('external_id__isnull', False)),
                fields=('external_id',),
                name='unique_external_id',
            ),
        ),
        migrations.AddIndex(
            model_name='skuimage',
            index=models.Index(fields=['sort_order'], name='sort_order_idx'),
        ),
    ]
