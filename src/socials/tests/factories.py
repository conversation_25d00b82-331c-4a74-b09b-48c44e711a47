import factory

from allauth.socialaccount.models import SocialApp
from factory import fuzzy


class SocialAppFactory(factory.django.DjangoModelFactory):
    name = factory.Sequence(lambda n: f'Social App {n}')
    provider = factory.Iterator(['google', 'facebook', 'apple'])
    client_id = fuzzy.FuzzyText(length=32)
    secret = fuzzy.FuzzyText(length=32)
    key = fuzzy.FuzzyText(length=32)

    class Meta:
        model = SocialApp
        django_get_or_create = ('provider',)
