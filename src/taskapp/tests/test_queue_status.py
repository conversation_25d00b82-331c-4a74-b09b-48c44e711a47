from unittest.mock import (
    Mock,
    patch,
)

import pytest

from taskapp.queue_status import (
    CeleryQueueStatus,
    QueueStatusAbstract,
)


class TestQueueStatusAbstract:
    test_class = QueueStatusAbstract
    tasks = [['task1', 'name1'], ['task2', 'name2'], ['task1', 'name1']]
    list_name = 'queue_list'

    @pytest.fixture()
    def redis_conn_mock(self):
        redis_conn = Mock()
        redis_conn.llen.return_value = len(self.tasks)
        redis_conn.lrange.side_effect = lambda list_name, start, end: self.tasks[
            start:end
        ]
        return redis_conn

    @pytest.fixture(autouse=True)
    def setup(self):
        # override abstractmethods in order to test them
        self.test_class.__abstractmethods__ = {}

        self.test_class._get_list_name = Mock(return_value=self.list_name)
        self.test_class._get_name = Mock(side_effect=lambda x: x[1])
        self.test_class._loads = Mock(side_effect=lambda x: x)

    def test_get_tasks(self, redis_conn_mock):
        instance = self.test_class(redis_conn=redis_conn_mock)
        tasks = instance.get_tasks(0, 2)
        redis_conn_mock.lrange.assert_called_once_with(instance.list_name, 0, 2)
        assert tasks == self.tasks[0:2]

    def test_get_tasks_count(self, redis_conn_mock):
        instance = self.test_class(redis_conn=redis_conn_mock)
        tasks_count = instance.get_tasks_count()

        redis_conn_mock.llen.assert_called_once_with(self.list_name)
        assert tasks_count == len(self.tasks)

    def test_get_tasks_names(self, redis_conn_mock):
        instance = self.test_class(redis_conn=redis_conn_mock)

        tasks_names = instance.get_tasks_names(0, 2)

        assert tasks_names == [self.tasks[0][1], self.tasks[1][1]]

    def test_get_most_common_tasks(self, redis_conn_mock):
        instance = self.test_class(redis_conn=redis_conn_mock)
        amount_of_tasks = 10
        most_common_tasks = instance.get_most_common_tasks(n=amount_of_tasks)
        assert most_common_tasks == [('name1', 2), ('name2', 1)]

    def test_get_head_tasks(self, redis_conn_mock):
        instance = self.test_class(redis_conn=redis_conn_mock)

        requested_amount = 2
        tasks_amount = len(self.tasks)
        expected = [task[1] for task in self.tasks[-requested_amount:tasks_amount]]
        expected.reverse()

        head_tasks = instance.get_head_tasks_names(requested_amount)

        assert head_tasks == expected

    def test_get_tail_tasks(self, redis_conn_mock):
        instance = self.test_class(redis_conn=redis_conn_mock)
        tasks_amount = 2

        tail_tasks = instance.get_tail_tasks_names(tasks_amount)

        expected = [task[1] for task in self.tasks[0:tasks_amount]]
        assert tail_tasks == expected


class TestCeleryQueueStatus:
    test_class = CeleryQueueStatus
    task = {'headers': {'task': 'task_name'}}

    @patch('taskapp.queue_status.json.loads')
    def test_loads(self, json_loads_mock):
        instance = self.test_class(redis_conn=Mock())
        instance._loads(self.task)
        json_loads_mock.assert_called_once_with(self.task)

    def test_get_name(self):
        instance = self.test_class(redis_conn=Mock())
        task = instance._get_name(self.task)
        assert task == self.task['headers']['task']
