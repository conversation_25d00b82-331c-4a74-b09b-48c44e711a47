from io import TextIOWrapper

from django.contrib.auth.mixins import PermissionRequiredMixin
from django.views.generic import FormView

from custom.exceptions import CsvImportError
from custom.forms import CsvFileForm
from user_profile.services.braze_csv_enricher import BrazeCSVEnricher


class BrazeEnricherAdminFormView(PermissionRequiredMixin, FormView):
    form_class = CsvFileForm
    permission_required = 'user_profile.view_userprofile'
    template_name = 'admin/braze_csv_enricher.html'

    def post(self, request, *args, **kwargs):
        form = self.form_class(request.POST, request.FILES)
        if not form.is_valid():
            return self.form_invalid(form)
        old_file = TextIOWrapper(form.files['csv_file'].file, encoding='utf-8')
        try:
            response = BrazeCSVEnricher(old_file).write_to_response()
        except CsvImportError as e:
            form.add_error('csv_file', e)
            return self.form_invalid(form)
        return response
