from django.db import models
from django.db.models import IntegerChoices
from django.utils.translation import gettext_lazy as _


class Gender(IntegerChoices):
    NONE = 0, _('none')
    MALE = 1, _('male')
    FEMALE = 2, _('female')


class UserType(IntegerChoices):
    STAFF = 0, 'staff'
    PRODUCER = 1, 'producer'
    CUSTOMER = 2, 'customer'
    GUEST_CUSTOMER = 3, 'guest'
    CUSTOMER_SERVICE = 4, 'customer service'
    EXTERNAL_COOPERATOR = 5, 'external cooperator'
    RECOVERY_PRODUCER = 6, 'recovery_producer'
    WAREHOUSE_OPERATOR = 7, 'warehouse operator'

    @classmethod
    def get_signed_in_types(cls) -> frozenset['UserType']:
        return frozenset(
            {
                cls.STAFF,
                cls.CUSTOMER,
                cls.CUSTOMER_SERVICE,
            }
        )


class RegistrationSource(IntegerChoices):
    WEB = 0, 'web'
    IPHONE = 1, 'iphone'
    IPAD = 2, 'ipad'
    POPUP_REGISTRATION = 3, 'popup registration'
    FACEBOOK = 4, 'facebook'
    API = 5, 'api'
    ANDROID = 6, 'android'
    ANDROID_TABLET = 7, 'android tablet'
    CUSTOMER_SERVICE = 98, 'customer service'
    UNKNOWN = 99, 'unknown'


class SubscriptionSources(models.TextChoices):
    APP = 'app'
    CONTENT = 'content'
    CONTENT_SAMPLE = 'content_sample'
    REGISTRATION = 'registration'
    OFFER = 'offer'
    EXIT_POPUP = 'exit popup'
    SAVED_ITEM = 'saved item'
    NEWSLETTER_NO = 'no opt-in'
    LP = 'newsletter_lp'
    SMS = 'sms_lp'
    CRM = 'crm_lp'
    PARTNER_PROGRAM = 'partner_program'
    NOPROMO_LP = 'nopromo_lp'
    CONTACT = 'contact'
    CHECKOUT = 'checkout'
    NEWSLETTER_FOR_CUSTOMERS = 'sm25_lp'
    SHOWROOM = 'showroom'
    LP_SOFA_TEASER = 'lp_sofa_teaser'
