# Generated by Django 4.1.9 on 2024-01-19 15:39

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('user_profile', '0023_alter_userprofile_user_type'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='userprofile',
            name='invoice_phone',
        ),
        migrations.RemoveField(
            model_name='userprofile',
            name='invoice_phone_prefix',
        ),
        migrations.RemoveField(
            model_name='userprofile',
            name='latitude',
        ),
        migrations.RemoveField(
            model_name='userprofile',
            name='longitude',
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='invoice_city',
            field=models.CharField(
                blank=True, max_length=256, null=True, verbose_name='invoice city'
            ),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='invoice_company_name',
            field=models.CharField(
                blank=True,
                max_length=256,
                null=True,
                verbose_name='invoice company name',
            ),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='invoice_country',
            field=models.CharField(
                blank=True, max_length=50, null=True, verbose_name='invoice country'
            ),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='invoice_country_area',
            field=models.CharField(
                blank=True,
                max_length=128,
                null=True,
                verbose_name='invoice country administrative area',
            ),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='invoice_postal_code',
            field=models.CharField(
                blank=True, max_length=20, null=True, verbose_name='invoice postal code'
            ),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='invoice_street_address_1',
            field=models.CharField(
                blank=True,
                max_length=256,
                null=True,
                verbose_name='invoice street address 1',
            ),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='invoice_street_address_2',
            field=models.CharField(
                blank=True,
                max_length=256,
                null=True,
                verbose_name='invoice street address 2',
            ),
        ),
    ]
