from typing import TypedDict

from django.contrib.auth import get_user_model
from django.db import models
from django.db.models import Field

from carts.services.cart_service import CartService
from regions.change_region import change_region
from user_profile.models import UserProfile

User = get_user_model()


class MergedCart(TypedDict):
    new_item_ids: list[int]
    old_item_ids: list[int]


class UserProfileTransferService:
    def __init__(self, source: User | UserProfile, target: User | UserProfile) -> None:
        if isinstance(source, User):
            self.source_user = source
            self.source_profile = source.profile
        else:
            self.source_user = source.user
            self.source_profile = source

        if isinstance(target, User):
            self.target_user = target
            self.target_profile = target.profile
        else:
            self.target_user = target.user
            self.target_profile = target

    def transfer(self, with_profile_data: bool = False) -> None:
        if with_profile_data:
            self.transfer_profile_data()

        self.transfer_orders()
        self.transfer_cart()

        # TODO: should region be also transferred?
        if self.source_profile.region is not None:
            change_region(self.target_profile, self.source_profile.region)

        self.source_profile.clear_library_item_number_cache()
        self.target_profile.clear_library_item_number_cache()

    def transfer_profile_data(self) -> None:
        for field in UserProfile._meta.get_fields():
            if self._should_not_transfer(field):
                continue

            source_value = getattr(self.source_profile, field.name)
            setattr(self.target_profile, field.name, source_value)
            self._reset_source_profile(field)

        self.target_profile.save()
        self.source_profile.save()

        for class_name in ('jetty', 'watty', 'sotty'):
            user_furniture_set = getattr(self.source_user, f'{class_name}_set')
            user_furniture_set.update(owner_id=self.target_user.id)

    def transfer_orders(self) -> None:
        self.source_user.order_set.update(owner_id=self.target_user.id)

    def transfer_cart(self) -> MergedCart:
        if not self.target_user.carts.exists():
            active_cart = CartService.get_cart(self.source_user)
            if not active_cart:
                return {'new_item_ids': [], 'old_item_ids': []}
            new_item_ids = list(active_cart.items.values_list('id', flat=True))
            active_cart.owner = self.target_user
            if active_cart.order:
                active_cart.order.owner = self.target_user
                active_cart.order.save(update_fields=['owner'])
            active_cart.save(update_fields=['owner'])
            return {'new_item_ids': new_item_ids, 'old_item_ids': []}

        target_user_cart = CartService.get_or_create_cart(self.target_user)
        old_item_ids = list(target_user_cart.items.values_list('id', flat=True))
        source_user_cart = CartService.get_cart(self.source_user)
        if source_user_cart:
            new_item_ids = list(source_user_cart.items.values_list('id', flat=True))
            source_user_cart.items.update(cart=target_user_cart)
            source_user_cart.delete()
            target_user_cart.refresh_from_db()
            CartService.recalculate_cart(target_user_cart)
        else:
            new_item_ids = []

        return {'new_item_ids': new_item_ids, 'old_item_ids': old_item_ids}

    @staticmethod
    def _should_not_transfer(field: Field) -> bool:
        return (
            field.auto_created
            or field.related_model
            or field.name.startswith('registration_')
            or (
                isinstance(field, models.DateField)
                and (field.auto_now or field.auto_now_add)
            )
        )

    def _reset_source_profile(self, field: Field) -> None:
        if field.null:
            setattr(self.source_profile, field.name, None)
        elif callable(field.default):
            setattr(self.source_profile, field.name, field.default())
        else:
            setattr(self.source_profile, field.name, field.default)
