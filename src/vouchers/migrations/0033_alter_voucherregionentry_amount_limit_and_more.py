# Generated by Django 4.1.9 on 2023-11-22 17:15

from decimal import Decimal

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('vouchers', '0032_add_new_sample_box_variants_to_item_discount'),
    ]

    operations = [
        migrations.AlterField(
            model_name='voucherregionentry',
            name='amount_limit',
            field=models.DecimalField(
                decimal_places=0, default=Decimal('1000000'), max_digits=10
            ),
        ),
        migrations.AlterField(
            model_name='voucherregionentry',
            name='amount_starts',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=8),
        ),
    ]
