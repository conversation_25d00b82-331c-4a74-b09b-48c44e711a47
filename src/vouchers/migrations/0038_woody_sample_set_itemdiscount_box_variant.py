# Generated by Django 4.1.9 on 2024-04-08 10:56

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('vouchers', '0037_alter_itemdiscount_material'),
    ]

    operations = [
        migrations.AlterField(
            model_name='itemdiscount',
            name='box_variant',
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[
                    (1, 'TYPE01 CLASSIC'),
                    (2, 'TYPE01 NEW'),
                    (3, 'TYPE02'),
                    (4, 'TYPE01 WITHOUT VENEER'),
                    (5, 'TYPE02 WITHOUT VENEER'),
                    (6, 'BOLD COLORS'),
                    (7, 'TYPE02 WITHOUT VENEER AND BLACK'),
                    (8, 'TYPE02 MATTE BLACK'),
                    (101, 'TYPE01 CLASSIC VARIANT 1'),
                    (102, 'TYPE01 CLASSIC VARIANT 2'),
                    (103, 'BOLD COLORS VARIANT 1'),
                    (104, 'TYPE02 VARIANT 1'),
                    (105, 'TYPE02 MIX SET'),
                    (106, 'TYPE02 TRENDSETTER SET'),
                    (111, 'ORIGINAL STATEMENT SET'),
                    (112, 'ORIGINAL GREY SET'),
                    (113, 'ORIGINAL CLASSIC SET'),
                    (114, 'ORIGINAL WOODY SET'),
                    (1001, 'TYPE03 WHITE'),
                    (1002, 'TYPE03 CASHMERE'),
                    (1003, 'TYPE03 BEIGE AND ANTIQUE PINK'),
                    (1004, 'TYPE03 GRAPHITE GREY'),
                    (1005, 'TYPE03 WHITE AND ANTIQUE PINK'),
                    (1006, 'TYPE03 GRAPHITE GREY AND ANTIQUE PINK'),
                    (1007, 'TYPE03 TONE WHITE SET'),
                    (1008, 'TYPE03 TONE GRAPHITE SET'),
                    (1009, 'TYPE03 TONE CASHMERE SET'),
                    (1101, 'TYPE13 MIX SET'),
                    (1102, 'TYPE13 MONOCHROME SET'),
                    (1103, 'TYPE13 EDGE EARTH TONE SET'),
                    (99, 'CUSTOM'),
                ],
                null=True,
            ),
        ),
    ]
