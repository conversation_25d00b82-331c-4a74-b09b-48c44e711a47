# Generated by Django 4.1.13 on 2025-01-21 16:35

from django.db import (
    migrations,
    models,
)


def create_initial_voucher_settings(apps, schema_editor):
    VoucherSettings = apps.get_model('vouchers', 'VoucherSettings')
    VoucherSettings.objects.create(_b2b_voucher_percentage_value=42)


class Migration(migrations.Migration):

    dependencies = [
        ('vouchers', '0049_alter_itemdiscount_material'),
    ]

    operations = [
        migrations.CreateModel(
            name='VoucherSettings',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    '_b2b_voucher_percentage_value',
                    models.DecimalField(decimal_places=0, max_digits=2),
                ),
            ],
            options={
                'verbose_name': 'Voucher Settings',
                'verbose_name_plural': 'Voucher Settings',
            },
        ),
        migrations.RunPython(
            create_initial_voucher_settings, reverse_code=migrations.RunPython.noop
        ),
    ]
