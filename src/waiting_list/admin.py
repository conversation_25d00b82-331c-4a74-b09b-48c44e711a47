from django import forms
from django.contrib import admin
from django.core.exceptions import ValidationError

from custom.admin_mixins import ViewOnlyAdminMixin
from mailing.flow_schedulers import schedule_no_limit_sale_flow
from waiting_list.models import (
    WaitingListEntry,
    WaitingListSetup,
)


class WaitingListSetupAdminForm(forms.ModelForm):
    def clean_enabled(self):
        is_enabled = self.cleaned_data['enabled']

        if is_enabled == WaitingListSetup.is_sales_enabled():
            status = 'enabled' if is_enabled else 'disabled'
            raise ValidationError(f'Sales is already {status}.')

        return is_enabled

    class Meta:
        model = WaitingListSetup
        fields = ['enabled']


class WaitingListSetupAdmin(ViewOnlyAdminMixin, admin.ModelAdmin):
    list_display = ['__str__', 'creator', 'enabled', 'created_at']
    form = WaitingListSetupAdminForm

    def save_model(self, request, obj, form, change):
        obj.creator = request.user
        schedule_no_limit_sale_flow()
        super().save_model(request, obj, form, change)


# Should it really be view only here? And withouth adding? What about changes for CS?
# also mailing flow status should be more meaningful or removed here
# non editable remove links, hard to navigate
# without change permissions - no action available
class WaitingListEntryAdmin(admin.ModelAdmin):
    list_display = [
        '__str__',
        'status',
        'desired_time',
        'email_address',
        'token_sent_at',
        'token_expire_at',
        'watty_id',
        'watty_material',
        'watty_front_area',
    ]
    list_filter = (
        'status',
        'watty__material',
        'desired_time',
        'token_sent_at',
        'first_entry_at',
    )
    readonly_fields = ('token', 'created_at')
    raw_id_fields = ('owner', 'watty', 'orders')

    # Cannot add new WaitingListEntry
    def has_add_permission(self, request, obj=None):
        return False


admin.site.register(WaitingListSetup, WaitingListSetupAdmin)
admin.site.register(WaitingListEntry, WaitingListEntryAdmin)
