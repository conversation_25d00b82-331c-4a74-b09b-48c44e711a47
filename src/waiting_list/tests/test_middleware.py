import uuid

from datetime import timedelta

from django.utils import timezone

import pytest

from freezegun import freeze_time
from rest_framework import status

DATETIME_NOW = timezone.now()


@pytest.mark.django_db
class TestWaitingListTokenMiddleware:
    def test_process_request_raises_for_invalid_token(self, client):

        with pytest.raises(ValueError):
            client.get('/en/', {'wltoken': 'invalid'})

    def test_process_request_returns_waiting_list_expire_date_for_unknown_token(
        self,
        client,
    ):
        response = client.get('/en/', {'wltoken': uuid.uuid4()})

        assert response.status_code == status.HTTP_200_OK
        assert client.session['waiting_list_expired'] is True
        assert 'waiting_list_expire_date' not in client.session

    @freeze_time(DATETIME_NOW)
    def test_process_request_sets_waiting_list_expire_date_cookie_for_valid_token(
        self,
        client,
        waiting_list_entry_factory,
        user,
    ):
        token = uuid.uuid4()
        token_expire_at = DATETIME_NOW + timedelta(days=14)
        timestamp = int(token_expire_at.timestamp())
        entry = waiting_list_entry_factory(
            token=token,
            token_expire_at=token_expire_at,
            owner=user,
        )
        client.force_login(user)
        response = client.get('/en/', {'wltoken': token})

        entry.refresh_from_db()
        assert response.status_code == status.HTTP_200_OK
        assert entry.first_entry_at == DATETIME_NOW
        assert int(client.cookies['waiting_list_expire_date'].value) == timestamp
