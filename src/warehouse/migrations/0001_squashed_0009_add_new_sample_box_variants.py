# Generated by Django 4.1.9 on 2023-10-27 16:35

import django.core.files.storage
import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)

import custom.models.fields


class Migration(migrations.Migration):

    replaces = [
        ('warehouse', '0001_initial'),
        ('warehouse', '0002_stock_for_sample_boxes'),
        ('warehouse', '0003_change_unique_for_t03'),
        ('warehouse', '0004_new_upload_to_dir'),
        ('warehouse', '0005_add_display_name_and_is_active_fields'),
        ('warehouse', '0006_alter_warehouse_file'),
        ('warehouse', '0007_add_sample_box_variant_model'),
        ('warehouse', '0008_alter_sampleboxvariant_variant_type'),
        ('warehouse', '0009_add_new_sample_box_variants'),
    ]

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('producers', '0001_squashed_0039_product_has_plus_feature'),
    ]

    operations = [
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'file',
                    custom.models.fields.FileFieldWithHash(
                        storage=django.core.files.storage.FileSystemStorage(),
                        upload_to='warehouse/warehouse/%Y/%m',
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'deleted_at',
                    models.DateTimeField(db_index=True, default=None, null=True),
                ),
                (
                    'owner',
                    models.ForeignKey(
                        help_text='user that created record',
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='StoredMaterial',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('codename', models.CharField(max_length=255)),
                ('manufacturer_code', models.CharField(max_length=255)),
                ('quantity', models.DecimalField(decimal_places=4, max_digits=12)),
                ('description', models.CharField(max_length=1000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'deleted_at',
                    models.DateTimeField(db_index=True, default=None, null=True),
                ),
                (
                    'manufacturer',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='producers.manufactor',
                    ),
                ),
                (
                    'warehouse',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='warehouse.warehouse',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='SampleBoxElement',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'element_type',
                    models.PositiveIntegerField(
                        choices=[(1, 'Material'), (2, 'Sticker'), (10, 'Others')]
                    ),
                ),
                ('element_name', models.CharField(blank=True, max_length=255)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ('element_name', 'element_type'),
                'unique_together': {('element_type', 'element_name', 'is_active')},
            },
        ),
        migrations.CreateModel(
            name='SampleBoxElementsDelivery',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('description', models.CharField(max_length=1000)),
                ('additional_info', models.TextField(blank=True, default='')),
                ('is_booked', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('booked_at', models.DateTimeField(default=None, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Sample Box Elements Delivery',
            },
        ),
        migrations.CreateModel(
            name='SampleBoxElementsDeliveryPosition',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'quantity',
                    models.IntegerField(
                        help_text='its possible to have negative delivery (damage etc)'
                    ),
                ),
                (
                    'price_net',
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    'price_gross',
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    'delivery',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='position',
                        to='warehouse.sampleboxelementsdelivery',
                    ),
                ),
                (
                    'element_type',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='warehouse.sampleboxelement',
                    ),
                ),
            ],
            options={
                'ordering': ('element_type',),
            },
        ),
        migrations.CreateModel(
            name='StockSampleBox',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('quantity', models.IntegerField(default=0)),
                (
                    'stock_type',
                    models.IntegerField(choices=[(1, 'In_stock'), (2, 'To_send')]),
                ),
                ('last_update', models.DateTimeField(auto_now=True)),
                (
                    'type_resource',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='warehouse.sampleboxelement',
                    ),
                ),
            ],
            options={
                'ordering': ('stock_type', 'type_resource'),
                'unique_together': {('stock_type', 'type_resource')},
            },
        ),
        migrations.CreateModel(
            name='StockSampleBoxEventLog',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('value', models.IntegerField()),
                ('source', models.CharField(max_length=150)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                (
                    'stock',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='warehouse.stocksamplebox',
                    ),
                ),
                (
                    'user',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                'verbose_name_plural': 'Logs for Sample Box Stock',
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='SampleBoxVariant',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(blank=True, default='', max_length=60)),
                (
                    'variant_type',
                    models.PositiveIntegerField(
                        choices=[
                            (1, 'TYPE01 CLASSIC'),
                            (2, 'TYPE01 NEW'),
                            (3, 'TYPE02'),
                            (4, 'TYPE01 WITHOUT VENEER'),
                            (5, 'TYPE02 WITHOUT VENEER'),
                            (6, 'BOLD COLORS'),
                            (7, 'TYPE02 WITHOUT VENEER AND BLACK'),
                            (8, 'TYPE02 MATTE BLACK'),
                            (101, 'TYPE01 CLASSIC VARIANT 1'),
                            (102, 'TYPE01 CLASSIC VARIANT 2'),
                            (103, 'BOLD COLORS VARIANT 1'),
                            (104, 'TYPE02 VARIANT 1'),
                            (105, 'TYPE02 MIX SET'),
                            (106, 'TYPE02 TRENDSETTER SET'),
                            (111, 'ORIGINAL STATEMENT SET'),
                            (112, 'ORIGINAL GREY SET'),
                            (113, 'ORIGINAL CLASSIC SET'),
                            (1001, 'TYPE03 WHITE'),
                            (1002, 'TYPE03 CASHMERE'),
                            (1003, 'TYPE03 BEIGE AND ANTIQUE PINK'),
                            (1004, 'TYPE03 GRAPHITE GREY'),
                            (1005, 'TYPE03 WHITE AND ANTIQUE PINK'),
                            (1006, 'TYPE03 GRAPHITE GREY AND ANTIQUE PINK'),
                            (1007, 'TYPE03 TONE WHITE SET'),
                            (1008, 'TYPE03 TONE GRAPHITE SET'),
                            (1009, 'TYPE03 TONE CASHMERE SET'),
                            (1101, 'TYPE13 MIX SET'),
                            (1102, 'TYPE13 MONOCHROME SET'),
                            (1103, 'TYPE13 EDGE EARTH TONE SET'),
                            (99, 'CUSTOM'),
                        ],
                        unique=True,
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name='sampleboxelement',
            name='box_variant',
            field=models.ManyToManyField(blank=True, to='warehouse.sampleboxvariant'),
        ),
    ]
