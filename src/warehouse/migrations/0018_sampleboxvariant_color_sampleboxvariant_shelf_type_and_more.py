# Generated by Django 4.1.13 on 2025-02-25 13:52

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0017_sampleboxelement_cost_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='sampleboxvariant',
            name='color',
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[
                    (
                        0,
                        'T01 White / T02 White / T03 Exterior White / T01 Veneer Ash / '
                        'T13 White / T13 Veneer Light / Tone Expressions Off White / '
                        'Sofa Rewool2 Brown',
                    ),
                    (
                        1,
                        'T01 Black / T02 Terracotta / T03 Exterior Beige / '
                        'T01 Veneer Oak / T13 Veneer Dark / '
                        'Tone Expressions Oyster Beige / Sofa Rewool2 Olive Green',
                    ),
                    (
                        3,
                        'T01 Grey / T02 Sand / T03 White / T13 Gray / '
                        'Tone Expressions Inky Black / Sofa Rewool2 Butter Yellow',
                    ),
                    (
                        6,
                        'T01 Red / T02 Matte Black / T03 Pink / T13 Gray Plywood / '
                        'Sofa Rewool2 Baby Blue',
                    ),
                    (
                        7,
                        'T01 Yellow / T02 Sky Blue / T03 Stone Gray / '
                        'Sofa Corduroy Ecru',
                    ),
                    (
                        8,
                        'T01 Dusty Pink / T02 Burgundy / T03 Sage Green / '
                        'T13 Clay Brown / Sofa Corduroy Rock',
                    ),
                    (
                        9,
                        'T01 Blue / T02 Cotton / T03 Misty Blue / T13 Olive Green / '
                        'Sofa Corduroy Dark Brown',
                    ),
                    (
                        2,
                        'T02 Midnight Blue / T03 Exterior Graphite / '
                        'T01 Veneer Dark Oak / Tone Expressions Pistachio Green / '
                        'Sofa Rewool2 Light Gray',
                    ),
                    (10, 'T02 Gray / T13 Beige / Sofa Corduroy Steel'),
                    (11, 'T02 Dark Gray / T13 Black / Sofa Corduroy Tobacco'),
                    (15, 'T02 Reisingers Pink'),
                    (16, 'T02 Sage Green'),
                    (17, 'T02 Stone Gray'),
                    (
                        4,
                        'T03 Beige / Tone Expressions Powder Pink / '
                        'Sofa Rewool2 Shadow Pink',
                    ),
                    (5, 'T03 Graphite / T13 White Plywood / Sofa Rewool2 Green'),
                    (12, 'Sofa Corduroy Pink'),
                    (13, 'Sofa Corduroy Camouflage'),
                    (14, 'Sofa Corduroy Blue Klein'),
                ],
                null=True,
            ),
        ),
        migrations.AddField(
            model_name='sampleboxvariant',
            name='shelf_type',
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[
                    (0, 'TYPE01'),
                    (1, 'TYPE02'),
                    (2, 'VENEER TYPE01'),
                    (3, 'TYPE03'),
                    (4, 'TYPE13'),
                    (5, 'VENEER TYPE13'),
                    (6, 'TYPE23'),
                    (7, 'TYPE24'),
                    (8, 'TYPE25'),
                    (10, 'SOFA TYPE01'),
                ],
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='sampleboxvariant',
            name='variant_type',
            field=models.PositiveIntegerField(
                choices=[
                    (20000, 'TYPE01 WHITE'),
                    (20001, 'TYPE01 BLACK'),
                    (20003, 'TYPE01 GREY'),
                    (20006, 'TYPE01 RED'),
                    (20007, 'TYPE01 YELLOW'),
                    (20008, 'TYPE01 DUSTY_PINK'),
                    (20009, 'TYPE01 BLUE'),
                    (20100, 'TYPE02 WHITE'),
                    (20101, 'TYPE02 TERRACOTTA'),
                    (20102, 'TYPE02 MIDNIGHT_BLUE'),
                    (20103, 'TYPE02 SAND'),
                    (20106, 'TYPE02 MATTE_BLACK'),
                    (20107, 'TYPE02 SKY_BLUE'),
                    (20108, 'TYPE02 BURGUNDY'),
                    (20109, 'TYPE02 COTTON'),
                    (20110, 'TYPE02 GRAY'),
                    (20111, 'TYPE02 DARK_GRAY'),
                    (20115, 'TYPE02 REISINGERS_PINK'),
                    (20116, 'TYPE02 SAGE_GREEN'),
                    (20117, 'TYPE02 STONE_GRAY'),
                    (20200, 'VENEER_TYPE01 ASH'),
                    (20201, 'VENEER_TYPE01 OAK'),
                    (20202, 'VENEER_TYPE01 DARK_OAK'),
                    (20300, 'TYPE03 EXTERIOR_WHITE'),
                    (20301, 'TYPE03 EXTERIOR_BEIGE'),
                    (20302, 'TYPE03 EXTERIOR_GRAPHITE'),
                    (20303, 'TYPE03 WHITE'),
                    (20304, 'TYPE03 BEIGE'),
                    (20305, 'TYPE03 GRAPHITE'),
                    (20306, 'TYPE03 PINK'),
                    (20307, 'TYPE03 STONE_GRAY'),
                    (20308, 'TYPE03 SAGE_GREEN'),
                    (20309, 'TYPE03 MISTY_BLUE'),
                    (20400, 'TYPE13 WHITE'),
                    (20403, 'TYPE13 GRAY'),
                    (20405, 'TYPE13 WHITE_PLYWOOD'),
                    (20406, 'TYPE13 GRAY_PLYWOOD'),
                    (20408, 'TYPE13 CLAY_BROWN'),
                    (20409, 'TYPE13 OLIVE_GREEN'),
                    (20410, 'TYPE13 BEIGE'),
                    (20411, 'TYPE13 BLACK'),
                    (20500, 'VENEER_TYPE13 LIGHT'),
                    (20501, 'VENEER_TYPE13 DARK'),
                    (20600, 'TYPE23 OFF_WHITE'),
                    (20601, 'TYPE23 OYSTER_BEIGE'),
                    (20602, 'TYPE23 PISTACHIO_GREEN'),
                    (20603, 'TYPE23 INKY_BLACK'),
                    (20604, 'TYPE23 POWDER_PINK'),
                    (21000, 'SOFA_TYPE01 REWOOL2_BROWN'),
                    (21001, 'SOFA_TYPE01 REWOOL2_OLIVE_GREEN'),
                    (21002, 'SOFA_TYPE01 REWOOL2_LIGHT_GRAY'),
                    (21003, 'SOFA_TYPE01 REWOOL2_BUTTER_YELLOW'),
                    (21004, 'SOFA_TYPE01 REWOOL2_SHADOW_PINK'),
                    (21005, 'SOFA_TYPE01 REWOOL2_GREEN'),
                    (21006, 'SOFA_TYPE01 REWOOL2_BABY_BLUE'),
                    (21007, 'SOFA_TYPE01 CORDUROY_ECRU'),
                    (21008, 'SOFA_TYPE01 CORDUROY_ROCK'),
                    (21009, 'SOFA_TYPE01 CORDUROY_DARK_BROWN'),
                    (21010, 'SOFA_TYPE01 CORDUROY_STEEL'),
                    (21011, 'SOFA_TYPE01 CORDUROY_TOBACCO'),
                    (21012, 'SOFA_TYPE01 CORDUROY_PINK'),
                    (21013, 'SOFA_TYPE01 CORDUROY_CAMOUFLAGE'),
                    (21014, 'SOFA_TYPE01 CORDUROY_BLUE_KLEIN'),
                    (1, 'TYPE01 CLASSIC'),
                    (2, 'TYPE01 NEW'),
                    (3, 'TYPE02'),
                    (4, 'TYPE01 WITHOUT VENEER'),
                    (5, 'TYPE02 WITHOUT VENEER'),
                    (6, 'BOLD COLORS'),
                    (7, 'TYPE02 WITHOUT VENEER AND BLACK'),
                    (8, 'TYPE02 MATTE BLACK'),
                    (101, 'TYPE01 CLASSIC VARIANT 1'),
                    (102, 'TYPE01 CLASSIC VARIANT 2'),
                    (103, 'BOLD COLORS VARIANT 1'),
                    (104, 'TYPE02 VARIANT 1'),
                    (105, 'TYPE02 MIX SET'),
                    (106, 'TYPE02 TRENDSETTER SET'),
                    (111, 'ORIGINAL STATEMENT SET'),
                    (112, 'ORIGINAL GREY SET'),
                    (113, 'ORIGINAL CLASSIC SET'),
                    (114, 'ORIGINAL WOODY SET'),
                    (1001, 'TYPE03 WHITE'),
                    (1002, 'TYPE03 CASHMERE'),
                    (1003, 'TYPE03 BEIGE AND ANTIQUE PINK'),
                    (1004, 'TYPE03 GRAPHITE GREY'),
                    (1005, 'TYPE03 WHITE AND ANTIQUE PINK'),
                    (1006, 'TYPE03 GRAPHITE GREY AND ANTIQUE PINK'),
                    (1007, 'TYPE03 TONE WHITE SET'),
                    (1008, 'TYPE03 TONE GRAPHITE SET'),
                    (1009, 'TYPE03 TONE CASHMERE SET'),
                    (1010, 'TYPE23 TONE SUBTLE SHADES SET'),
                    (1101, 'TYPE13 MIX SET'),
                    (1102, 'TYPE13 MONOCHROME SET'),
                    (1103, 'TYPE13 EDGE EARTH TONE SET'),
                    (1201, 'TYPE13 MIXED WARM SET'),
                    (99, 'CUSTOM'),
                ],
                unique=True,
            ),
        ),
    ]
